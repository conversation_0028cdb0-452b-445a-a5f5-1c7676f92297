# 异步预加载实现方案

## 概述

针对您提出的"需要调用接口，是异步的"问题，我们重新设计了一个异步预加载方案。这个方案既能保证数据在页面加载前准备好，又能处理异步接口调用，并具备降级机制。

## 架构设计

### 1. AppBootstrap 组件

`AppBootstrap` 是应用启动器，负责在渲染主应用之前完成所有必需数据的异步初始化。

```javascript
// src/AppBootstrap.js
const AppBootstrap = ({ children, onDataReady }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    const initializeAppData = async () => {
      try {
        // 1. 异步获取部门数据（API + 降级）
        let departments = [];
        try {
          const response = await departmentAPI.getDepartments();
          departments = response.data;
        } catch (apiError) {
          // 降级到配置文件
          departments = Object.values(DEPARTMENT_CONFIG);
        }
        
        // 2. 初始化其他数据...
        
        // 3. 通知数据准备完成
        onDataReady(initializedData);
        setLoading(false);
      } catch (error) {
        setError(error.message);
      }
    };
    
    initializeAppData();
  }, []);
  
  // 显示加载状态或错误状态
  if (loading) return <LoadingScreen />;
  if (error) return <ErrorScreen />;
  
  // 数据准备完成，渲染子组件
  return children;
};
```

### 2. 支持预加载的 Store

修改 `StoreProvider` 来接收预加载的数据：

```javascript
// src/store/index.js
const createInitialState = (preloadedData = null) => {
  const baseData = preloadedData || createEmptyBaseData();
  
  return {
    departments: baseData.departments,
    departmentMap: baseData.departmentMap,
    roles: baseData.roles,
    roleMap: baseData.roleMap,
    permissions: baseData.permissions,
    permissionMap: baseData.permissionMap,
    // ... 其他状态
  };
};

export const StoreProvider = ({ children, preloadedData = null }) => {
  const [state, dispatch] = useReducer(storeReducer, createInitialState(preloadedData));
  // ...
};
```

### 3. 应用入口整合

在 `App.js` 中整合 AppBootstrap 和 StoreProvider：

```javascript
function App(props) {
  const [preloadedData, setPreloadedData] = useState(null);

  const handleDataReady = (data) => {
    setPreloadedData(data);
  };

  return (
    <ConfigProvider locale={antdLocale}>
      <AppBootstrap onDataReady={handleDataReady}>
        <StoreProvider preloadedData={preloadedData}>
          <Router>
            <div className="App">
              <H5AppQS config={props.config} />
            </div>
          </Router>
        </StoreProvider>
      </AppBootstrap>
    </ConfigProvider>
  );
}
```

## 数据流程

### 1. 应用启动流程

```
1. App 组件渲染
   ↓
2. AppBootstrap 开始异步初始化
   ↓
3. 显示加载屏幕
   ↓
4. 异步获取数据（API + 降级）
   ↓
5. 数据准备完成，调用 onDataReady
   ↓
6. StoreProvider 接收预加载数据
   ↓
7. 主应用渲染，数据立即可用
```

### 2. 数据获取策略

```
部门数据获取:
API 调用 → 成功 → 使用 API 数据
    ↓
   失败 → 降级到配置文件数据

角色/权限数据:
直接使用预定义数据（无需 API 调用）
```

## 关键特性

### 1. 异步支持
- ✅ 支持异步 API 调用
- ✅ 在应用启动时完成数据获取
- ✅ 用户看到加载状态，体验友好

### 2. 降级机制
- ✅ API 失败时自动降级到配置文件
- ✅ 确保应用始终能够启动
- ✅ 提供错误处理和重试机制

### 3. 性能优化
- ✅ 数据预加载，组件无需等待
- ✅ 创建映射表，提供快速查找
- ✅ 避免重复的 API 调用

### 4. 用户体验
- ✅ 统一的加载状态显示
- ✅ 错误状态处理和重试
- ✅ 数据准备完成后立即可用

## 使用方式

### 1. 在组件中使用

```javascript
// 数据立即可用，无需检查加载状态
const MyComponent = () => {
  const { departments, getDepartmentName } = useDepartments();
  const { roles, getRoleName } = useRoles();
  
  return (
    <div>
      <p>部门总数: {departments.length}</p>
      <p>第一个部门: {getDepartmentName(1)}</p>
    </div>
  );
};
```

### 2. 类组件中使用

```javascript
class MyComponent extends Component {
  render() {
    const { store } = this.props;
    
    return (
      <div>
        <p>部门总数: {store.departments.length}</p>
        <p>第一个部门: {store.getDepartmentName(1)}</p>
      </div>
    );
  }
}

export default withStore(MyComponent);
```

## 测试页面

我们提供了多个测试页面来验证异步预加载功能：

1. **异步预加载测试**: `/async-test` - 验证异步预加载流程
2. **快速测试**: `/quick-test` - 简单验证数据可用性
3. **详细测试**: `/sync-test` - 完整的数据展示

## 控制台输出

应用启动时的控制台输出：

```
🚀 开始应用数据初始化...
✅ 从API获取部门数据: X 个部门
✅ 角色数据初始化完成: 5 个角色
✅ 权限数据初始化完成: 7 个权限
🎉 应用数据初始化完成！
```

如果 API 失败：

```
🚀 开始应用数据初始化...
⚠️ API获取部门数据失败，使用配置文件数据: [错误信息]
✅ 使用配置文件部门数据: X 个部门
✅ 角色数据初始化完成: 5 个角色
✅ 权限数据初始化完成: 7 个权限
🎉 应用数据初始化完成！
```

## 优势对比

| 特性 | 同步初始化 | 异步预加载 | 组件内加载 |
|------|------------|------------|------------|
| 支持 API 调用 | ❌ | ✅ | ✅ |
| 页面加载前准备好 | ✅ | ✅ | ❌ |
| 降级机制 | ❌ | ✅ | 部分 |
| 用户体验 | 好 | 很好 | 一般 |
| 实现复杂度 | 低 | 中 | 高 |
| 维护性 | 好 | 很好 | 一般 |

## 扩展性

### 1. 添加新的数据源

```javascript
// 在 AppBootstrap 中添加新的数据获取
const brands = await brandAPI.getBrands();
const suppliers = await supplierAPI.getSuppliers();

const initializedData = {
  departments,
  roles,
  permissions,
  brands,      // 新增
  suppliers,   // 新增
};
```

### 2. 自定义加载策略

```javascript
// 可以根据不同环境使用不同的加载策略
const loadStrategy = process.env.NODE_ENV === 'development' 
  ? 'config-file' 
  : 'api-first';
```

### 3. 缓存机制

```javascript
// 可以添加本地存储缓存
const cachedData = localStorage.getItem('app-data');
if (cachedData && !isExpired(cachedData)) {
  return JSON.parse(cachedData);
}
```

## 总结

异步预加载方案完美解决了您提出的问题：

1. **支持异步接口调用** - 通过 AppBootstrap 在应用启动时异步获取数据
2. **数据预准备** - 确保组件渲染时数据已经可用
3. **降级机制** - API 失败时自动使用配置文件数据
4. **良好的用户体验** - 统一的加载状态和错误处理
5. **高可维护性** - 清晰的数据流和组件职责分离

这个方案既满足了异步数据获取的需求，又保证了数据在页面加载前就已经准备好，为用户提供了最佳的使用体验。
