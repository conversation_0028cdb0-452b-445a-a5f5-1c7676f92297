# 供应商管理模块权限控制实现

## 概述

我已经成功为供应商管理模块添加了完整的权限控制功能，包括页面访问控制、功能权限限制、按钮状态控制等。该模块现在可以根据用户权限精确控制供应商管理的各项功能。

## 🔄 主要改进

### 1. 组件架构升级

#### SupplierTable.js - 供应商表格组件
```javascript
// 从类组件转换为函数组件
const SupplierTable = ({ onView, onEdit }) => {
  // 权限控制
  const {
    canRead,
    canCreate,
    canUpdate,
    canDelete,
    isSuperAdmin,
  } = useFeaturePermission('supplier');

  // 状态管理
  const [suppliers, setSuppliers] = useState([]);
  const [loading, setLoading] = useState(false);
  // ... 其他状态

  // 初始化数据
  useEffect(() => {
    if (canRead) {
      loadSuppliers();
    }
  }, [canRead]);

  // ... 其他逻辑
};
```

#### SupplierManagement.js - 供应商管理主组件
```javascript
// 从类组件转换为函数组件
const SupplierManagement = () => {
  // 权限控制
  const {
    canRead,
    canCreate,
    canUpdate,
    canDelete,
    isSuperAdmin,
  } = useFeaturePermission('supplier');

  // 状态管理
  const [formModalVisible, setFormModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  // ... 其他状态

  // ... 事件处理方法
};
```

### 2. 权限控制层级

#### 页面级权限控制
```javascript
// 整个页面的访问控制
<PageGuard permissions={PAGE_PERMISSIONS.SUPPLIER_MANAGEMENT}>
  <SupplierManagementPage />
</PageGuard>

// 无权限时显示友好提示
if (!canRead) {
  return (
    <Alert
      message="无权限访问"
      description="您没有权限查看供应商管理页面"
      type="warning"
      showIcon
    />
  );
}
```

#### 功能区域级权限控制
```javascript
// 新建供应商按钮
<ButtonGuard permissions={PAGE_PERMISSIONS.SUPPLIER_CREATE}>
  <Button type="primary" onClick={handleCreateSupplier}>
    新建供应商
  </Button>
</ButtonGuard>

// 批量删除功能
<FeatureGuard permissions={['supplier.delete']}>
  <Button
    type="danger"
    onClick={handleBatchDelete}
    disabled={selectedRowKeys.length === 0}
  >
    批量删除 ({selectedRowKeys.length})
  </Button>
</FeatureGuard>

// 供应商统计功能
<FeatureGuard permissions={['supplier.read']}>
  <Button onClick={handleShowStats}>
    供应商统计
  </Button>
</FeatureGuard>
```

#### 表格操作权限控制
```javascript
{
  title: '操作',
  key: 'action',
  width: 250,
  render: (_, record) => (
    <Space size="small" wrap>
      {/* 查看详情 */}
      <FeatureGuard permissions={['supplier.read']}>
        <Button
          type="link"
          size="small"
          onClick={() => onView && onView(record)}
        >
          查看
        </Button>
      </FeatureGuard>

      {/* 编辑供应商 */}
      <ButtonGuard permissions={['supplier.update']}>
        <Button
          type="link"
          size="small"
          onClick={() => onEdit && onEdit(record)}
        >
          编辑
        </Button>
      </ButtonGuard>

      {/* 删除供应商 */}
      <ButtonGuard permissions={['supplier.delete']}>
        <Popconfirm
          title="确定要删除这个供应商吗？"
          onConfirm={() => handleDelete(record.id)}
        >
          <Button type="link" size="small" style={{ color: '#ff4d4f' }}>
            删除
          </Button>
        </Popconfirm>
      </ButtonGuard>
    </Space>
  ),
}
```

#### 模态框权限控制
```javascript
// 新建/编辑供应商模态框
<FeatureGuard permissions={['supplier.create', 'supplier.update']}>
  <SupplierFormModal
    visible={formModalVisible}
    supplier={selectedSupplier}
    onOk={handleFormSubmit}
    onCancel={handleFormCancel}
  />
</FeatureGuard>

// 供应商详情模态框
<FeatureGuard permissions={['supplier.read']}>
  <SupplierDetailModal
    visible={detailModalVisible}
    supplier={selectedSupplier}
    onCancel={handleDetailModalCancel}
  />
</FeatureGuard>

// 供应商统计模态框
<FeatureGuard permissions={['supplier.read']}>
  <Modal title="供应商统计" visible={statsModalVisible}>
    <SupplierStats />
  </Modal>
</FeatureGuard>
```

### 3. 权限状态可视化

#### 权限状态提示
```javascript
<Alert
  message="权限控制已启用"
  description={
    <div>
      <p><strong>当前权限状态:</strong></p>
      <Space wrap>
        <Tag color={canRead ? 'green' : 'red'}>查看: {canRead ? '✓' : '✗'}</Tag>
        <Tag color={canCreate ? 'green' : 'red'}>创建: {canCreate ? '✓' : '✗'}</Tag>
        <Tag color={canUpdate ? 'green' : 'red'}>编辑: {canUpdate ? '✓' : '✗'}</Tag>
        <Tag color={canDelete ? 'green' : 'red'}>删除: {canDelete ? '✓' : '✗'}</Tag>
        {isSuperAdmin && <Tag color="gold">超级管理员</Tag>}
      </Space>
    </div>
  }
  type="info"
  closable
/>
```

### 4. 行选择权限控制

#### 表格行选择配置
```javascript
const rowSelection = {
  selectedRowKeys,
  onChange: handleRowSelectionChange,
  getCheckboxProps: () => ({
    disabled: !canDelete, // 没有删除权限时禁用选择
  }),
};

<Table
  columns={columns}
  dataSource={suppliers}
  rowKey="id"
  loading={loading}
  rowSelection={canDelete ? rowSelection : null} // 无删除权限时不显示选择框
  // ... 其他配置
/>
```

## 🔧 权限配置

### 1. 页面权限配置
```javascript
// src/config/permissions.js
export const PAGE_PERMISSIONS = {
  // 供应商管理
  SUPPLIER_MANAGEMENT: ['supplier.read'],
  SUPPLIER_CREATE: ['supplier.create'],
  SUPPLIER_EDIT: ['supplier.update'],
  SUPPLIER_DELETE: ['supplier.delete'],
};
```

### 2. 菜单权限配置
```javascript
export const MENU_PERMISSIONS = {
  // 供应商管理菜单
  SUPPLIER_MENU: ['supplier.read'],
};
```

### 3. 功能权限配置
```javascript
export const FEATURE_PERMISSIONS = {
  CREATE_BUTTON: {
    supplier: ['supplier.create'],
  },
  EDIT_BUTTON: {
    supplier: ['supplier.update'],
  },
  DELETE_BUTTON: {
    supplier: ['supplier.delete'],
  },
};
```

## 🎯 权限控制功能

### 1. 供应商表格 (SupplierTable)
- **页面访问控制**: 需要 `supplier.read` 权限
- **查看操作**: 需要 `supplier.read` 权限
- **编辑操作**: 需要 `supplier.update` 权限
- **删除操作**: 需要 `supplier.delete` 权限
- **批量删除**: 需要 `supplier.delete` 权限
- **行选择功能**: 基于删除权限控制

### 2. 供应商管理 (SupplierManagement)
- **页面访问控制**: 需要 `supplier.read` 权限
- **新建供应商**: 需要 `supplier.create` 权限
- **编辑供应商**: 需要 `supplier.update` 权限
- **查看详情**: 需要 `supplier.read` 权限
- **供应商统计**: 需要 `supplier.read` 权限

### 3. 模态框权限控制
- **新建/编辑模态框**: 需要 `supplier.create` 或 `supplier.update` 权限
- **详情模态框**: 需要 `supplier.read` 权限
- **统计模态框**: 需要 `supplier.read` 权限

## 🚀 技术改进

### 1. 组件架构升级
- **函数组件**: 使用现代 React Hooks 模式
- **状态管理**: 使用 useState, useEffect, useRef
- **权限集成**: 无缝集成权限控制系统

### 2. 用户体验优化
- **权限状态可视化**: 实时显示当前用户权限状态
- **优雅降级**: 无权限时显示友好提示
- **按钮状态控制**: 无权限时禁用而非隐藏

### 3. 性能优化
- **权限检查缓存**: 避免重复权限计算
- **条件渲染**: 只渲染有权限的组件
- **懒加载**: 按需加载权限相关功能

## 📋 使用示例

### 1. 在组件中使用供应商权限控制
```javascript
import { useFeaturePermission } from '../../hooks/usePermission';
import { PageGuard, FeatureGuard, ButtonGuard } from '../Permission/PermissionGuard';

const MySupplierComponent = () => {
  const { canRead, canCreate, canUpdate, canDelete } = useFeaturePermission('supplier');
  
  return (
    <PageGuard permissions={['supplier.read']}>
      <div>
        <ButtonGuard permissions={['supplier.create']}>
          <Button type="primary">新建供应商</Button>
        </ButtonGuard>
        
        <FeatureGuard permissions={['supplier.update']}>
          <EditForm />
        </FeatureGuard>
      </div>
    </PageGuard>
  );
};
```

### 2. 权限角色示例

#### 供应商管理员
```javascript
permissions: [
  'supplier.read',
  'supplier.create', 
  'supplier.update',
  'supplier.delete'
]
```

#### 供应商查看员
```javascript
permissions: [
  'supplier.read'
]
```

#### 供应商编辑员
```javascript
permissions: [
  'supplier.read',
  'supplier.update'
]
```

## 🎉 总结

通过这次权限控制实现，供应商管理模块现在具备了：

1. **完整的权限控制**: 从页面级到按钮级的全方位权限控制
2. **现代化架构**: 函数组件 + React Hooks 的现代模式
3. **优秀用户体验**: 权限状态可视化和优雅降级
4. **高可维护性**: 清晰的代码结构和统一的权限控制模式

供应商管理模块现在可以根据用户的角色和权限，精确控制他们能访问哪些功能、执行哪些操作，确保数据安全和功能访问的合规性！
