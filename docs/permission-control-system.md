# 权限控制系统实现

## 概述

基于您提供的45个权限数据，我已经实现了一套完整的权限控制系统，包括页面访问控制、功能权限限制、按钮状态控制等。该系统可以精确控制用户能看到哪些功能、能操作哪些功能。

## 系统架构

### 1. 权限数据结构

基于API提供的权限数据结构：
```json
{
  "id": "cmc569vxb0000kr94iby8t2nm",
  "name": "user.read",
  "displayName": "查看用户",
  "description": "查看用户信息",
  "module": "user",
  "action": "read",
  "resource": "",
  "isSystem": true
}
```

### 2. 核心组件

#### usePermission Hook
```javascript
// 权限检查核心逻辑
const { hasPermission, hasAnyPermission, hasAllPermissions, isSuperAdmin } = usePermission();

// 模块权限检查
const { canRead, canCreate, canUpdate, canDelete, canApprove } = useFeaturePermission('project');
```

#### 权限守卫组件
```javascript
// 页面级权限控制
<PageGuard permissions={['project.read']}>
  <ProjectPage />
</PageGuard>

// 功能区域权限控制
<FeatureGuard permissions={['project.create']}>
  <CreateButton />
</FeatureGuard>

// 按钮权限控制
<ButtonGuard permissions={['project.delete']}>
  <DeleteButton />
</ButtonGuard>
```

## 权限控制层级

### 1. 页面级权限控制

```javascript
// 使用 PageGuard 保护整个页面
<PageGuard permissions={PAGE_PERMISSIONS.PROJECT_MANAGEMENT}>
  <ProjectManagementPage />
</PageGuard>

// 无权限时显示403页面
<ProtectedRoute permissions={['project.read']} path="/projects">
  <ProjectManagement />
</ProtectedRoute>
```

### 2. 功能区域权限控制

```javascript
// 控制功能区域的显示/隐藏
<FeatureGuard permissions={['project.create']}>
  <div>
    <Button type="primary">新建项目</Button>
    <Button>批量导入</Button>
  </div>
</FeatureGuard>

// 无权限时显示降级内容
<FeatureGuard 
  permissions={['finance.read']}
  fallback={<Alert message="无权限查看财务数据" type="warning" />}
>
  <FinanceChart />
</FeatureGuard>
```

### 3. 按钮级权限控制

```javascript
// 无权限时禁用按钮
<ButtonGuard permissions={['project.update']}>
  <Button icon={<EditOutlined />}>编辑</Button>
</ButtonGuard>

// 无权限时隐藏按钮
<FeatureGuard permissions={['project.delete']}>
  <Button danger icon={<DeleteOutlined />}>删除</Button>
</FeatureGuard>
```

### 4. 表格操作权限控制

```javascript
// 根据权限动态显示操作列
{
  title: '操作',
  render: (_, record) => (
    <Space>
      <FeatureGuard permissions={['project.read']}>
        <Button type="link">查看</Button>
      </FeatureGuard>
      
      <ButtonGuard permissions={['project.update']}>
        <Button type="link">编辑</Button>
      </ButtonGuard>
      
      <ButtonGuard permissions={['project.delete']}>
        <Popconfirm title="确定删除？">
          <Button type="link" danger>删除</Button>
        </Popconfirm>
      </ButtonGuard>
    </Space>
  ),
}
```

## 权限配置

### 1. 页面权限配置 (permissions.js)

```javascript
export const PAGE_PERMISSIONS = {
  // 项目管理
  PROJECT_MANAGEMENT: ['project.read'],
  PROJECT_CREATE: ['project.create'],
  PROJECT_EDIT: ['project.update'],
  PROJECT_DELETE: ['project.delete'],
  
  // 用户管理
  USER_MANAGEMENT: ['user.read'],
  USER_CREATE: ['user.create'],
  // ...
};
```

### 2. 菜单权限配置

```javascript
export const MENU_PERMISSIONS = {
  PROJECT_MENU: ['project.read'],
  BRAND_MENU: ['brand.read'],
  FINANCE_MENU: ['finance.read'],
  SYSTEM_MENU: ['user.read', 'role.read', 'permission.read'],
  // ...
};
```

### 3. 功能权限配置

```javascript
export const FEATURE_PERMISSIONS = {
  CREATE_BUTTON: {
    project: ['project.create'],
    user: ['user.create'],
    // ...
  },
  DELETE_BUTTON: {
    project: ['project.delete'],
    user: ['user.delete'],
    // ...
  },
};
```

## 权限检查逻辑

### 1. 基础权限检查

```javascript
// 检查单个权限
const canCreateProject = hasPermission('project.create');

// 检查多个权限（任一）
const canManageProject = hasAnyPermission(['project.read', 'project.update']);

// 检查多个权限（全部）
const canFullManageProject = hasAllPermissions(['project.read', 'project.create', 'project.update']);
```

### 2. 模块权限检查

```javascript
// 检查模块的特定操作权限
const canReadProject = hasModulePermission('project', 'read');
const canCreateProject = hasModulePermission('project', 'create');

// 获取模块的所有权限
const projectPermissions = getModulePermissions('project');
// 返回: ['read', 'create', 'update', 'delete', 'approve']
```

### 3. 超级管理员检查

```javascript
// 超级管理员拥有所有权限
if (isSuperAdmin) {
  // 允许所有操作
  return true;
}
```

## 实际应用示例

### 1. 项目管理页面

```javascript
const ProjectManagement = () => {
  const { canRead, canCreate, canUpdate, canDelete, canApprove } = useFeaturePermission('project');
  
  return (
    <PageGuard permissions={['project.read']}>
      <div>
        {/* 操作按钮区 */}
        <Space>
          <ButtonGuard permissions={['project.create']}>
            <Button type="primary">新建项目</Button>
          </ButtonGuard>
          
          <FeatureGuard permissions={['report.export']}>
            <Button>导出数据</Button>
          </FeatureGuard>
        </Space>
        
        {/* 数据表格 */}
        <Table 
          columns={[
            // ... 其他列
            {
              title: '操作',
              render: (_, record) => (
                <Space>
                  <FeatureGuard permissions={['project.read']}>
                    <Button type="link">详情</Button>
                  </FeatureGuard>
                  
                  <ButtonGuard permissions={['project.update']}>
                    <Button type="link">编辑</Button>
                  </ButtonGuard>
                  
                  <FeatureGuard permissions={['project.approve']}>
                    <Button type="link">审批</Button>
                  </FeatureGuard>
                  
                  <ButtonGuard permissions={['project.delete']}>
                    <Button type="link" danger>删除</Button>
                  </ButtonGuard>
                </Space>
              ),
            }
          ]}
          rowSelection={canDelete ? rowSelection : null}
        />
      </div>
    </PageGuard>
  );
};
```

### 2. 菜单权限控制

```javascript
const MenuItems = () => {
  const { hasAnyPermission } = usePermission();
  
  return (
    <Menu>
      <Menu.Item key="dashboard">仪表板</Menu.Item>
      
      {hasAnyPermission(['project.read']) && (
        <Menu.Item key="projects">项目管理</Menu.Item>
      )}
      
      {hasAnyPermission(['brand.read']) && (
        <Menu.Item key="brands">品牌管理</Menu.Item>
      )}
      
      {hasAnyPermission(['finance.read']) && (
        <Menu.Item key="finance">财务管理</Menu.Item>
      )}
      
      {hasAnyPermission(['user.read', 'role.read', 'permission.read']) && (
        <SubMenu key="system" title="系统管理">
          {hasAnyPermission(['user.read']) && (
            <Menu.Item key="users">用户管理</Menu.Item>
          )}
          {hasAnyPermission(['role.read']) && (
            <Menu.Item key="roles">角色管理</Menu.Item>
          )}
        </SubMenu>
      )}
    </Menu>
  );
};
```

## 测试页面

### 1. 权限控制演示 (`/permission-demo`)
- 模拟不同角色的权限
- 动态切换模块和角色
- 实时显示权限状态

### 2. 项目管理权限版 (`/project-permission`)
- 完整的项目管理页面
- 应用所有权限控制功能
- 展示实际业务场景

### 3. 权限数据测试 (`/permission-test`)
- 验证权限数据结构
- 按模块分组显示
- 权限映射功能测试

## 权限角色示例

### 1. 系统管理员
```javascript
permissions: state.permissions // 所有权限
```

### 2. 项目经理
```javascript
permissions: [
  'project.read', 'project.create', 'project.update', 'project.delete', 'project.approve',
  'brand.read', 'brand.create', 'brand.update', 'brand.delete',
  'supplier.read', 'supplier.create', 'supplier.update', 'supplier.delete',
  'budget.read', 'budget.create', 'budget.update', 'budget.delete'
]
```

### 3. 财务人员
```javascript
permissions: [
  'finance.read', 'finance.create', 'finance.update', 'finance.delete', 'finance.approve',
  'budget.read', 'budget.approve',
  'project.read',
  'report.read', 'report.export'
]
```

### 4. 普通用户
```javascript
permissions: [
  'project.read',
  'brand.read',
  'supplier.read',
  'budget.read',
  'finance.read',
  'report.read'
]
```

## 总结

这套权限控制系统提供了：

1. **完整的权限层级**: 页面 → 功能区域 → 按钮 → 具体操作
2. **灵活的权限检查**: 支持单个、多个、模块级权限检查
3. **友好的用户体验**: 无权限时的降级显示和禁用状态
4. **易于维护**: 集中的权限配置和清晰的组件结构
5. **实际业务应用**: 真实的项目管理场景演示

通过这套系统，您可以精确控制用户在系统中能看到什么、能操作什么，确保数据安全和功能访问的合规性。
