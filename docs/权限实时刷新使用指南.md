# 权限实时刷新使用指南

## 概述

为了解决后台重新分配用户角色后，前端缓存没有及时更新的问题，我们实现了权限实时刷新机制。

## 核心特性

### 1. 实时权限获取
- 每次权限检查都可以选择从后台获取最新数据
- 不再完全依赖本地缓存
- 确保权限信息的实时性

### 2. 手动权限刷新
- 提供权限刷新按钮
- 用户可以主动刷新权限信息
- 自动检测权限变更并提示用户

### 3. 智能缓存策略
- 默认使用缓存提高性能
- 关键操作时实时获取最新权限
- 平衡性能和实时性

## 使用方法

### 1. 在组件中使用权限刷新Hook

```javascript
import { usePermissionRefresh } from '../hooks/usePermissionRefresh';

const MyComponent = () => {
  const { 
    refreshing, 
    refreshPermissions, 
    checkPermissionRealtime 
  } = usePermissionRefresh();

  // 手动刷新权限
  const handleRefresh = async () => {
    const result = await refreshPermissions();
    if (result.success) {
      console.log('权限刷新成功');
    }
  };

  // 实时检查权限（不使用缓存）
  const checkPermission = async () => {
    const hasPermission = await checkPermissionRealtime('project:create');
    console.log('是否有创建项目权限:', hasPermission);
  };

  return (
    <div>
      <Button loading={refreshing} onClick={handleRefresh}>
        刷新权限
      </Button>
      <Button onClick={checkPermission}>
        检查权限
      </Button>
    </div>
  );
};
```

### 2. 使用权限刷新按钮组件

```javascript
import PermissionRefreshButton from '../components/Auth/PermissionRefreshButton';

const Header = () => {
  return (
    <div>
      {/* 简单的刷新按钮 */}
      <PermissionRefreshButton />
      
      {/* 带文字的刷新按钮 */}
      <PermissionRefreshButton showText />
      
      {/* 自定义样式的刷新按钮 */}
      <PermissionRefreshButton 
        type="primary" 
        size="large"
        tooltip="点击刷新我的权限"
      />
    </div>
  );
};
```

### 3. 使用实时权限守卫

```javascript
import { PermissionGuard } from '../components/Auth/PermissionGuard';

const MyComponent = () => {
  return (
    <div>
      {/* 使用缓存的权限检查（默认） */}
      <PermissionGuard permissions={['project:create']}>
        <Button>创建项目</Button>
      </PermissionGuard>
      
      {/* 实时权限检查（不使用缓存） */}
      <PermissionGuard 
        permissions={['project:delete']} 
        realtime={true}
        showLoading={true}
      >
        <Button danger>删除项目</Button>
      </PermissionGuard>
    </div>
  );
};
```

### 4. 在用户菜单中集成

```javascript
import UserMenu from '../components/Layout/UserMenu';

const AppHeader = () => {
  return (
    <Header>
      <div className="header-right">
        <UserMenu /> {/* 包含权限刷新功能的用户菜单 */}
      </div>
    </Header>
  );
};
```

## API 参考

### authService 新增方法

#### `getLatestUserInfo()`
获取最新的用户信息（每次都从后台获取）

```javascript
const userInfo = await authService.getLatestUserInfo();
```

#### `hasPermission(permission, useCache = true)`
检查权限（支持实时获取）

```javascript
// 使用缓存（默认）
const hasPermission = await authService.hasPermission('project:create');

// 实时获取
const hasPermission = await authService.hasPermission('project:create', false);
```

#### `hasRole(role, useCache = true)`
检查角色（支持实时获取）

```javascript
// 使用缓存（默认）
const hasRole = await authService.hasRole('admin');

// 实时获取
const hasRole = await authService.hasRole('admin', false);
```

#### `refreshUserPermissions()`
刷新用户权限信息

```javascript
const result = await authService.refreshUserPermissions();
if (result.success) {
  console.log('权限刷新成功');
  if (result.hasChanges) {
    console.log('检测到权限变更');
  }
}
```

### Hook API

#### `usePermissionRefresh()`

返回对象：
- `refreshing`: boolean - 是否正在刷新
- `refreshPermissions`: function - 手动刷新权限
- `checkPermissionRealtime`: function - 实时权限检查
- `checkRoleRealtime`: function - 实时角色检查
- `checkMultiplePermissions`: function - 批量权限检查

## 最佳实践

### 1. 性能优化
- 默认使用缓存，只在必要时实时获取
- 避免频繁的实时权限检查
- 使用批量检查减少网络请求

### 2. 用户体验
- 在关键操作前进行实时权限检查
- 提供明显的权限刷新入口
- 权限变更时给用户明确提示

### 3. 错误处理
- 网络异常时降级到缓存数据
- 权限检查失败时给用户友好提示
- 记录权限相关的错误日志

## 注意事项

1. **向后兼容性**: 原有的同步权限检查方法仍然可用
2. **性能考虑**: 实时权限检查会增加网络请求，请合理使用
3. **错误处理**: 实时权限检查可能因网络问题失败，需要适当的错误处理
4. **缓存策略**: 系统会智能地平衡缓存和实时性，大多数情况下使用默认设置即可

## 故障排除

### 权限刷新失败
1. 检查网络连接
2. 确认用户token是否有效
3. 查看控制台错误日志

### 权限检查不准确
1. 尝试手动刷新权限
2. 检查后台权限配置
3. 确认用户角色分配是否正确

通过这套权限实时刷新机制，用户在角色权限被管理员修改后，可以立即获得最新的权限信息，无需手动刷新页面。
