# Store 状态管理使用指南

## 概述

本项目使用基于 React Context 的状态管理系统，提供全局状态管理、数据映射、选择器函数等功能。

## 核心功能

### 1. 全局状态管理
- 用户数据 (users, userMap)
- 部门数据 (departments, departmentMap)
- 角色数据 (roles, roleMap)
- 权限数据 (permissions, permissionMap)
- 品牌数据 (brands, brandMap)
- 供应商数据 (suppliers, supplierMap)
- 项目数据 (projects, projectMap)

### 2. 自动映射
所有数据都会自动创建 ID 到对象的映射，方便快速查找：
```javascript
// 自动生成的映射
userMap: { 1: { id: 1, name: '张三', ... }, 2: { id: 2, name: '李四', ... } }
departmentMap: { 1: { id: 1, name: '项目管理部', ... } }
```

### 3. 选择器函数
提供丰富的选择器函数用于数据查询和过滤。

### 4. 自定义 Hooks
封装了常用的状态操作，简化组件中的使用。

## 使用方式

### 1. 基本使用

```javascript
import React from 'react';
import { useUsers, useDepartments, useNameMappings } from '../store/hooks';

const MyComponent = () => {
  const { users, loading, fetchUsers } = useUsers();
  const { getDepartmentName } = useDepartments();
  const { getUserName } = useNameMappings();

  // 获取用户列表
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  return (
    <div>
      {users.map(user => (
        <div key={user.id}>
          {user.name} - {getDepartmentName(user.departmentId)}
        </div>
      ))}
    </div>
  );
};
```

### 2. 数据查询和过滤

```javascript
const { searchUsers, getUsersByDepartment } = useUsers();

// 搜索用户
const filteredUsers = searchUsers('张三');

// 按部门获取用户
const deptUsers = getUsersByDepartment(1);
```

### 3. 名称映射

```javascript
const { getUserName, getDepartmentName, getRoleName } = useNameMappings();

// 快速获取名称
const userName = getUserName(userId);
const deptName = getDepartmentName(departmentId);
const roleName = getRoleName(roleId);
```

### 4. 统计数据

```javascript
const { userStats, roleStats, departmentStats } = useStats();

console.log(userStats); // { total: 10, active: 8, inactive: 2 }
```

### 5. 异步操作

```javascript
const { createUser, updateUser, deleteUser } = useUsers();

// 创建用户
try {
  const newUser = await createUser({
    name: '新用户',
    mobile: '13800138000',
    departmentId: 1
  });
  message.success('用户创建成功');
} catch (error) {
  message.error('创建失败: ' + error.message);
}
```

## 可用的 Hooks

### useUsers()
```javascript
const {
  users,              // 用户列表
  userMap,            // 用户映射
  loading,            // 加载状态
  error,              // 错误信息
  fetchUsers,         // 获取用户列表
  createUser,         // 创建用户
  updateUser,         // 更新用户
  deleteUser,         // 删除用户
  syncDingTalkUsers,  // 同步钉钉用户
  getUserById,        // 根据ID获取用户
  getUsersByDepartment, // 根据部门获取用户
  searchUsers,        // 搜索用户
} = useUsers();
```

### useDepartments()
```javascript
const {
  departments,        // 部门列表
  departmentMap,      // 部门映射
  getDepartmentById,  // 根据ID获取部门
  getDepartmentName,  // 根据ID获取部门名称
  fetchDepartmentUsers, // 获取部门用户
  getDepartmentUsers, // 获取部门用户（从缓存）
  getDepartmentStats, // 获取部门统计
} = useDepartments();
```

### useRoles()
```javascript
const {
  roles,              // 角色列表
  roleMap,            // 角色映射
  loading,            // 加载状态
  error,              // 错误信息
  fetchRoles,         // 获取角色列表
  createRole,         // 创建角色
  updateRole,         // 更新角色
  deleteRole,         // 删除角色
  getRoleById,        // 根据ID获取角色
  getRoleName,        // 根据ID获取角色名称
  getRolesByIds,      // 根据ID数组获取角色
  searchRoles,        // 搜索角色
  getRoleStats,       // 获取角色统计
} = useRoles();
```

### usePermissions()
```javascript
const {
  permissions,        // 权限列表
  permissionMap,      // 权限映射
  permissionCategories, // 权限分类
  loading,            // 加载状态
  error,              // 错误信息
  fetchPermissions,   // 获取权限列表
  fetchPermissionCategories, // 获取权限分类
  getPermissionById,  // 根据ID获取权限
  getPermissionsByCategory, // 根据分类获取权限
  getPermissionsByIds, // 根据ID数组获取权限
  searchPermissions,  // 搜索权限
} = usePermissions();
```

### useNameMappings()
```javascript
const {
  getUserName,        // 获取用户名称
  getDepartmentName,  // 获取部门名称
  getRoleName,        // 获取角色名称
  getBrandName,       // 获取品牌名称
  getSupplierName,    // 获取供应商名称
  getProjectName,     // 获取项目名称
} = useNameMappings();
```

### useStats()
```javascript
const {
  userStats,          // 用户统计 { total, active, inactive, pending }
  roleStats,          // 角色统计 { total, active, inactive }
  departmentStats,    // 部门统计 [{ ...department, userCount }]
} = useStats();
```

## 最佳实践

### 1. 数据初始化
在应用启动时初始化基础数据：
```javascript
// 在 App.js 或主组件中
useEffect(() => {
  initializeStore();
}, []);
```

### 2. 错误处理
始终处理异步操作的错误：
```javascript
try {
  await fetchUsers();
} catch (error) {
  message.error('加载失败: ' + error.message);
}
```

### 3. 加载状态
使用加载状态提升用户体验：
```javascript
<Table loading={loading} dataSource={users} />
```

### 4. 名称映射
优先使用名称映射函数而不是手动查找：
```javascript
// 推荐
const deptName = getDepartmentName(departmentId);

// 不推荐
const dept = departments.find(d => d.id === departmentId);
const deptName = dept ? dept.name : '未知部门';
```

### 5. 搜索和过滤
使用内置的搜索函数：
```javascript
// 推荐
const results = searchUsers(keyword);

// 不推荐
const results = users.filter(user => 
  user.name.includes(keyword) || user.mobile.includes(keyword)
);
```

## 扩展

### 添加新的数据类型
1. 在 `store/index.js` 中添加状态定义
2. 在 `store/selectors.js` 中添加选择器
3. 在 `store/actions.js` 中添加异步操作
4. 在 `store/hooks.js` 中添加自定义 hook

### 添加新的选择器
在 `store/selectors.js` 中添加新的选择器函数：
```javascript
export const getActiveUsers = (state) => 
  state.users.filter(user => user.status === 'active');
```

### 添加新的统计
在 `store/selectors.js` 中添加统计选择器：
```javascript
export const getProjectStats = (state) => {
  const projects = state.projects;
  return {
    total: projects.length,
    active: projects.filter(p => p.status === 'active').length,
    completed: projects.filter(p => p.status === 'completed').length,
  };
};
```
