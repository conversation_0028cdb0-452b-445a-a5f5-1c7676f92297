# ProjectTable 权限控制升级

## 概述

我已经成功将 `ProjectTable.js` 组件从类组件转换为函数组件，并添加了完整的权限控制功能。这个升级确保了用户只能看到和操作他们有权限的功能。

## 主要改造内容

### 1. 组件架构升级

#### 从类组件转换为函数组件
```javascript
// 旧版本 - 类组件
class ProjectTable extends Component {
  constructor(props) {
    super(props);
    this.state = { ... };
  }
  
  componentDidMount() {
    this.loadData();
  }
  
  render() { ... }
}

// 新版本 - 函数组件
const ProjectTable = () => {
  const [dataSource, setDataSource] = useState([]);
  // ... 其他状态
  
  useEffect(() => {
    if (canRead) {
      loadData();
    }
  }, [canRead, loadData]);
  
  return ( ... );
};
```

#### 权限控制集成
```javascript
// 权限检查 Hooks
const {
  canRead,
  canCreate,
  canUpdate,
  canDelete,
  canApprove,
  isSuperAdmin,
} = useFeaturePermission('project');

// 财务和预算权限
const financePermission = useFeaturePermission('finance');
const budgetPermission = useFeaturePermission('budget');
```

### 2. 页面级权限控制

#### 整页权限保护
```javascript
// 无权限时显示提示
if (!canRead) {
  return (
    <div style={{ padding: '20px' }}>
      <Alert
        message="无权限访问"
        description="您没有权限查看项目管理页面"
        type="warning"
        showIcon
      />
    </div>
  );
}

// 有权限时用 PageGuard 包装
return (
  <PageGuard permissions={PAGE_PERMISSIONS.PROJECT_MANAGEMENT}>
    {/* 页面内容 */}
  </PageGuard>
);
```

#### 权限状态显示
```javascript
<Alert
  message="权限控制已启用"
  description={
    <div>
      <p><strong>当前权限状态:</strong></p>
      <Space wrap>
        <Tag color={canRead ? 'green' : 'red'}>查看: {canRead ? '✓' : '✗'}</Tag>
        <Tag color={canCreate ? 'green' : 'red'}>创建: {canCreate ? '✓' : '✗'}</Tag>
        <Tag color={canUpdate ? 'green' : 'red'}>编辑: {canUpdate ? '✓' : '✗'}</Tag>
        <Tag color={canDelete ? 'green' : 'red'}>删除: {canDelete ? '✓' : '✗'}</Tag>
        {/* ... 更多权限状态 */}
      </Space>
    </div>
  }
  type="info"
  closable
/>
```

### 3. 功能级权限控制

#### 新建项目按钮
```javascript
<ButtonGuard permissions={PAGE_PERMISSIONS.PROJECT_CREATE}>
  <Button
    type="primary"
    onClick={handleAdd}
    icon="plus"
  >
    新建项目
  </Button>
</ButtonGuard>
```

#### 批量删除功能
```javascript
<FeatureGuard permissions={['project.delete']}>
  {selectedRowKeys.length > 0 && (
    <div style={{ marginBottom: 16 }}>
      <Popconfirm
        title={`确定要删除选中的${selectedRowKeys.length}个项目吗？`}
        onConfirm={handleBatchDelete}
      >
        <Button type="danger">
          批量删除 ({selectedRowKeys.length})
        </Button>
      </Popconfirm>
    </div>
  )}
</FeatureGuard>
```

### 4. 表格操作权限控制

#### 操作列权限控制
```javascript
{
  title: '操作',
  key: 'action',
  width: 300,
  fixed: 'right',
  render: (_, record) => (
    <Space size="small" wrap>
      {/* 查看详情 */}
      <FeatureGuard permissions={['project.read']}>
        <Button type="link" size="small" onClick={() => handleViewDetail(record)}>
          <Icon type="eye" /> 详情
        </Button>
      </FeatureGuard>

      {/* 编辑项目 */}
      <ButtonGuard permissions={['project.update']}>
        <Button type="link" size="small" onClick={() => handleEdit(record)}>
          <Icon type="edit" /> 编辑
        </Button>
      </ButtonGuard>

      {/* 收入管理 */}
      <FeatureGuard permissions={['finance.read']}>
        <Button type="link" size="small" onClick={() => handleRevenueManagement(record)}>
          <Icon type="dollar" /> 收入
        </Button>
      </FeatureGuard>

      {/* 预算管理 */}
      <FeatureGuard permissions={['budget.read']}>
        <Button type="link" size="small" onClick={() => handleWeeklyBudgetManagement(record)}>
          <Icon type="calendar" /> 预算
        </Button>
      </FeatureGuard>

      {/* 删除项目 */}
      <ButtonGuard permissions={['project.delete']}>
        <Popconfirm title="确定要删除这个项目吗？" onConfirm={() => handleDelete(record)}>
          <Button type="link" size="small" style={{ color: '#f5222d' }}>
            <Icon type="delete" /> 删除
          </Button>
        </Popconfirm>
      </ButtonGuard>
    </Space>
  ),
}
```

#### 行选择权限控制
```javascript
const rowSelection = {
  selectedRowKeys,
  onChange: (keys) => {
    setSelectedRowKeys(keys);
  },
  getCheckboxProps: () => ({
    disabled: !canDelete, // 没有删除权限时禁用选择
  }),
};

// 表格配置
<Table
  columns={columns}
  dataSource={dataSource}
  rowKey="id"
  loading={loading}
  rowSelection={canDelete ? rowSelection : null} // 无删除权限时不显示选择框
  // ... 其他配置
/>
```

### 5. 模态框权限控制

#### 新建项目模态框
```javascript
<FeatureGuard permissions={['project.create']}>
  <Modal
    title="新建项目"
    visible={createModalVisible}
    onCancel={handleCreateModalCancel}
    footer={null}
    width={1200}
    destroyOnClose
  >
    <ProjectForm onSubmit={handleCreateFormSubmit} hideActions={false} />
  </Modal>
</FeatureGuard>
```

#### 编辑项目模态框
```javascript
<FeatureGuard permissions={['project.update']}>
  <ProjectEditModal
    visible={editModalVisible}
    project={editingProject}
    onCancel={handleEditModalCancel}
    onSubmit={handleEditFormSubmit}
  />
</FeatureGuard>
```

#### 收入管理模态框
```javascript
<FeatureGuard permissions={['finance.read']}>
  <Modal
    title={`项目收入管理${selectedProjectForRevenue ? ` - ${selectedProjectForRevenue.projectName}` : ''}`}
    visible={revenueModalVisible}
    onCancel={handleRevenueModalCancel}
    footer={null}
    width={1200}
    destroyOnClose
  >
    <ProjectRevenue
      projectName={selectedProjectForRevenue.projectName}
      projectId={selectedProjectForRevenue.id}
      projectInfo={selectedProjectForRevenue}
    />
  </Modal>
</FeatureGuard>
```

#### 周预算管理模态框
```javascript
<FeatureGuard permissions={['budget.read']}>
  <Modal
    title={`项目周预算管理${selectedProjectForWeeklyBudget ? ` - ${selectedProjectForWeeklyBudget.projectName}` : ''}`}
    visible={weeklyBudgetModalVisible}
    onCancel={handleWeeklyBudgetModalCancel}
    footer={null}
    width={1200}
    destroyOnClose
  >
    <WeeklyBudgetManagement
      projectId={selectedProjectForWeeklyBudget.id}
      projectInfo={selectedProjectForWeeklyBudget}
    />
  </Modal>
</FeatureGuard>
```

### 6. 权限配置

#### 页面权限定义
```javascript
// src/config/permissions.js
export const PAGE_PERMISSIONS = {
  PROJECT_MANAGEMENT: ['project.read'],
  PROJECT_CREATE: ['project.create'],
  PROJECT_EDIT: ['project.update'],
  PROJECT_DELETE: ['project.delete'],
  // ...
};
```

#### 功能权限检查
```javascript
// 项目相关权限
const projectPermissions = useFeaturePermission('project');
// 财务相关权限
const financePermissions = useFeaturePermission('finance');
// 预算相关权限
const budgetPermissions = useFeaturePermission('budget');
```

## 权限控制层级

### 1. 页面级
- 整个项目管理页面需要 `project.read` 权限
- 无权限时显示友好的提示信息

### 2. 功能区域级
- 新建项目区域需要 `project.create` 权限
- 批量删除区域需要 `project.delete` 权限
- 收入管理需要 `finance.read` 权限
- 预算管理需要 `budget.read` 权限

### 3. 按钮级
- 编辑按钮需要 `project.update` 权限，无权限时禁用
- 删除按钮需要 `project.delete` 权限，无权限时禁用

### 4. 操作级
- 表格行选择功能根据删除权限控制
- 各种模态框根据对应权限显示/隐藏

## 用户体验优化

### 1. 权限状态可视化
- 页面顶部显示当前用户的权限状态
- 用不同颜色的标签表示权限状态（绿色=有权限，红色=无权限）

### 2. 优雅降级
- 无权限的按钮显示为禁用状态而不是隐藏
- 无权限的功能区域显示友好的提示信息
- 保持界面布局的一致性

### 3. 错误处理
- 权限检查失败时的错误处理
- 网络请求失败时的降级处理

## 技术改进

### 1. 性能优化
- 使用 `useCallback` 优化函数引用
- 合理的依赖数组避免不必要的重渲染
- 权限检查结果缓存

### 2. 代码质量
- 函数组件替代类组件，代码更简洁
- 统一的权限控制模式
- 清晰的组件职责分离

### 3. 可维护性
- 集中的权限配置管理
- 可复用的权限控制组件
- 清晰的权限检查逻辑

## 总结

通过这次升级，ProjectTable 组件现在具备了：

1. **完整的权限控制**: 从页面级到按钮级的全方位权限控制
2. **优秀的用户体验**: 权限状态可视化和优雅降级
3. **现代化的架构**: 函数组件 + Hooks 的现代 React 模式
4. **高可维护性**: 清晰的代码结构和统一的权限控制模式

这个升级为其他组件的权限控制改造提供了标准模板和最佳实践参考。
