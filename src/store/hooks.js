import { useCallback, useMemo } from 'react';
import { useStore } from './index';
import * as selectors from './selectors';
import * as actions from './actions';

// 用户相关 hooks
export const useUsers = () => {
  const { state, dispatch } = useStore();


  const currentUser = useMemo(() => selectors.getCurrentUser(state), [state]);
  const users = useMemo(() => selectors.getUsers(state), [state]);
  const userMap = useMemo(() => selectors.getUserMap(state), [state]);
  const loading = useMemo(() => selectors.isLoading(state, 'users'), [state]);
  const error = useMemo(() => selectors.getError(state, 'users'), [state]);

  const fetchUsers = useCallback((params) => {
    return actions.fetchUsers(params)(dispatch);
  }, [dispatch]);

  const createUser = useCallback((userData) => {
    return actions.createUser(userData)(dispatch);
  }, [dispatch]);

  const updateUser = useCallback((userId, userData) => {
    return actions.updateUser(userId, userData)(dispatch);
  }, [dispatch]);

  const deleteUser = useCallback((userId) => {
    return actions.deleteUser(userId)(dispatch);
  }, [dispatch]);

  const syncDingTalkUsers = useCallback((params) => {
    return actions.syncDingTalkUsers(params)(dispatch);
  }, [dispatch]);

  const getUserById = useCallback((userId) => {
    return selectors.getUserById(state, userId);
  }, [state]);

  const getUsersByDepartment = useCallback((departmentId) => {
    return selectors.getUsersByDepartment(state, departmentId);
  }, [state]);

  const searchUsers = useCallback((keyword) => {
    return selectors.searchUsers(state, keyword);
  }, [state]);

  return {
    currentUser,
    users,
    userMap,
    loading,
    error,
    fetchUsers,
    createUser,
    updateUser,
    deleteUser,
    syncDingTalkUsers,
    getUserById,
    getUsersByDepartment,
    searchUsers,
  };
};

// 部门相关 hooks
export const useDepartments = () => {
  const { state, dispatch } = useStore();

  const departments = useMemo(() => selectors.getDepartments(state), [state]);
  const departmentMap = useMemo(() => selectors.getDepartmentMap(state), [state]);

  const getDepartmentById = useCallback((departmentId) => {
    return selectors.getDepartmentById(state, departmentId);
  }, [state]);

  const getDepartmentName = useCallback((departmentId) => {
    return selectors.getDepartmentName(state, departmentId);
  }, [state]);

  const fetchDepartmentUsers = useCallback((departmentId, params) => {
    return actions.fetchDepartmentUsers(departmentId, params)(dispatch);
  }, [dispatch]);

  const getDepartmentUsers = useCallback((departmentId) => {
    return selectors.getDepartmentUsers(state, departmentId);
  }, [state]);

  const getDepartmentStats = useCallback(() => {
    return selectors.getDepartmentStats(state);
  }, [state]);

  return {
    departments,
    departmentMap,
    getDepartmentById,
    getDepartmentName,
    fetchDepartmentUsers,
    getDepartmentUsers,
    getDepartmentStats,
  };
};

// 角色相关 hooks
export const useRoles = () => {
  const { state, dispatch } = useStore();

  const roles = useMemo(() => selectors.getRoles(state), [state]);
  const roleMap = useMemo(() => selectors.getRoleMap(state), [state]);
  const loading = useMemo(() => selectors.isLoading(state, 'roles'), [state]);
  const error = useMemo(() => selectors.getError(state, 'roles'), [state]);

  const fetchRoles = useCallback((params) => {
    return actions.fetchRoles(params)(dispatch);
  }, [dispatch]);

  const createRole = useCallback((roleData) => {
    return actions.createRole(roleData)(dispatch);
  }, [dispatch]);

  const updateRole = useCallback((roleId, roleData) => {
    return actions.updateRole(roleId, roleData)(dispatch);
  }, [dispatch]);

  const deleteRole = useCallback((roleId) => {
    return actions.deleteRole(roleId)(dispatch);
  }, [dispatch]);

  const getRoleById = useCallback((roleId) => {
    return selectors.getRoleById(state, roleId);
  }, [state]);

  const getRoleName = useCallback((roleId) => {
    return selectors.getRoleName(state, roleId);
  }, [state]);

  const getRolesByIds = useCallback((roleIds) => {
    return selectors.getRolesByIds(state, roleIds);
  }, [state]);

  const searchRoles = useCallback((keyword) => {
    return selectors.searchRoles(state, keyword);
  }, [state]);

  const getRoleStats = useCallback(() => {
    return selectors.getRoleStats(state);
  }, [state]);

  return {
    roles,
    roleMap,
    loading,
    error,
    fetchRoles,
    createRole,
    updateRole,
    deleteRole,
    getRoleById,
    getRoleName,
    getRolesByIds,
    searchRoles,
    getRoleStats,
  };
};

// 权限相关 hooks
export const usePermissions = () => {
  const { state, dispatch } = useStore();

  const permissions = useMemo(() => selectors.getPermissions(state), [state]);
  const permissionMap = useMemo(() => selectors.getPermissionMap(state), [state]);
  const permissionCategories = useMemo(() => selectors.getPermissionCategories(state), [state]);
  const loading = useMemo(() => selectors.isLoading(state, 'permissions'), [state]);
  const error = useMemo(() => selectors.getError(state, 'permissions'), [state]);

  const fetchPermissions = useCallback((params) => {
    return actions.fetchPermissions(params)(dispatch);
  }, [dispatch]);

  const fetchPermissionCategories = useCallback(() => {
    return actions.fetchPermissionCategories()(dispatch);
  }, [dispatch]);

  const getPermissionById = useCallback((permissionId) => {
    return selectors.getPermissionById(state, permissionId);
  }, [state]);

  const getPermissionsByCategory = useCallback((category) => {
    return selectors.getPermissionsByCategory(state, category);
  }, [state]);

  const getPermissionsByIds = useCallback((permissionIds) => {
    return selectors.getPermissionsByIds(state, permissionIds);
  }, [state]);

  const searchPermissions = useCallback((keyword) => {
    return selectors.searchPermissions(state, keyword);
  }, [state]);

  return {
    permissions,
    permissionMap,
    permissionCategories,
    loading,
    error,
    fetchPermissions,
    fetchPermissionCategories,
    getPermissionById,
    getPermissionsByCategory,
    getPermissionsByIds,
    searchPermissions,
  };
};

// 通用 hooks
export const useLoading = () => {
  const { state } = useStore();

  const loading = useMemo(() => selectors.getLoading(state), [state]);

  const isLoading = useCallback((key) => {
    return selectors.isLoading(state, key);
  }, [state]);

  return {
    loading,
    isLoading,
  };
};

export const useErrors = () => {
  const { state, dispatch } = useStore();

  const errors = useMemo(() => selectors.getErrors(state), [state]);

  const getError = useCallback((key) => {
    return selectors.getError(state, key);
  }, [state]);

  const clearError = useCallback((key) => {
    return actions.clearError(key)(dispatch);
  }, [dispatch]);

  return {
    errors,
    getError,
    clearError,
  };
};

// 名称映射 hooks - 用于快速获取 ID 对应的名称
export const useNameMappings = () => {
  const { state } = useStore();

  const getUserName = useCallback((userId) => {
    const user = selectors.getUserById(state, userId);
    return user ? user.name : '未知用户';
  }, [state]);

  const getDepartmentName = useCallback((departmentId) => {
    return selectors.getDepartmentName(state, departmentId);
  }, [state]);

  const getRoleName = useCallback((roleId) => {
    return selectors.getRoleName(state, roleId);
  }, [state]);

  const getBrandName = useCallback((brandId) => {
    return selectors.getBrandName(state, brandId);
  }, [state]);

  const getSupplierName = useCallback((supplierId) => {
    return selectors.getSupplierName(state, supplierId);
  }, [state]);

  const getProjectName = useCallback((projectId) => {
    return selectors.getProjectName(state, projectId);
  }, [state]);

  return {
    getUserName,
    getDepartmentName,
    getRoleName,
    getBrandName,
    getSupplierName,
    getProjectName,
  };
};

// 统计数据 hooks
export const useStats = () => {
  const { state } = useStore();

  const userStats = useMemo(() => selectors.getUserStats(state), [state]);
  const roleStats = useMemo(() => selectors.getRoleStats(state), [state]);
  const departmentStats = useMemo(() => selectors.getDepartmentStats(state), [state]);

  return {
    userStats,
    roleStats,
    departmentStats,
  };
};

export const useApp = () => {
  const { state, dispatch } = useStore();

  const getSpaceId = useCallback(() => {
    return selectors.getSpaceId(state);
  }, [state]);

  const fetchSpaceId = useCallback(() => {
    return actions.fetchSpaceId(state)(dispatch);
  }, [state, dispatch]);

  return {
    getSpaceId,
    // setSpaceId,
    fetchSpaceId,
  };
};
