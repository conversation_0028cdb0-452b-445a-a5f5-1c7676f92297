import React, { createContext, useContext, useReducer, useEffect, useRef } from 'react';
import { roleApi, permissionApi, departmentAPI, userApi } from '../services/api';

// 创建空的基础数据
const createEmptyBaseData = () => ({
  currentUserPermissions: [],
  currentUserRoles: [],
  departments: [],
  departmentMap: {},
  roles: [],
  roleMap: {},
  permissions: [],
  permissionMap: {},
  permissionCategories: [],
});
// 创建初始状态的函数，支持预初始化数据
const createInitialState = (preloadedData = null) => {
  const baseData = preloadedData || createEmptyBaseData();

  return {
    // app
    spaceId: null,

    // 用户相关
    users: [],
    userMap: {}, // id -> user 映射
    currentUser: null,
    currentUserPermissions: baseData.currentUserPermissions,
    currentUserRoles: baseData.currentUserRoles,

    // 部门相关（可预初始化）
    departments: baseData.departments,
    departmentMap: baseData.departmentMap,
    departmentUsers: {}, // departmentId -> users[] 映射

    // 角色相关（可预初始化）
    roles: baseData.roles,
    roleMap: baseData.roleMap,

    // 权限相关（可预初始化）
    permissions: baseData.permissions,
    permissionMap: baseData.permissionMap,
    permissionCategories: baseData.permissionCategories,

    // 品牌相关
    brands: [],
    brandMap: {}, // id -> brand 映射

    // 供应商相关
    suppliers: [],
    supplierMap: {}, // id -> supplier 映射

    // 项目相关
    projects: [],
    projectMap: {}, // id -> project 映射

    // 加载状态
    loading: {
      users: false,
      departments: false,
      roles: false,
      permissions: false,
      brands: false,
      suppliers: false,
      projects: false,
    },

    // 错误状态
    errors: {},
  };
};


// Action 类型
const ActionTypes = {
  SET_SPACE_ID: 'SET_SPACE_ID',
  // 设置加载状态
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',

  // 用户相关
  SET_USERS: 'SET_USERS',
  ADD_USER: 'ADD_USER',
  UPDATE_USER: 'UPDATE_USER',
  DELETE_USER: 'DELETE_USER',
  SET_CURRENT_USER: 'SET_CURRENT_USER',

  // 部门相关
  SET_DEPARTMENTS: 'SET_DEPARTMENTS',
  SET_DEPARTMENT_USERS: 'SET_DEPARTMENT_USERS',

  // 角色相关
  SET_ROLES: 'SET_ROLES',
  ADD_ROLE: 'ADD_ROLE',
  UPDATE_ROLE: 'UPDATE_ROLE',
  DELETE_ROLE: 'DELETE_ROLE',

  // 权限相关
  SET_PERMISSIONS: 'SET_PERMISSIONS',
  SET_PERMISSION_CATEGORIES: 'SET_PERMISSION_CATEGORIES',
  ADD_PERMISSION: 'ADD_PERMISSION',
  UPDATE_PERMISSION: 'UPDATE_PERMISSION',
  DELETE_PERMISSION: 'DELETE_PERMISSION',
  SET_CURRENT_USER_PERMISSIONS: 'SET_CURRENT_USER_PERMISSIONS',
  SET_CURRENT_USER_ROLES: 'SET_CURRENT_USER_ROLES',

  // 品牌相关
  SET_BRANDS: 'SET_BRANDS',
  ADD_BRAND: 'ADD_BRAND',
  UPDATE_BRAND: 'UPDATE_BRAND',
  DELETE_BRAND: 'DELETE_BRAND',

  // 供应商相关
  SET_SUPPLIERS: 'SET_SUPPLIERS',
  ADD_SUPPLIER: 'ADD_SUPPLIER',
  UPDATE_SUPPLIER: 'UPDATE_SUPPLIER',
  DELETE_SUPPLIER: 'DELETE_SUPPLIER',

  // 项目相关
  SET_PROJECTS: 'SET_PROJECTS',
  ADD_PROJECT: 'ADD_PROJECT',
  UPDATE_PROJECT: 'UPDATE_PROJECT',
  DELETE_PROJECT: 'DELETE_PROJECT',
};

// 创建映射的辅助函数
const createMap = (items, keyField = 'id') => {
  return items.reduce((acc, item) => {
    return { ...acc, [item[keyField]]: item };
  }, {});
};

// Reducer
const storeReducer = (state, action) => {
  switch (action.type) {
    case ActionTypes.SET_SPACE_ID:
      return {
        ...state,
        spaceId: action.payload,
      };

    case ActionTypes.SET_LOADING:
      return {
        ...state,
        loading: {
          ...state.loading,
          [action.payload.key]: action.payload.value,
        },
      };

    case ActionTypes.SET_ERROR:
      return {
        ...state,
        errors: {
          ...state.errors,
          [action.payload.key]: action.payload.error,
        },
      };

    // 用户相关
    case ActionTypes.SET_USERS:
      return {
        ...state,
        users: action.payload,
        userMap: createMap(action.payload),
      };

    case ActionTypes.ADD_USER: {
      const newUsers = [...state.users, action.payload];
      return {
        ...state,
        users: newUsers,
        userMap: createMap(newUsers),
      };
    }

    case ActionTypes.UPDATE_USER: {
      const updatedUsers = state.users.map((user) =>
        (user.id === action.payload.id ? { ...user, ...action.payload } : user));
      return {
        ...state,
        users: updatedUsers,
        userMap: createMap(updatedUsers),
      };
    }

    case ActionTypes.DELETE_USER: {
      const filteredUsers = state.users.filter((user) => user.id !== action.payload);
      return {
        ...state,
        users: filteredUsers,
        userMap: createMap(filteredUsers),
      };
    }

    case ActionTypes.SET_CURRENT_USER:
      console.log('[ action.payload ] >', action.payload);
      return {
        ...state,
        currentUser: action.payload,
      };

    // 部门相关
    case ActionTypes.SET_DEPARTMENTS:
      return {
        ...state,
        departments: action.payload,
        departmentMap: createMap(action.payload, 'deptId'),
      };

    case ActionTypes.SET_DEPARTMENT_USERS:
      return {
        ...state,
        departmentUsers: {
          ...state.departmentUsers,
          [action.payload.departmentId]: action.payload.users,
        },
      };

    // 角色相关
    case ActionTypes.SET_ROLES:
      return {
        ...state,
        roles: action.payload,
        roleMap: createMap(action.payload),
      };

    case ActionTypes.ADD_ROLE: {
      const newRoles = [...state.roles, action.payload];
      return {
        ...state,
        roles: newRoles,
        roleMap: createMap(newRoles),
      };
    }

    case ActionTypes.UPDATE_ROLE: {
      const updatedRoles = state.roles.map((role) =>
        (role.id === action.payload.id ? { ...role, ...action.payload } : role));
      return {
        ...state,
        roles: updatedRoles,
        roleMap: createMap(updatedRoles),
      };
    }

    case ActionTypes.DELETE_ROLE: {
      const filteredRoles = state.roles.filter((role) => role.id !== action.payload);
      return {
        ...state,
        roles: filteredRoles,
        roleMap: createMap(filteredRoles),
      };
    }

    // 权限相关
    case ActionTypes.SET_PERMISSIONS:
      return {
        ...state,
        permissions: action.payload,
        permissionMap: createMap(action.payload),
      };

    case ActionTypes.SET_PERMISSION_CATEGORIES:
      return {
        ...state,
        permissionCategories: action.payload,
      };

    case ActionTypes.SET_CURRENT_USER_PERMISSIONS:
      return {
        ...state,
        currentUserPermissions: action.payload,
      };

    case ActionTypes.SET_CURRENT_USER_ROLES:
      return {
        ...state,
        currentUserRoles: action.payload,
      };

    // 品牌相关
    case ActionTypes.SET_BRANDS:
      return {
        ...state,
        brands: action.payload,
        brandMap: createMap(action.payload),
      };

    // 供应商相关
    case ActionTypes.SET_SUPPLIERS:
      return {
        ...state,
        suppliers: action.payload,
        supplierMap: createMap(action.payload),
      };

    // 项目相关
    case ActionTypes.SET_PROJECTS:
      return {
        ...state,
        projects: action.payload,
        projectMap: createMap(action.payload),
      };

    default:
      return state;
  }
};

// 创建 Context
const StoreContext = createContext();

// Store Provider 组件
export const StoreProvider = ({
  children,
  preloadedData = null,
  onInitialized = null,
  onAuthError = null,
}) => {
  const [state, dispatch] = useReducer(storeReducer, createInitialState(preloadedData));
  const initializationRef = useRef(false); // 防止重复初始化

  // 异步初始化基础数据
  useEffect(() => {
    // 如果已经有预加载数据或已经初始化过，则跳过
    if (preloadedData || initializationRef.current) {
      // 如果有预加载数据，立即通知初始化完成
      if (preloadedData && onInitialized) {
        onInitialized();
      }
      return;
    }

    const initializeBaseData = async () => {
      try {
        console.log('🚀 开始并行加载基础数据...');
        initializationRef.current = true; // 标记为已开始初始化

        // 🚀 并行加载所有基础数据
        const [
          departmentResult,
          roleResult,
          permissionResult,
          currentUserResult,
          userPermissionResult,
        ] = await Promise.allSettled([
          // 1. 获取部门数据
          departmentAPI.getDepartments().catch((error) => {
            // 检查是否是401错误
            if (error.message.includes('401') || error.message.includes('登录已过期')) {
              throw new Error('AUTH_ERROR');
            }
            return {
              success: false,
              error: error.message,
              data: [],
            };
          }),

          // 2. 获取角色数据
          roleApi.getRoles({ pageSize: 1000 }).catch((error) => {
            if (error.message.includes('401') || error.message.includes('登录已过期')) {
              throw new Error('AUTH_ERROR');
            }
            return {
              success: false,
              error: error.message,
              data: { roles: [] },
            };
          }),

          // 3. 获取权限数据
          permissionApi.getPermissions({ pageSize: 1000 }).catch((error) => {
            if (error.message.includes('401') || error.message.includes('登录已过期')) {
              throw new Error('AUTH_ERROR');
            }
            return {
              success: false,
              error: error.message,
              data: { permissions: [] },
            };
          }),
          userApi.getCurrentUserInfo().catch((error) => {
            if (error.message.includes('401') || error.message.includes('登录已过期')) {
              throw new Error('AUTH_ERROR');
            }
            return {
              success: false,
              error: error.message,
              data: { permissions: [], roles: [] },
            };
          }),

          // 4. 获取当前用户权限
          userApi.getCurrentUserPermissions().catch((error) => {
            if (error.message.includes('401') || error.message.includes('登录已过期')) {
              throw new Error('AUTH_ERROR');
            }
            return {
              success: false,
              error: error.message,
              data: null,
            };
          }),
        ]);

        // 检查是否有认证错误
        const authErrors = [departmentResult, roleResult, permissionResult, userPermissionResult]
          .filter((result) => result.status === 'rejected' && result.reason?.message === 'AUTH_ERROR');

        if (authErrors.length > 0) {
          console.warn('🔐 检测到认证错误，需要重新登录');
          if (onAuthError) {
            onAuthError();
            return; // 不继续初始化
          }
        }

        // 处理部门数据
        let departments = [];
        if (departmentResult.status === 'fulfilled' && departmentResult.value.success) {
          const deptResponse = departmentResult.value;
          departments = Array.isArray(deptResponse.data)
            ? deptResponse.data
            : deptResponse.data.list || [];
          console.log('✅ 获取部门数据:', departments.length, '个部门');
        } else {
          console.warn('⚠️ 获取部门数据失败:', departmentResult.reason?.message || '未知错误');
        }

        // 处理角色数据
        let roles = [];
        if (roleResult.status === 'fulfilled' && roleResult.value.success) {
          const roleResponse = roleResult.value;
          roles = Array.isArray(roleResponse.data)
            ? roleResponse.data
            : roleResponse.data.roles || [];
          console.log('✅ 获取角色数据:', roles.length, '个角色');
        } else {
          console.warn('⚠️ 获取角色数据失败:', roleResult.reason?.message || '未知错误');
        }

        console.log('[ currentUserResult ] >', currentUserResult);
        let currentUserInfo = null;
        if (currentUserResult.status === 'fulfilled' && currentUserResult.value.success) {
          const currentResponse = currentUserResult.value;
          currentUserInfo = currentResponse.data.user;

          console.log('✅ 获取用户数据:', currentUserInfo);
        }

        // 处理权限数据
        let permissions = [];
        if (permissionResult.status === 'fulfilled' && permissionResult.value.success) {
          const permResponse = permissionResult.value;
          permissions = Array.isArray(permResponse.data)
            ? permResponse.data
            : permResponse.data.permissions || [];
          console.log('✅ 获取权限数据:', permissions.length, '个权限');
        } else {
          console.warn('⚠️ 获取权限数据失败:', permissionResult.reason?.message || '未知错误');
        }

        // 处理用户权限数据
        let currentUserPermissions = [];
        let currentUserRoles = [];
        if (userPermissionResult.status === 'fulfilled' && userPermissionResult.value.success) {
          const userPermResponse = userPermissionResult.value;
          currentUserPermissions = userPermResponse.data.permissions || [];
          currentUserRoles = userPermResponse.data.roles || [];
          console.log('✅ 获取用户权限:', currentUserPermissions.length, '个权限');
          console.log('✅ 获取用户角色:', currentUserRoles.length, '个角色');
        } else {
          console.warn('⚠️ 获取用户权限失败:', userPermissionResult.reason?.message || '未知错误');
        }

        // 🎯 批量更新 Store（减少重渲染）
        console.log('📦 批量更新 Store 数据...');

        /**
         * 当前用户数据
         */
        dispatch({
          type: ActionTypes.SET_CURRENT_USER,
          payload: currentUserInfo,
        });

        // 部门数据
        dispatch({
          type: ActionTypes.SET_DEPARTMENTS,
          payload: departments,
        });

        // 角色数据
        dispatch({
          type: ActionTypes.SET_ROLES,
          payload: roles,
        });

        // 权限数据
        dispatch({
          type: ActionTypes.SET_PERMISSIONS,
          payload: permissions,
        });

        // 权限分类
        const permissionCategories = [
          { id: 'system', name: '系统管理', description: '系统相关权限' },
          { id: 'business', name: '业务管理', description: '业务相关权限' },
        ];
        dispatch({
          type: ActionTypes.SET_PERMISSION_CATEGORIES,
          payload: permissionCategories,
        });

        // 用户权限数据
        dispatch({
          type: ActionTypes.SET_CURRENT_USER_ROLES,
          payload: currentUserRoles,
        });
        dispatch({
          type: ActionTypes.SET_CURRENT_USER_PERMISSIONS,
          payload: currentUserPermissions,
        });

        console.log('🎉 基础数据初始化完成！');

        // 通知父组件初始化完成
        if (onInitialized) {
          onInitialized();
        }
      } catch (error) {
        console.error('❌ 基础数据初始化失败:', error);
        dispatch({
          type: ActionTypes.SET_ERROR,
          payload: { key: 'initialization', error: error.message },
        });

        // 即使失败也要通知完成，避免无限等待
        if (onInitialized) {
          onInitialized();
        }
      }
    };

    initializeBaseData();
  }, [onInitialized, preloadedData, onAuthError]); // 添加依赖项

  const value = {
    state,
    dispatch,
    actions: {
      // 设置加载状态
      setLoading: (key, _value) => {
        dispatch({
          type: ActionTypes.SET_LOADING,
          payload: { key, value: _value },
        });
      },

      // 设置错误
      setError: (key, error) => {
        dispatch({
          type: ActionTypes.SET_ERROR,
          payload: { key, error },
        });
      },

      // 用户相关操作
      setUsers: (users) => {
        dispatch({
          type: ActionTypes.SET_USERS,
          payload: users,
        });
      },

      setCurrentUser: (user) => {
        console.log('[ setCurrentUser ] >', user);
        dispatch({
          type: ActionTypes.SET_CURRENT_USER,
          payload: user,
        });
      },

      // 角色相关操作
      setRoles: (roles) => {
        dispatch({
          type: ActionTypes.SET_ROLES,
          payload: roles,
        });
      },

      // 权限相关操作
      setPermissions: (permissions) => {
        dispatch({
          type: ActionTypes.SET_PERMISSIONS,
          payload: permissions,
        });
      },

      setPermissionCategories: (categories) => {
        dispatch({
          type: ActionTypes.SET_PERMISSION_CATEGORIES,
          payload: categories,
        });
      },

      // 部门相关操作
      setDepartments: (departments) => {
        dispatch({
          type: ActionTypes.SET_DEPARTMENTS,
          payload: departments,
        });
      },

      // 品牌相关操作
      setBrands: (brands) => {
        dispatch({
          type: ActionTypes.SET_BRANDS,
          payload: brands,
        });
      },

      // 供应商相关操作
      setSuppliers: (suppliers) => {
        dispatch({
          type: ActionTypes.SET_SUPPLIERS,
          payload: suppliers,
        });
      },

      // 项目相关操作
      setProjects: (projects) => {
        dispatch({
          type: ActionTypes.SET_PROJECTS,
          payload: projects,
        });
      },

      // 清除错误
      clearError: (key) => {
        dispatch({
          type: ActionTypes.SET_ERROR,
          payload: { key, error: null },
        });
      },
    },
  };

  return (
    <StoreContext.Provider value={value}>
      {children}
    </StoreContext.Provider>
  );
};

// 自定义 Hook
export const useStore = () => {
  const context = useContext(StoreContext);
  if (!context) {
    throw new Error('useStore must be used within a StoreProvider');
  }
  return context;
};

export { ActionTypes };
