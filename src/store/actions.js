// 异步操作 actions
import { user<PERSON>pi, roleApi, permissionApi, departmentAPI } from '../services/api';
import { dingApi } from '../services/dingApi';
import { ActionTypes } from './index';

// 用户相关异步操作
export const fetchUsers = (params = {}) => async (dispatch) => {
  dispatch({
    type: ActionTypes.SET_LOADING,
    payload: { key: 'users', value: true },
  });

  try {
    const response = await userApi.getUsers(params);
    if (response.success) {
      dispatch({
        type: ActionTypes.SET_USERS,
        payload: response.data.list || [],
      });
    } else {
      throw new Error(response.message || '获取用户列表失败');
    }
  } catch (error) {
    dispatch({
      type: ActionTypes.SET_ERROR,
      payload: { key: 'users', error: error.message },
    });
    throw error;
  } finally {
    dispatch({
      type: ActionTypes.SET_LOADING,
      payload: { key: 'users', value: false },
    });
  }
};

export const createUser = (userData) => async (dispatch) => {
  try {
    const response = await userApi.createUser(userData);
    if (response.success) {
      dispatch({
        type: ActionTypes.ADD_USER,
        payload: response.data,
      });
      return response.data;
    } else {
      throw new Error(response.message || '创建用户失败');
    }
  } catch (error) {
    dispatch({
      type: ActionTypes.SET_ERROR,
      payload: { key: 'users', error: error.message },
    });
    throw error;
  }
};

export const updateUser = (userId, userData) => async (dispatch) => {
  try {
    const response = await userApi.updateUser(userId, userData);
    if (response.success) {
      dispatch({
        type: ActionTypes.UPDATE_USER,
        payload: { id: userId, ...response.data },
      });
      return response.data;
    } else {
      throw new Error(response.message || '更新用户失败');
    }
  } catch (error) {
    dispatch({
      type: ActionTypes.SET_ERROR,
      payload: { key: 'users', error: error.message },
    });
    throw error;
  }
};

export const deleteUser = (userId) => async (dispatch) => {
  try {
    const response = await userApi.deleteUser(userId);
    if (response.success) {
      dispatch({
        type: ActionTypes.DELETE_USER,
        payload: userId,
      });
    } else {
      throw new Error(response.message || '删除用户失败');
    }
  } catch (error) {
    dispatch({
      type: ActionTypes.SET_ERROR,
      payload: { key: 'users', error: error.message },
    });
    throw error;
  }
};

export const syncDingTalkUsers = (params = {}) => async (dispatch) => {
  dispatch({
    type: ActionTypes.SET_LOADING,
    payload: { key: 'users', value: true },
  });

  try {
    const response = await userApi.syncDingTalkUsers(params);
    if (response.success) {
      // 同步完成后重新获取用户列表
      await fetchUsers()(dispatch);
      return response.data;
    } else {
      throw new Error(response.message || '同步钉钉用户失败');
    }
  } catch (error) {
    dispatch({
      type: ActionTypes.SET_ERROR,
      payload: { key: 'users', error: error.message },
    });
    throw error;
  } finally {
    dispatch({
      type: ActionTypes.SET_LOADING,
      payload: { key: 'users', value: false },
    });
  }
};

// 部门相关异步操作
export const fetchDepartmentUsers = (departmentId, params = {}) => async (dispatch) => {
  try {
    const response = await departmentAPI.getDepartmentUsers({
      deptId: departmentId,
      ...params,
    });
    if (response.success) {
      dispatch({
        type: ActionTypes.SET_DEPARTMENT_USERS,
        payload: {
          departmentId,
          users: response.data || [],
        },
      });
      return response.data;
    } else {
      throw new Error(response.message || '获取部门用户失败');
    }
  } catch (error) {
    dispatch({
      type: ActionTypes.SET_ERROR,
      payload: { key: 'departments', error: error.message },
    });
    throw error;
  }
};

// 角色相关异步操作
export const fetchRoles = (params = {}) => async (dispatch) => {
  dispatch({
    type: ActionTypes.SET_LOADING,
    payload: { key: 'roles', value: true },
  });

  try {
    const response = await roleApi.getRoles(params);
    if (response.success) {
      dispatch({
        type: ActionTypes.SET_ROLES,
        payload: response.data.list || [],
      });
    } else {
      throw new Error(response.message || '获取角色列表失败');
    }
  } catch (error) {
    dispatch({
      type: ActionTypes.SET_ERROR,
      payload: { key: 'roles', error: error.message },
    });
    throw error;
  } finally {
    dispatch({
      type: ActionTypes.SET_LOADING,
      payload: { key: 'roles', value: false },
    });
  }
};

export const createRole = (roleData) => async (dispatch) => {
  try {
    const response = await roleApi.createRole(roleData);
    if (response.success) {
      dispatch({
        type: ActionTypes.ADD_ROLE,
        payload: response.data,
      });
      return response.data;
    } else {
      throw new Error(response.message || '创建角色失败');
    }
  } catch (error) {
    dispatch({
      type: ActionTypes.SET_ERROR,
      payload: { key: 'roles', error: error.message },
    });
    throw error;
  }
};

export const updateRole = (roleId, roleData) => async (dispatch) => {
  try {
    const response = await roleApi.updateRole(roleId, roleData);
    if (response.success) {
      dispatch({
        type: ActionTypes.UPDATE_ROLE,
        payload: { id: roleId, ...response.data },
      });
      return response.data;
    } else {
      throw new Error(response.message || '更新角色失败');
    }
  } catch (error) {
    dispatch({
      type: ActionTypes.SET_ERROR,
      payload: { key: 'roles', error: error.message },
    });
    throw error;
  }
};

export const deleteRole = (roleId) => async (dispatch) => {
  try {
    const response = await roleApi.deleteRole(roleId);
    if (response.success) {
      dispatch({
        type: ActionTypes.DELETE_ROLE,
        payload: roleId,
      });
    } else {
      throw new Error(response.message || '删除角色失败');
    }
  } catch (error) {
    dispatch({
      type: ActionTypes.SET_ERROR,
      payload: { key: 'roles', error: error.message },
    });
    throw error;
  }
};

// 权限相关异步操作
export const fetchPermissions = (params = {}) => async (dispatch) => {
  dispatch({
    type: ActionTypes.SET_LOADING,
    payload: { key: 'permissions', value: true },
  });

  try {
    const response = await permissionApi.getPermissions(params);
    if (response.success) {
      dispatch({
        type: ActionTypes.SET_PERMISSIONS,
        payload: response.data || [],
      });
    } else {
      throw new Error(response.message || '获取权限列表失败');
    }
  } catch (error) {
    dispatch({
      type: ActionTypes.SET_ERROR,
      payload: { key: 'permissions', error: error.message },
    });
    throw error;
  } finally {
    dispatch({
      type: ActionTypes.SET_LOADING,
      payload: { key: 'permissions', value: false },
    });
  }
};

export const fetchPermissionCategories = () => async (dispatch) => {
  try {
    const response = await permissionApi.getPermissionCategories();
    if (response.success) {
      dispatch({
        type: ActionTypes.SET_PERMISSION_CATEGORIES,
        payload: response.data || [],
      });
    } else {
      throw new Error(response.message || '获取权限分类失败');
    }
  } catch (error) {
    dispatch({
      type: ActionTypes.SET_ERROR,
      payload: { key: 'permissions', error: error.message },
    });
    throw error;
  }
};

// 初始化数据的复合操作
export const initializeStore = () => async (dispatch) => {
  try {
    // 并行加载基础数据
    await Promise.all([
      fetchUsers()(dispatch),
      fetchRoles()(dispatch),
      fetchPermissions()(dispatch),
      fetchPermissionCategories()(dispatch),
    ]);
  } catch (error) {
    console.error('Store initialization failed:', error);
  }
};

// 清除错误
export const clearError = (key) => (dispatch) => {
  dispatch({
    type: ActionTypes.SET_ERROR,
    payload: { key, error: null },
  });
};


export const fetchSpaceId = (state = {}) => async (dispatch) => {
  if (state.spaceId) {
    return state.spaceId;
  }
  try {
    const response = await dingApi.getProcessInstanceCspaceSpace();
    if (response.success) {
      dispatch({
        type: ActionTypes.SET_SPACE_ID,
        payload: response.data.spaceId,
      });
      return response.data.spaceId;
    } else {
      throw new Error(response.message || '获取空间ID失败');
    }
  } catch (error) {
    console.error('Fetch space ID failed:', error);
    throw error;
  }
};
