import React from 'react';
import { 
  useUsers, 
  useDepartments, 
  useRoles, 
  usePermissions,
  useNameMappings, 
  useStats 
} from './hooks';

/**
 * Store Consumer 组件
 * 使用 render props 模式为类组件提供 store 数据
 * 
 * 使用方式：
 * 
 * class MyComponent extends Component {
 *   render() {
 *     return (
 *       <StoreConsumer>
 *         {(store) => (
 *           <div>
 *             <p>用户数量: {store.users.length}</p>
 *             <p>部门名称: {store.getDepartmentName(1)}</p>
 *           </div>
 *         )}
 *       </StoreConsumer>
 *     );
 *   }
 * }
 */
const StoreConsumer = ({ children }) => {
  // 获取所有 store hooks 的数据
  const users = useUsers();
  const departments = useDepartments();
  const roles = useRoles();
  const permissions = usePermissions();
  const nameMappings = useNameMappings();
  const stats = useStats();

  // 将所有 store 数据合并到一个对象中
  const store = {
    // 用户相关
    users: users.users,
    userMap: users.userMap,
    usersLoading: users.loading,
    usersError: users.error,
    fetchUsers: users.fetchUsers,
    createUser: users.createUser,
    updateUser: users.updateUser,
    deleteUser: users.deleteUser,
    searchUsers: users.searchUsers,
    getUsersByDepartment: users.getUsersByDepartment,
    getUsersByRole: users.getUsersByRole,
    
    // 部门相关
    departments: departments.departments,
    departmentMap: departments.departmentMap,
    getDepartmentName: departments.getDepartmentName,
    fetchDepartments: departments.fetchDepartments,
    
    // 角色相关
    roles: roles.roles,
    roleMap: roles.roleMap,
    getRoleName: roles.getRoleName,
    fetchRoles: roles.fetchRoles,
    createRole: roles.createRole,
    updateRole: roles.updateRole,
    deleteRole: roles.deleteRole,
    
    // 权限相关
    permissions: permissions.permissions,
    permissionMap: permissions.permissionMap,
    getPermissionName: permissions.getPermissionName,
    fetchPermissions: permissions.fetchPermissions,
    createPermission: permissions.createPermission,
    updatePermission: permissions.updatePermission,
    deletePermission: permissions.deletePermission,
    
    // 名称映射
    getUserName: nameMappings.getUserName,
    getBrandName: nameMappings.getBrandName,
    getSupplierName: nameMappings.getSupplierName,
    getProjectName: nameMappings.getProjectName,
    
    // 统计数据
    userStats: stats.userStats,
    roleStats: stats.roleStats,
    departmentStats: stats.departmentStats,
  };

  // 使用 render props 模式
  return children(store);
};

/**
 * 简化版本的 Store Consumer
 * 只提供基础的数据访问功能
 */
export const BasicStoreConsumer = ({ children }) => {
  const { users } = useUsers();
  const { departments, getDepartmentName } = useDepartments();
  const { roles, getRoleName } = useRoles();
  const { getUserName } = useNameMappings();

  const basicStore = {
    users,
    departments,
    roles,
    getDepartmentName,
    getRoleName,
    getUserName,
  };

  return children(basicStore);
};

export default StoreConsumer;
