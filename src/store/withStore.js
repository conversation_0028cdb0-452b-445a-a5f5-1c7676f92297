import React from 'react';
import {
  useUsers,
  useDepartments,
  useRoles,
  usePermissions,
  useNameMappings,
  useStats,
  useLoading,
  useErrors,
} from './hooks';

/**
 * 高阶组件：为类组件提供 store 功能
 * 使用方式：
 *
 * class MyComponent extends Component {
 *   render() {
 *     const { users, getDepartmentName } = this.props.store;
 *     // 使用 store 数据
 *   }
 * }
 *
 * export default withStore(MyComponent);
 */
export const withStore = (WrappedComponent) => {
  const WithStoreComponent = (props) => {
    // 获取所有 store hooks 的数据
    const users = useUsers();
    const departments = useDepartments();
    const roles = useRoles();
    const permissions = usePermissions();
    const nameMappings = useNameMappings();
    const stats = useStats();
    const loading = useLoading();
    const errors = useErrors();

    // 将所有 store 数据合并到一个对象中
    const store = {
      // 用户相关
      ...users,

      // 部门相关
      departments: departments.departments,
      departmentMap: departments.departmentMap,
      getDepartmentName: departments.getDepartmentName,
      fetchDepartments: departments.fetchDepartments,

      // 角色相关
      roles: roles.roles,
      roleMap: roles.roleMap,
      getRoleName: roles.getRoleName,
      fetchRoles: roles.fetchRoles,
      createRole: roles.createRole,
      updateRole: roles.updateRole,
      deleteRole: roles.deleteRole,

      // 权限相关
      permissions: permissions.permissions,
      permissionMap: permissions.permissionMap,
      getPermissionName: permissions.getPermissionName,
      fetchPermissions: permissions.fetchPermissions,
      createPermission: permissions.createPermission,
      updatePermission: permissions.updatePermission,
      deletePermission: permissions.deletePermission,

      // 名称映射
      ...nameMappings,

      // 统计数据
      ...stats,

      // 加载状态
      ...loading,

      // 错误处理
      ...errors,
    };

    // 将 store 作为 prop 传递给被包装的组件
    return <WrappedComponent {...props} store={store} />;
  };

  // 设置显示名称，便于调试
  WithStoreComponent.displayName = `withStore(${WrappedComponent.displayName || WrappedComponent.name})`;

  return WithStoreComponent;
};

/**
 * 简化版本：只提供基础的数据访问功能
 */
export const withBasicStore = (WrappedComponent) => {
  const WithBasicStoreComponent = (props) => {
    const { users } = useUsers();
    const { departments, getDepartmentName } = useDepartments();
    const { roles, getRoleName } = useRoles();
    const { getUserName } = useNameMappings();

    const basicStore = {
      users,
      departments,
      roles,
      getDepartmentName,
      getRoleName,
      getUserName,
    };

    return <WrappedComponent {...props} store={basicStore} />;
  };

  WithBasicStoreComponent.displayName = `withBasicStore(${WrappedComponent.displayName || WrappedComponent.name})`;

  return WithBasicStoreComponent;
};

export default withStore;
