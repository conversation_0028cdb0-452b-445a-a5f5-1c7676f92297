// 资金计划管理相关工具函数
import moment from 'moment';

/**
 * 格式化金额显示
 * @param {number} amount - 金额
 * @returns {string} 格式化后的金额字符串
 */
export const formatCurrency = (amount) => {
  if (!amount && amount !== 0) return '-';
  return `¥${amount.toLocaleString()}`;
};

/**
 * 格式化日期
 * @param {string|Date} date - 日期
 * @param {string} format - 格式
 * @returns {string} 格式化后的日期字符串
 */
export const formatDate = (date, format = 'YYYY-MM-DD') => {
  if (!date) return '-';
  return moment(date).format(format);
};

/**
 * 格式化日期时间
 * @param {string|Date} date - 日期时间
 * @returns {string} 格式化后的日期时间字符串
 */
export const formatDateTime = (date) => {
  if (!date) return '-';
  return moment(date).format('YYYY-MM-DD HH:mm:ss');
};

/**
 * 格式化月份周次显示
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @param {number} weekOfMonth - 月内周次
 * @returns {string} 格式化后的显示文本
 */
export const formatMonthWeek = (year, month, weekOfMonth) => {
  if (!year || !month || !weekOfMonth) return '-';
  return `${year}年${month}月第${weekOfMonth}周`;
};

/**
 * 获取付款状态配置
 * @param {boolean} isPaid - 是否已付款
 * @returns {object} 状态配置对象
 */
export const getPaymentStatusConfig = (isPaid) => {
  return isPaid ? {
    label: '已付款',
    color: '#52c41a',
    tag: 'success',
  } : {
    label: '未付款',
    color: '#faad14',
    tag: 'warning',
  };
};

/**
 * 计算月份内的周数和日期范围
 * @param {number} year - 年份
 * @param {number} month - 月份（1-12）
 * @returns {Array} 周数组，每个元素包含周次、开始日期、结束日期
 */
export const calculateMonthWeeks = (year, month) => {
  const monthStart = moment(`${year}-${String(month).padStart(2, '0')}-01`);
  const monthEnd = monthStart.clone().endOf('month');

  const weeks = [];
  let weekNumber = 1;
  const current = monthStart.clone();

  while (current.isSameOrBefore(monthEnd)) {
    const weekStart = current.clone();
    const weekEnd = current.clone().add(6, 'days');

    // 确保周结束日期不超过月末
    if (weekEnd.isAfter(monthEnd)) {
      weekEnd.set('date', monthEnd.date());
    }

    weeks.push({
      weekOfMonth: weekNumber,
      weekStart: weekStart.format('YYYY-MM-DD'),
      weekEnd: weekEnd.format('YYYY-MM-DD'),
      label: `第${weekNumber}周 (${weekStart.format('DD')}-${weekEnd.format('DD')}日)`,
    });

    current.add(7, 'days');
    weekNumber++;

    // 如果下一周的开始日期已经超过了当月，则停止
    if (current.isAfter(monthEnd)) {
      break;
    }
  }

  return weeks;
};

/**
 * 生成资金计划标题
 * @param {string} projectName - 项目名称
 * @param {number} month - 月份
 * @param {number} weekOfMonth - 周次
 * @returns {string} 资金计划标题
 */
export const generateFundingPlanTitle = (projectName, month, weekOfMonth) => {
  if (!projectName || !month || !weekOfMonth) return '';
  return `${projectName}-${month}月第${weekOfMonth}周资金计划`;
};

/**
 * 验证资金计划数据
 * @param {object} planData - 资金计划数据
 * @returns {object} 验证结果
 */
export const validateFundingPlan = (planData) => {
  const errors = [];

  if (!planData.title || planData.title.trim().length < 2) {
    errors.push('计划标题不能少于2个字符');
  }

  if (!planData.year || planData.year < 2020 || planData.year > 2030) {
    errors.push('请选择有效的年份');
  }

  if (!planData.month || planData.month < 1 || planData.month > 12) {
    errors.push('请选择有效的月份');
  }

  if (!planData.weekOfMonth || planData.weekOfMonth < 1 || planData.weekOfMonth > 6) {
    errors.push('请选择有效的周次');
  }

  if (!planData.amount || planData.amount <= 0) {
    errors.push('资金金额必须大于0');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * 计算资金计划统计信息
 * @param {Array} plans - 资金计划数组
 * @returns {object} 统计信息
 */
export const calculateFundingPlanStats = (plans) => {
  if (!plans || plans.length === 0) {
    return {
      totalPlans: 0,
      totalAmount: 0,
      paidAmount: 0,
      unpaidAmount: 0,
      paidCount: 0,
      unpaidCount: 0,
      paymentRate: 0,
    };
  }

  const totalPlans = plans.length;
  const totalAmount = plans.reduce((sum, plan) => sum + (plan.amount || 0), 0);
  const paidPlans = plans.filter((plan) => plan.isPaid);
  const unpaidPlans = plans.filter((plan) => !plan.isPaid);

  const paidAmount = paidPlans.reduce((sum, plan) => sum + (plan.amount || 0), 0);
  const unpaidAmount = unpaidPlans.reduce((sum, plan) => sum + (plan.amount || 0), 0);

  const paymentRate = totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0;

  return {
    totalPlans,
    totalAmount,
    paidAmount,
    unpaidAmount,
    paidCount: paidPlans.length,
    unpaidCount: unpaidPlans.length,
    paymentRate: Math.round(paymentRate * 100) / 100, // 保留两位小数
  };
};

/**
 * 按月份分组资金计划
 * @param {Array} plans - 资金计划数组
 * @returns {object} 按月份分组的资金计划
 */
export const groupPlansByMonth = (plans) => {
  if (!plans || plans.length === 0) return {};

  return plans.reduce((groups, plan) => {
    const monthKey = `${plan.year}-${String(plan.month).padStart(2, '0')}`;
    if (!groups[monthKey]) {
      groups[monthKey] = [];
    }
    groups[monthKey].push(plan);
    return groups;
  }, {});
};

/**
 * 按付款状态分组资金计划
 * @param {Array} plans - 资金计划数组
 * @returns {object} 按付款状态分组的资金计划
 */
export const groupPlansByPaymentStatus = (plans) => {
  if (!plans || plans.length === 0) return { paid: [], unpaid: [] };

  return plans.reduce((groups, plan) => {
    if (plan.isPaid) {
      groups.paid.push(plan);
    } else {
      groups.unpaid.push(plan);
    }
    return groups;
  }, { paid: [], unpaid: [] });
};

/**
 * 生成月份选项（基于项目执行周期）
 * @param {Array} executionPeriod - 项目执行周期 [startDate, endDate]
 * @returns {Array} 月份选项数组
 */
export const generateMonthOptions = (executionPeriod) => {
  if (!executionPeriod || executionPeriod.length !== 2) return [];

  const startDate = moment(executionPeriod[0]);
  const endDate = moment(executionPeriod[1]);
  const monthOptions = [];

  const current = startDate.clone().startOf('month');
  while (current.isSameOrBefore(endDate, 'month')) {
    monthOptions.push({
      value: current.format('YYYY-MM'),
      label: current.format('YYYY年MM月'),
      year: current.year(),
      month: current.month() + 1,
    });
    current.add(1, 'month');
  }

  return monthOptions;
};

/**
 * 生成指定月份的周期选项
 * @param {number} year - 年份
 * @param {number} month - 月份 (1-12)
 * @returns {Array} 周期选项数组
 */
export const generateWeekOptionsForMonth = (year, month) => {
  const monthStart = moment(`${year}-${String(month).padStart(2, '0')}-01`);
  const monthEnd = monthStart.clone().endOf('month');

  const weekOptions = [];
  let weekNumber = 1;
  const current = monthStart.clone();

  while (current.isSameOrBefore(monthEnd)) {
    const weekStart = current.clone();
    const weekEnd = current.clone().add(6, 'days');

    // 确保周结束日期不超过月末
    if (weekEnd.isAfter(monthEnd)) {
      weekEnd.set('date', monthEnd.date());
    }

    weekOptions.push({
      weekOfMonth: weekNumber,
      weekStart: weekStart.format('YYYY-MM-DD'),
      weekEnd: weekEnd.format('YYYY-MM-DD'),
      label: `第${weekNumber}周 (${weekStart.format('DD')}-${weekEnd.format('DD')}日)`,
    });

    current.add(7, 'days');
    weekNumber++;

    // 如果下一周的开始日期已经超过了当月那就算到最后一天，则停止
    if (current.isAfter(monthEnd)) {
      break;
    }
  }

  return weekOptions;
};

/**
 * 检查资金计划时间是否在项目执行周期内
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @param {number} weekOfMonth - 周次
 * @param {Array} executionPeriod - 项目执行周期
 * @returns {boolean} 是否在执行周期内
 */
export const isWithinExecutionPeriod = (year, month, weekOfMonth, executionPeriod) => {
  if (!executionPeriod || executionPeriod.length !== 2) return true;

  const weeks = calculateMonthWeeks(year, month);
  const targetWeek = weeks.find((week) => week.weekOfMonth === weekOfMonth);

  if (!targetWeek) return false;

  const startDate = moment(executionPeriod[0]);
  const endDate = moment(executionPeriod[1]);
  const weekStart = moment(targetWeek.weekStart);
  const weekEnd = moment(targetWeek.weekEnd);

  return weekStart.isSameOrAfter(startDate, 'day') && weekEnd.isSameOrBefore(endDate, 'day');
};

/**
 * 排序资金计划
 * @param {Array} plans - 资金计划数组
 * @param {string} sortBy - 排序字段
 * @param {string} sortOrder - 排序方向 ('asc' | 'desc')
 * @returns {Array} 排序后的资金计划数组
 */
export const sortFundingPlans = (plans, sortBy = 'createdAt', sortOrder = 'desc') => {
  if (!plans || plans.length === 0) return [];

  return [...plans].sort((a, b) => {
    let aValue = a[sortBy];
    let bValue = b[sortBy];

    // 特殊处理时间字段
    if (sortBy === 'time') {
      aValue = `${a.year}-${String(a.month).padStart(2, '0')}-${a.weekOfMonth}`;
      bValue = `${b.year}-${String(b.month).padStart(2, '0')}-${b.weekOfMonth}`;
    }

    // 处理日期字段
    if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
      aValue = moment(aValue);
      bValue = moment(bValue);
    }

    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });
};
