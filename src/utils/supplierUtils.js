// 供应商管理相关工具函数
import moment from 'moment';

/**
 * 格式化金额显示
 * @param {number} amount - 金额
 * @returns {string} 格式化后的金额字符串
 */
export const formatCurrency = (amount) => {
  if (!amount && amount !== 0) return '-';
  return `¥${amount.toLocaleString()}`;
};

/**
 * 格式化日期
 * @param {string|Date} date - 日期
 * @param {string} format - 格式
 * @returns {string} 格式化后的日期字符串
 */
export const formatDate = (date, format = 'YYYY-MM-DD') => {
  if (!date) return '-';
  return moment(date).format(format);
};

// 供应商状态选项
export const SUPPLIER_STATUS = [
  { value: 'active', label: '启用', color: 'green' },
  { value: 'inactive', label: '禁用', color: 'red' },
  { value: 'pending', label: '待审核', color: 'orange' },
  { value: 'blacklisted', label: '黑名单', color: 'red' },
];

// 服务类型选项
export const SERVICE_TYPES = [
  { value: 'influencer', label: '达人服务' },
  { value: 'advertising', label: '广告投放' },
  { value: 'other', label: '其他服务' },
];

// 税率选项
export const TAX_RATES = [
  { value: 'special_1', label: '专票1%' },
  { value: 'special_3', label: '专票3%' },
  { value: 'special_6', label: '专票6%' },
  { value: 'general', label: '普票' },
];

// 供应商评级选项
export const SUPPLIER_RATINGS = [
  { value: 5, label: '★★★★★' },
  { value: 4, label: '★★★★☆' },
  { value: 3, label: '★★★☆☆' },
  { value: 2, label: '★★☆☆☆' },
  { value: 1, label: '★☆☆☆☆' },
];

/**
 * 获取供应商状态配置
 * @param {string} status - 供应商状态
 * @returns {object} 状态配置对象
 */
export const getSupplierStatusConfig = (status) => {
  return SUPPLIER_STATUS.find((item) => item.value === status) || {
    label: status,
    color: 'default',
  };
};

/**
 * 获取服务类型标签
 * @param {string} type - 服务类型
 * @returns {string} 服务类型标签
 */
export const getServiceTypeLabel = (type) => {
  const serviceType = SERVICE_TYPES.find((item) => item.value === type);
  return serviceType ? serviceType.label : type;
};

/**
 * 获取税率标签
 * @param {string} rate - 税率
 * @returns {string} 税率标签
 */
export const getTaxRateLabel = (rate) => {
  const taxRate = TAX_RATES.find((item) => item.value === rate);
  return taxRate ? taxRate.label : rate;
};

/**
 * 获取评级显示
 * @param {number} rating - 评级
 * @returns {string} 评级显示
 */
export const getRatingDisplay = (rating) => {
  const ratingConfig = SUPPLIER_RATINGS.find((item) => item.value === rating);
  return ratingConfig ? ratingConfig.label : '未评级';
};

/**
 * 验证供应商表单数据
 * @param {object} formData - 表单数据
 * @returns {object} 验证结果
 */
export const validateSupplierForm = (formData) => {
  const errors = {};

  if (!formData.name || formData.name.trim() === '') {
    errors.name = '供应商名称不能为空';
  }

  if (!formData.code || formData.code.trim() === '') {
    errors.code = '供应商编码不能为空';
  }

  if (!formData.contactPerson || formData.contactPerson.trim() === '') {
    errors.contactPerson = '联系人不能为空';
  }

  if (!formData.contactPhone || formData.contactPhone.trim() === '') {
    errors.contactPhone = '联系电话不能为空';
  } else if (!/^1[3-9]\d{9}$/.test(formData.contactPhone)) {
    errors.contactPhone = '请输入正确的手机号码';
  }

  if (formData.contactEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.contactEmail)) {
    errors.contactEmail = '请输入正确的邮箱地址';
  }

  if (!formData.serviceTypes || formData.serviceTypes.length === 0) {
    errors.serviceTypes = '请至少选择一种服务类型';
  }

  if (formData.creditLimit && formData.creditLimit < 0) {
    errors.creditLimit = '信用额度不能为负数';
  }

  if (formData.rating && (formData.rating < 1 || formData.rating > 5)) {
    errors.rating = '评级必须在1-5之间';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

/**
 * 格式化供应商数据用于显示
 * @param {object} supplier - 供应商数据
 * @returns {object} 格式化后的供应商数据
 */
export const formatSupplierForDisplay = (supplier) => {
  return {
    ...supplier,
    statusLabel: getSupplierStatusConfig(supplier.status).label,
    serviceTypesLabel: supplier.serviceTypes?.map((type) => getServiceTypeLabel(type)).join(', ') || '-',
    taxRateLabel: getTaxRateLabel(supplier.preferredTaxRate),
    ratingDisplay: getRatingDisplay(supplier.rating),
    creditLimitDisplay: formatCurrency(supplier.creditLimit),
    createdAtDisplay: formatDate(supplier.createdAt),
    updatedAtDisplay: formatDate(supplier.updatedAt),
  };
};

/**
 * 生成供应商编码
 * @param {string} name - 供应商名称
 * @param {number} index - 序号
 * @returns {string} 供应商编码
 */
export const generateSupplierCode = (name, index = 1) => {
  const prefix = 'SUP';
  const nameCode = name.substring(0, 2).toUpperCase();
  const numberCode = String(index).padStart(3, '0');
  return `${prefix}${nameCode}${numberCode}`;
};

/**
 * 检查供应商编码是否唯一
 * @param {string} code - 供应商编码
 * @param {array} existingSuppliers - 现有供应商列表
 * @param {string} excludeId - 排除的供应商ID（编辑时使用）
 * @returns {boolean} 是否唯一
 */
export const isSupplierCodeUnique = (code, existingSuppliers, excludeId = null) => {
  return !existingSuppliers.some((supplier) =>
    supplier.code === code && supplier.id !== excludeId);
};

/**
 * 过滤供应商列表
 * @param {array} suppliers - 供应商列表
 * @param {object} filters - 过滤条件
 * @returns {array} 过滤后的供应商列表
 */
export const filterSuppliers = (suppliers, filters) => {
  return suppliers.filter((supplier) => {
    // 关键词搜索
    if (filters.keyword) {
      const keyword = filters.keyword.toLowerCase();
      const searchFields = [
        supplier.name,
        supplier.shortName,
        supplier.contactPerson,
        supplier.contactPhone,
        supplier.contactEmail,
      ].filter(Boolean);

      const matchKeyword = searchFields.some((field) =>
        field.toLowerCase().includes(keyword));

      if (!matchKeyword) return false;
    }

    // 状态过滤
    if (filters.status && supplier.status !== filters.status) {
      return false;
    }

    // 服务类型过滤
    if (filters.serviceType && !supplier.serviceTypes?.includes(filters.serviceType)) {
      return false;
    }

    return true;
  });
};

/**
 * 排序供应商列表
 * @param {array} suppliers - 供应商列表
 * @param {string} sortBy - 排序字段
 * @param {string} sortOrder - 排序方向
 * @returns {array} 排序后的供应商列表
 */
export const sortSuppliers = (suppliers, sortBy, sortOrder = 'asc') => {
  return [...suppliers].sort((a, b) => {
    let aValue = a[sortBy];
    let bValue = b[sortBy];

    // 处理日期字段
    if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
      aValue = new Date(aValue);
      bValue = new Date(bValue);
    }

    // 处理数字字段
    if (sortBy === 'rating' || sortBy === 'creditLimit') {
      aValue = Number(aValue) || 0;
      bValue = Number(bValue) || 0;
    }

    // 处理字符串字段
    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    if (aValue < bValue) {
      return sortOrder === 'asc' ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortOrder === 'asc' ? 1 : -1;
    }
    return 0;
  });
};
