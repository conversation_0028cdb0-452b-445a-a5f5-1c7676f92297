// 项目管理相关工具函数
import moment from 'moment';

/**
 * 计算项目利润
 * @param {number} budget - 项目规划预算
 * @param {number} talentCost - 达人成本
 * @param {number} adCost - 投流成本
 * @param {number} otherCost - 其他成本
 * @param {number} talentRebate - 预估达人返点
 * @returns {number} 项目利润
 */
export const calculateProjectProfit = (budget, talentCost, adCost, otherCost, talentRebate) => {
  const totalCost = (talentCost || 0) + (adCost || 0) + (otherCost || 0);
  const profit = (budget || 0) - totalCost + (talentRebate || 0);
  return profit;
};

/**
 * 计算项目毛利率
 * @param {number} profit - 项目利润
 * @param {number} budget - 项目规划预算
 * @returns {number} 毛利率（百分比）
 */
export const calculateGrossMargin = (profit, budget) => {
  if (!budget || budget === 0) return 0;
  return ((profit / budget) * 100).toFixed(2);
};

/**
 * 格式化金额显示
 * @param {number} amount - 金额
 * @returns {string} 格式化后的金额字符串
 */
export const formatCurrency = (amount) => {
  if (!amount && amount !== 0) return '-';
  return `¥${amount.toLocaleString()}`;
};

/**
 * 格式化百分比显示
 * @param {number} percentage - 百分比数值
 * @returns {string} 格式化后的百分比字符串
 */
export const formatPercentage = (percentage) => {
  if (!percentage && percentage !== 0) return '-';
  return `${percentage.toFixed(2)}%`;
};

// 单据类型选项
export const DOCUMENT_TYPES = [
  { value: 'project_initiation', label: '项目立项表' },
];

// 合同类型选项（对应API枚举）
export const CONTRACT_TYPES = [
  { value: 'ANNUAL_FRAME', label: '年框' },
  { value: 'QUARTERLY_FRAME', label: '季框' },
  { value: 'SINGLE', label: '单次' },
  { value: 'PO_ORDER', label: 'PO单' },
  { value: 'JING_TASK', label: '京任务' },
];

// 项目状态选项（对应API枚举）
export const PROJECT_STATUS = [
  { value: 'DRAFT', label: '草稿' },
  { value: 'ACTIVE', label: '进行中' },
  { value: 'COMPLETED', label: '已完成' },
  { value: 'CANCELLED', label: '已取消' },
];

// 品牌状态选项（对应API枚举）
export const BRAND_STATUS = [
  { value: 'active', label: '启用' },
  { value: 'inactive', label: '禁用' },
];

// 合同签署状态选项（对应API枚举）
export const CONTRACT_SIGNING_STATUS = [
  { value: 'NO_CONTRACT', label: '无合同', color: '#8c8c8c' },
  { value: 'PENDING', label: '待签署', color: '#faad14' },
  { value: 'SIGNING', label: '签署中', color: '#1890ff' },
  { value: 'SIGNED', label: '已签署', color: '#52c41a' },
];

/**
 * 验证表单数据
 * @param {object} formData - 表单数据
 * @returns {object} 验证结果
 */
export const validateProjectForm = (formData) => {
  const errors = {};

  if (!formData.projectName) {
    errors.projectName = '项目名称不能为空';
  }

  if (!formData.documentType) {
    errors.documentType = '请选择单据类型';
  }

  if (!formData.brand) {
    errors.brand = '请选择品牌';
  }

  if (!formData.executionPeriod || !formData.executionPeriod[0] || !formData.executionPeriod[1]) {
    errors.executionPeriod = '请选择项目执行周期';
  }

  if (!formData.planningBudget || formData.planningBudget <= 0) {
    errors.planningBudget = '项目规划预算必须大于0';
  }

  if (!formData.executivePM) {
    errors.executivePM = '请选择执行PM';
  }

  if (!formData.contractType) {
    errors.contractType = '请选择合同类型';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

/**
 * 格式化日期
 * @param {string|Date} date - 日期
 * @param {string} format - 格式
 * @returns {string} 格式化后的日期字符串
 */
export const formatDate = (date, format = 'YYYY-MM-DD') => {
  if (!date) return '-';
  return moment(date).format(format);
};

/**
 * 格式化日期范围
 * @param {string|Date} startDate - 开始日期
 * @param {string|Date} endDate - 结束日期
 * @param {string} format - 格式
 * @returns {string} 格式化后的日期范围字符串
 */
export const formatDateRange = (startDate, endDate, format = 'YYYY-MM-DD') => {
  if (!startDate || !endDate) return '-';
  return `${moment(startDate).format(format)} 至 ${moment(endDate).format(format)}`;
};

/**
 * 获取项目状态配置
 * @param {string} status - 项目状态
 * @returns {object} 状态配置对象
 */
export const getProjectStatusConfig = (status) => {
  return PROJECT_STATUS.find((item) => item.value === status) || {
    label: status,
    color: 'default',
  };
};

/**
 * 获取合同类型标签
 * @param {string} type - 合同类型
 * @returns {string} 合同类型标签
 */
export const getContractTypeLabel = (type) => {
  const contractType = CONTRACT_TYPES.find((item) => item.value === type);
  return contractType ? contractType.label : type;
};

/**
 * 获取合同签署状态配置
 * @param {string} status - 合同签署状态
 * @returns {object} 状态配置对象
 */
export const getContractSigningStatusConfig = (status) => {
  return CONTRACT_SIGNING_STATUS.find((item) => item.value === status) || {
    label: status,
    color: '#8c8c8c',
  };
};

/**
 * 获取合同签署状态标签
 * @param {string} status - 合同签署状态
 * @returns {string} 合同签署状态标签
 */
export const getContractSigningStatusLabel = (status) => {
  const statusConfig = CONTRACT_SIGNING_STATUS.find((item) => item.value === status);
  return statusConfig ? statusConfig.label : status;
};
