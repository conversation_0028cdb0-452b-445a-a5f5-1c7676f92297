// 项目收入管理工具函数

// 收入状态常量
export const REVENUE_STATUS = [
  { value: 'receiving', label: '收款中', color: '#1890ff' },
  // { value: 'confirmed', label: '已确认', color: '#52c41a' },
  // { value: 'invoiced', label: '已开票', color: '#faad14' },
  { value: 'received', label: '已收款', color: '#13c2c2' },
  // { value: 'overdue', label: '逾期', color: '#f5222d' },
  { value: 'cancelled', label: '已取消', color: '#8c8c8c' },
];

// 收入类型常量
export const REVENUE_TYPE = [
  { value: 'influencer_income', label: '达人收入' },
  { value: 'project_income', label: '项目收入' },
  { value: 'other', label: '其他' },
];

// 获取状态标签配置
export const getStatusConfig = (status) => {
  return REVENUE_STATUS.find((item) => item.value === status) || { label: status, color: '#666' };
};

// 获取类型标签
export const getTypeLabel = (type) => {
  const typeConfig = REVENUE_TYPE.find((item) => item.value === type);
  return typeConfig ? typeConfig.label : type;
};

// 格式化金额
export const formatAmount = (amount) => {
  if (amount === null || amount === undefined) return '-';
  return `¥${Number(amount).toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
};

// 格式化日期
export const formatDate = (date) => {
  if (!date) return '-';
  return new Date(date).toLocaleDateString('zh-CN');
};

// 计算收入完成率
export const calculateCompletionRate = (actualAmount, plannedAmount) => {
  if (!plannedAmount || plannedAmount === 0) return 0;
  if (!actualAmount) return 0;
  return Math.round((actualAmount / plannedAmount) * 100);
};

// 判断是否逾期
export const isOverdue = (plannedDate, status) => {
  if (status === 'received' || status === 'cancelled') return false;
  const today = new Date();
  const planned = new Date(plannedDate);
  return planned < today;
};

// 获取状态流转选项
export const getNextStatusOptions = (currentStatus) => {
  const statusFlow = {
    planned: ['confirmed', 'cancelled'],
    confirmed: ['invoiced', 'cancelled'],
    invoiced: ['received', 'overdue'],
    overdue: ['received', 'cancelled'],
    received: [], // 已收款不能再变更
    cancelled: [], // 已取消不能再变更
  };

  return statusFlow[currentStatus] || [];
};

// 验证收入数据
export const validateRevenueData = (data) => {
  const errors = {};

  if (!data.title || data.title.trim() === '') {
    errors.title = '请输入收入标题';
  }

  if (!data.revenueType) {
    errors.revenueType = '请选择收入类型';
  }

  if (!data.plannedAmount || data.plannedAmount <= 0) {
    errors.plannedAmount = '请输入有效的预计收入金额';
  }

  // if (!data.plannedDate) {
  //   errors.plannedDate = '请选择预计收入时间';
  // }

  // if (data.actualAmount && data.actualAmount < 0) {
  //   errors.actualAmount = '实际收入金额不能为负数';
  // }

  // if (data.invoiceAmount && data.invoiceAmount < 0) {
  //   errors.invoiceAmount = '开票金额不能为负数';
  // }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

// 生成收入统计数据
export const generateRevenueStats = (revenues) => {
  const stats = {
    totalPlanned: 0,
    totalActual: 0,
    totalInvoiced: 0,
    totalReceived: 0,
    byStatus: {},
    byType: {},
    completionRate: 0,
  };

  revenues.forEach((revenue) => {
    // 总计统计
    stats.totalPlanned += revenue.plannedAmount || 0;
    stats.totalActual += revenue.actualAmount || 0;
    stats.totalInvoiced += revenue.invoiceAmount || 0;

    if (revenue.status === 'received') {
      stats.totalReceived += revenue.actualAmount || 0;
    }

    // 按状态统计
    if (!stats.byStatus[revenue.status]) {
      stats.byStatus[revenue.status] = { count: 0, amount: 0 };
    }
    stats.byStatus[revenue.status].count += 1;
    stats.byStatus[revenue.status].amount += revenue.actualAmount || revenue.plannedAmount || 0;

    // 按类型统计
    if (!stats.byType[revenue.revenueType]) {
      stats.byType[revenue.revenueType] = { count: 0, amount: 0 };
    }
    stats.byType[revenue.revenueType].count += 1;
    stats.byType[revenue.revenueType].amount += revenue.actualAmount || revenue.plannedAmount || 0;
  });

  // 计算完成率
  if (stats.totalPlanned > 0) {
    stats.completionRate = Math.round((stats.totalReceived / stats.totalPlanned) * 100);
  }

  return stats;
};

// 排序选项
export const SORT_OPTIONS = [
  { value: 'plannedDate', label: '预计收入时间' },
  { value: 'plannedAmount', label: '预计收入金额' },
  { value: 'actualAmount', label: '实际收入金额' },
  { value: 'createdAt', label: '创建时间' },
];

// 导出数据格式化
export const formatRevenueForExport = (revenues) => {
  return revenues.map((revenue) => ({
    收入标题: revenue.title,
    收入类型: getTypeLabel(revenue.revenueType),
    收入状态: getStatusConfig(revenue.status).label,
    预计金额: formatAmount(revenue.plannedAmount),
    实际金额: formatAmount(revenue.actualAmount),
    开票金额: formatAmount(revenue.invoiceAmount),
    预计时间: formatDate(revenue.plannedDate),
    确认时间: formatDate(revenue.confirmedDate),
    开票时间: formatDate(revenue.invoiceDate),
    收款时间: formatDate(revenue.receivedDate),
    里程碑: revenue.milestone || '-',
    发票号码: revenue.invoiceNumber || '-',
    付款条件: revenue.paymentTerms || '-',
    备注: revenue.notes || '-',
  }));
};
