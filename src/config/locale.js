// 国际化配置文件
import moment from 'moment';
import 'moment/locale/zh-cn';
import zhCN from 'antd/es/locale/zh_CN';

// 设置 moment.js 全局中文本地化
moment.locale('zh-cn');

// 完善的 Ant Design 中文本地化配置
export const antdLocale = {
  ...zhCN,
  // 确保 DatePicker 相关的本地化文本
  DatePicker: {
    ...zhCN.DatePicker,
    placeholder: '请选择日期',
    rangePlaceholder: ['开始日期', '结束日期'],
    monthPlaceholder: '请选择月份',
    yearPlaceholder: '请选择年份',
    weekPlaceholder: '请选择周',
    timePlaceholder: '请选择时间',
  },
  // 确保 TimePicker 相关的本地化文本
  TimePicker: {
    ...zhCN.TimePicker,
    placeholder: '请选择时间',
  },
  // 确保 Select 相关的本地化文本
  Select: {
    ...zhCN.Select,
    notFoundContent: '无匹配结果',
    // 移除全局placeholder设置，让各个组件自己设置placeholder
  },
  // 确保 Input 相关的本地化文本
  Input: {
    ...zhCN.Input,
    // 移除全局placeholder设置，让各个组件自己设置placeholder
  },
  // 确保 Table 相关的本地化文本
  Table: {
    ...zhCN.Table,
    filterTitle: '筛选',
    filterConfirm: '确定',
    filterReset: '重置',
    selectAll: '全选当页',
    selectInvert: '反选当页',
    emptyText: '暂无数据',
  },
  // 确保 Pagination 相关的本地化文本
  Pagination: {
    ...zhCN.Pagination,
    items_per_page: '条/页',
    jump_to: '跳至',
    jump_to_confirm: '确定',
    page: '页',
    prev_page: '上一页',
    next_page: '下一页',
    prev_5: '向前 5 页',
    next_5: '向后 5 页',
    prev_3: '向前 3 页',
    next_3: '向后 3 页',
  },
};

// 自定义中文文本配置
export const customLocale = {
  // 通用文本
  common: {
    confirm: '确认',
    cancel: '取消',
    save: '保存',
    edit: '编辑',
    delete: '删除',
    add: '添加',
    search: '搜索',
    reset: '重置',
    submit: '提交',
    loading: '加载中...',
    noData: '暂无数据',
    total: '共 {total} 条记录',
    selected: '已选择 {count} 项',
  },

  // 日期相关文本
  date: {
    today: '今天',
    yesterday: '昨天',
    thisWeek: '本周',
    thisMonth: '本月',
    thisYear: '今年',
    selectDate: '选择日期',
    selectTime: '选择时间',
    selectDateTime: '选择日期时间',
    startDate: '开始日期',
    endDate: '结束日期',
    dateRange: '日期范围',
    timeRange: '时间范围',
  },

  // 表单验证文本
  validation: {
    required: '此字段为必填项',
    email: '请输入有效的邮箱地址',
    phone: '请输入有效的手机号码',
    number: '请输入有效的数字',
    min: '最小值为 {min}',
    max: '最大值为 {max}',
    minLength: '最少输入 {min} 个字符',
    maxLength: '最多输入 {max} 个字符',
  },

  // 项目管理相关文本
  project: {
    projectName: '项目名称',
    projectStatus: '项目状态',
    projectBudget: '项目预算',
    executionPeriod: '执行周期',
    createProject: '新建项目',
    editProject: '编辑项目',
    deleteProject: '删除项目',
    projectList: '项目列表',
    projectDetail: '项目详情',
  },

  // 品牌管理相关文本
  brand: {
    brandName: '品牌名称',
    brandStatus: '品牌状态',
    createBrand: '新建品牌',
    editBrand: '编辑品牌',
    deleteBrand: '删除品牌',
    brandList: '品牌列表',
    brandDetail: '品牌详情',
  },

  // 供应商管理相关文本
  supplier: {
    supplierName: '供应商名称',
    supplierStatus: '供应商状态',
    createSupplier: '新建供应商',
    editSupplier: '编辑供应商',
    deleteSupplier: '删除供应商',
    supplierList: '供应商列表',
    supplierDetail: '供应商详情',
  },
};

// 日期格式配置
export const dateFormats = {
  date: 'YYYY-MM-DD',
  datetime: 'YYYY-MM-DD HH:mm:ss',
  time: 'HH:mm:ss',
  month: 'YYYY-MM',
  year: 'YYYY',
  week: 'YYYY-wo',
  quarter: 'YYYY-Q',
};

// 数字格式配置
export const numberFormats = {
  currency: {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  },
  percent: {
    style: 'percent',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  },
  decimal: {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  },
};

// 工具函数
export const localeUtils = {
  // 格式化货币
  formatCurrency: (amount) => {
    if (amount === null || amount === undefined || isNaN(amount)) return '¥0.00';
    return new Intl.NumberFormat('zh-CN', numberFormats.currency).format(amount);
  },

  // 格式化百分比
  formatPercent: (value) => {
    if (value === null || value === undefined || isNaN(value)) return '0.00%';
    return new Intl.NumberFormat('zh-CN', numberFormats.percent).format(value / 100);
  },

  // 格式化日期
  formatDate: (date, format = dateFormats.date) => {
    if (!date) return '-';
    return moment(date).format(format);
  },

  // 格式化日期范围
  formatDateRange: (startDate, endDate, format = dateFormats.date) => {
    if (!startDate || !endDate) return '-';
    return `${moment(startDate).format(format)} 至 ${moment(endDate).format(format)}`;
  },

  // 获取相对时间
  getRelativeTime: (date) => {
    if (!date) return '-';
    return moment(date).fromNow();
  },

  // 获取文本（支持模板变量）
  getText: (key, variables = {}) => {
    const keys = key.split('.');
    let text = customLocale;

    for (const k of keys) {
      if (text && typeof text === 'object' && k in text) {
        text = text[k];
      } else {
        return key; // 如果找不到对应的文本，返回原始key
      }
    }

    if (typeof text === 'string') {
      // 替换模板变量
      return text.replace(/\{(\w+)\}/g, (match, varName) => {
        return variables[varName] !== undefined ? variables[varName] : match;
      });
    }

    return key;
  },
};

export default {
  antdLocale,
  customLocale,
  dateFormats,
  numberFormats,
  localeUtils,
};
