import React, { Component } from 'react';
import {
  Modal,
  Form,
  Input,
  // Select,
  message,
  Row,
  Col,
  AutoComplete,
} from 'antd';
import { permissionApi } from '../../services/api';

// const { Option } = Select;
const { TextArea } = Input;

class PermissionFormModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      categoryOptions: [],
      resourceOptions: [],
      actionOptions: [],
    };
  }

  componentDidMount() {
    this.initializeOptions();
  }

  // 初始化选项数据
  initializeOptions = () => {
    const { categories } = this.props;

    // 权限分类选项
    const categoryOptions = categories.map((category) => ({
      value: category,
      text: category,
    }));

    // 常用资源选项
    const resourceOptions = [
      { value: 'project', text: '项目' },
      { value: 'brand', text: '品牌' },
      { value: 'supplier', text: '供应商' },
      { value: 'user', text: '用户' },
      { value: 'role', text: '角色' },
      { value: 'permission', text: '权限' },
      { value: 'revenue', text: '收入' },
      { value: 'budget', text: '预算' },
      { value: 'report', text: '报表' },
      { value: 'system', text: '系统' },
    ];

    // 常用操作选项
    const actionOptions = [
      { value: 'view', text: '查看' },
      { value: 'create', text: '创建' },
      { value: 'edit', text: '编辑' },
      { value: 'delete', text: '删除' },
      { value: 'export', text: '导出' },
      { value: 'import', text: '导入' },
      { value: 'approve', text: '审批' },
      { value: 'manage', text: '管理' },
    ];

    this.setState({
      categoryOptions,
      resourceOptions,
      actionOptions,
    });
  };

  // 表单提交
  handleSubmit = () => {
    const { form, permission, onSuccess } = this.props;

    form.validateFields(async (err, values) => {
      if (err) {
        message.error('请检查表单填写是否正确');
        return;
      }

      this.setState({ loading: true });

      try {
        let response;
        if (permission) {
          // 编辑权限
          response = await permissionApi.updatePermission(permission.id, values);
        } else {
          // 新增权限
          response = await permissionApi.createPermission(values);
        }

        if (response.success) {
          onSuccess();
        } else {
          message.error(response.message || '操作失败');
        }
      } catch (error) {
        console.error('Permission form submit failed:', error);
        message.error('操作失败');
      } finally {
        this.setState({ loading: false });
      }
    });
  };

  // 生成权限编码
  generatePermissionCode = (category, resource, action) => {
    if (!category || !resource || !action) return '';

    return `${category.toLowerCase()}:${resource.toLowerCase()}:${action.toLowerCase()}`;
  };

  // 权限信息变化时自动生成编码
  handlePermissionInfoChange = () => {
    const { form, permission } = this.props;

    // 只有新增时才自动生成编码
    if (!permission) {
      setTimeout(() => {
        const values = form.getFieldsValue(['category', 'resource', 'action']);
        if (values.category && values.resource && values.action) {
          const code = this.generatePermissionCode(
            values.category,
            values.resource,
            values.action,
          );
          form.setFieldsValue({ code });
        }
      }, 100);
    }
  };

  render() {
    const { visible, permission, onCancel, form } = this.props;
    const { loading, categoryOptions, resourceOptions, actionOptions } = this.state;
    const { getFieldDecorator } = form;

    const isEdit = !!permission;

    return (
      <Modal
        title={isEdit ? '编辑权限' : '新增权限'}
        visible={visible}
        onOk={this.handleSubmit}
        onCancel={onCancel}
        confirmLoading={loading}
        width={600}
        destroyOnClose
      >
        <Form layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="权限名称">
                {getFieldDecorator('name', {
                  initialValue: permission?.name || '',
                  rules: [
                    { required: true, message: '请输入权限名称' },
                    { max: 50, message: '权限名称不能超过50个字符' },
                  ],
                })(<Input placeholder="请输入权限名称" />)}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="权限编码">
                {getFieldDecorator('code', {
                  initialValue: permission?.code || '',
                  rules: [
                    { required: true, message: '请输入权限编码' },
                    { max: 100, message: '权限编码不能超过100个字符' },
                    {
                      pattern: /^[a-z0-9:_-]+$/,
                      message: '权限编码只能包含小写字母、数字、冒号、下划线和横线',
                    },
                  ],
                })(
                  <Input
                    placeholder="请输入权限编码"
                    disabled={isEdit} // 编辑时不允许修改编码
                  />,
                )}
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="权限分类">
                {getFieldDecorator('category', {
                  initialValue: permission?.category || '',
                  rules: [{ required: true, message: '请选择权限分类' }],
                })(
                  <AutoComplete
                    placeholder="请选择或输入权限分类"
                    onChange={this.handlePermissionInfoChange}
                    dataSource={categoryOptions.map((opt) => opt.value)}
                  />,
                )}
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="资源">
                {getFieldDecorator('resource', {
                  initialValue: permission?.resource || '',
                  rules: [{ required: true, message: '请选择资源' }],
                })(
                  <AutoComplete
                    placeholder="请选择或输入资源"
                    onChange={this.handlePermissionInfoChange}
                    dataSource={resourceOptions.map((opt) => opt.value)}
                  />,
                )}
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="操作">
                {getFieldDecorator('action', {
                  initialValue: permission?.action || '',
                  rules: [{ required: true, message: '请选择操作' }],
                })(
                  <AutoComplete
                    placeholder="请选择或输入操作"
                    onChange={this.handlePermissionInfoChange}
                    dataSource={actionOptions.map((opt) => opt.value)}
                  />,
                )}
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="权限描述">
            {getFieldDecorator('description', {
              initialValue: permission?.description || '',
              rules: [
                { max: 200, message: '权限描述不能超过200个字符' },
              ],
            })(
              <TextArea
                placeholder="请输入权限描述"
                rows={3}
                maxLength={200}
              />,
            )}
          </Form.Item>

          <Form.Item label="备注">
            {getFieldDecorator('remark', {
              initialValue: permission?.remark || '',
            })(
              <TextArea
                placeholder="请输入备注信息"
                rows={2}
                maxLength={500}
              />,
            )}
          </Form.Item>
        </Form>

        <div style={{ marginTop: 16, fontSize: '12px', color: '#666' }}>
          <p>• 权限编码格式建议：分类:资源:操作，如 project:project:view</p>
          <p>• 权限编码创建后不可修改，请谨慎填写</p>
          <p>• 权限分类用于组织和管理权限，资源和操作用于具体的权限控制</p>
        </div>
      </Modal>
    );
  }
}

export default Form.create()(PermissionFormModal);
