import React, { Component } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Row,
  Col,
  message,
  Tag,
  Popconfirm,
  Divider,
  Tree,
} from 'antd';
import { permissionApi } from '../../services/api';
import PermissionFormModal from './PermissionFormModal';

const { Search } = Input;
const { Option } = Select;

class PermissionManagement extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      permissions: [],
      modules: [
        { id: 'project', name: '项目管理', description: '项目相关权限' },
        { id: 'brand', name: '品牌管理', description: '品牌相关权限' },
        { id: 'supplier', name: '供应商管理', description: '供应商相关权限' },
        { id: 'budget', name: '预算管理', description: '预算相关权限' },
        { id: 'finance', name: '财务管理', description: '财务相关权限' },
        { id: 'report', name: '报表管理', description: '报表相关权限' },
        { id: 'system', name: '系统管理', description: '系统相关权限' },
        { id: 'other', name: '其他', description: '其他权限' },
      ],
      viewMode: 'table', // table | tree
      filters: {
        module: undefined,
        search: '',
      },
      permissionFormVisible: false,
      selectedPermission: null,
      selectedRowKeys: [],
    };
  }

  componentDidMount() {
    this.loadPermissions();
    this.loadCategories();
  }

  // 加载权限列表
  loadPermissions = async (params = {}) => {
    this.setState({ loading: true });

    try {
      const { filters } = this.state;
      const requestParams = {
        ...filters,
        ...params,
      };

      const response = await permissionApi.getPermissions(requestParams);

      if (response.success) {
        this.setState({
          permissions: response.data.permissions || [],
        });
      } else {
        message.error(response.message || '获取权限列表失败');
      }
    } catch (error) {
      console.error('Load permissions failed:', error);
      message.error('获取权限列表失败');
    } finally {
      this.setState({ loading: false });
    }
  };

  // 加载权限分类
  loadCategories = async () => {
    try {
      const response = await permissionApi.getPermissionCategories();
      if (response.success) {
        this.setState({ modules: response.data || [] });
      }
    } catch (error) {
      console.error('Load categories failed:', error);
    }
  };

  // 处理搜索
  handleSearch = (keyword) => {
    this.setState(
      (prevState) => ({
        filters: { ...prevState.filters, keyword },
      }),
      () => this.loadPermissions(),
    );
  };

  // 处理筛选
  handleFilterChange = (key, value) => {
    this.setState(
      (prevState) => ({
        filters: { ...prevState.filters, [key]: value },
      }),
      () => this.loadPermissions(),
    );
  };

  // 切换视图模式
  handleViewModeChange = (mode) => {
    this.setState({ viewMode: mode });
  };

  // 打开权限表单
  handleOpenPermissionForm = (permission = null) => {
    this.setState({
      permissionFormVisible: true,
      selectedPermission: permission,
    });
  };

  // 关闭权限表单
  handleClosePermissionForm = () => {
    this.setState({
      permissionFormVisible: false,
      selectedPermission: null,
    });
  };

  // 权限表单提交成功
  handlePermissionFormSuccess = () => {
    this.handleClosePermissionForm();
    this.loadPermissions();
    this.loadCategories();
    message.success('操作成功');
  };

  // 删除权限
  handleDeletePermission = async (permissionId) => {
    try {
      const response = await permissionApi.deletePermission(permissionId);
      if (response.success) {
        message.success('权限删除成功');
        this.loadPermissions();
      } else {
        message.error(response.message || '权限删除失败');
      }
    } catch (error) {
      console.error('Delete permission failed:', error);
      message.error('权限删除失败');
    }
  };

  // 构建权限树数据
  buildPermissionTree = () => {
    const { permissions } = this.state;
    console.log('[ permissions ] >', permissions);
    // 按分类分组
    const groupedPermissions = (permissions || []).reduce((acc, permission) => {
      const category = permission.category || '其他';
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(permission);
      return acc;
    }, {});

    return Object.entries(groupedPermissions).map(([category, perms]) => ({
      title: (
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <span style={{ fontWeight: 'bold', fontSize: '14px' }}>{category}</span>
          <Tag color="blue">{perms.length}</Tag>
        </div>
      ),
      key: category,
      children: perms.map((permission) => ({
        title: (
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div>
              <div style={{ fontWeight: 'bold' }}>{permission.name}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                {permission.code}
              </div>
              {permission.description && (
                <div style={{ fontSize: '12px', color: '#999', marginTop: 2 }}>
                  {permission.description}
                </div>
              )}
            </div>
            <div>
              <Button
                type="link"
                size="small"
                onClick={() => this.handleOpenPermissionForm(permission)}
              >
                编辑
              </Button>
              <Popconfirm
                title="确定要删除这个权限吗？"
                onConfirm={() => this.handleDeletePermission(permission.id)}
                okText="确定"
                cancelText="取消"
              >
                <Button type="link" size="small" style={{ color: '#ff4d4f' }}>
                  删除
                </Button>
              </Popconfirm>
            </div>
          </div>
        ),
        key: permission.id.toString(),
        isLeaf: true,
      })),
    }));
  };

  // 渲染表格视图
  renderTableView = () => {
    const { permissions, selectedRowKeys } = this.state;

    const columns = [
      {
        title: '权限名称',
        dataIndex: 'name',
        width: 150,
        render: (text, record) => (
          <div>
            <div style={{ fontWeight: 'bold' }}>{text}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {record.code}
            </div>
          </div>
        ),
      },
      {
        title: '权限分类',
        dataIndex: 'category',
        width: 120,
        render: (category) => (
          <Tag color="blue">{category || '其他'}</Tag>
        ),
      },
      {
        title: '描述',
        dataIndex: 'description',
        width: 200,
        ellipsis: true,
        render: (text) => text || '-',
      },
      {
        title: '资源',
        dataIndex: 'resource',
        width: 120,
        render: (text) => text || '-',
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 100,
        render: (text) => text || '-',
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        width: 120,
        render: (time) => (time ? new Date(time).toLocaleDateString() : '-'),
      },
      {
        title: '操作',
        key: 'actions',
        width: 150,
        render: (_, record) => (
          <div>
            <Button
              type="link"
              size="small"
              onClick={() => this.handleOpenPermissionForm(record)}
            >
              编辑
            </Button>
            <Divider type="vertical" />
            <Popconfirm
              title="确定要删除这个权限吗？"
              onConfirm={() => this.handleDeletePermission(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" size="small" style={{ color: '#ff4d4f' }}>
                删除
              </Button>
            </Popconfirm>
          </div>
        ),
      },
    ];

    return (
      <Table
        columns={columns}
        dataSource={permissions}
        rowKey="id"
        loading={this.state.loading}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
        }}
        rowSelection={{
          selectedRowKeys,
          onChange: (keys) => this.setState({ selectedRowKeys: keys }),
        }}
      />
    );
  };

  // 渲染树形视图
  renderTreeView = () => {
    const treeData = this.buildPermissionTree();

    return (
      <div style={{ border: '1px solid #d9d9d9', borderRadius: 4, padding: 16 }}>
        <Tree
          showLine
          defaultExpandAll
          treeData={treeData}
          loading={this.state.loading}
        />
      </div>
    );
  };

  render() {
    const {
      // loading,
      modules,
      filters,
      viewMode,
      permissionFormVisible,
      selectedPermission,
    } = this.state;

    return (
      <div style={{ padding: '24px' }}>
        <Card
          title="权限管理"
          extra={
            <div>
              <Button.Group style={{ marginRight: 8 }}>
                <Button
                  type={viewMode === 'table' ? 'primary' : 'default'}
                  onClick={() => this.handleViewModeChange('table')}
                >
                  表格视图
                </Button>
                <Button
                  type={viewMode === 'tree' ? 'primary' : 'default'}
                  onClick={() => this.handleViewModeChange('tree')}
                >
                  树形视图
                </Button>
              </Button.Group>
              <Button type="primary" onClick={() => this.handleOpenPermissionForm()}>
                新增权限
              </Button>
            </div>
          }
        >
          {/* 筛选区域 */}
          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col span={6}>
              <Search
                placeholder="搜索权限名称、编码"
                onSearch={this.handleSearch}
                value={filters.search}
                onChange={(e) => this.handleFilterChange('search', e.target.value)}
                allowClear
              />
            </Col>
            <Col span={4}>
              <Select
                placeholder="选择分类"
                value={filters.module}
                onChange={(value) => this.handleFilterChange('module', value)}
                allowClear
                style={{ width: '100%' }}
              >
                {modules.map((module) => (
                  <Option key={module.id} value={module.id}>
                    {module.name}
                  </Option>
                ))}
              </Select>
            </Col>
          </Row>

          {/* 权限列表 */}
          {viewMode === 'table' ? this.renderTableView() : this.renderTreeView()}
        </Card>

        {/* 权限表单弹窗 */}
        {permissionFormVisible && (
          <PermissionFormModal
            visible={permissionFormVisible}
            permission={selectedPermission}
            categories={modules}
            onCancel={this.handleClosePermissionForm}
            onSuccess={this.handlePermissionFormSuccess}
          />
        )}
      </div>
    );
  }
}

export default PermissionManagement;
