# 供应商管理组件

这是一个完整的供应商管理系统组件集合，包含供应商表单、供应商列表表格和统计功能。已集成真实的后端API接口。

## 组件结构

```
SupplierManagement/
├── SupplierManagement.js    # 主页面组件（整合所有功能）
├── SupplierForm.js          # 供应商表单组件（带专业表单校验）
├── SupplierTable.js         # 供应商列表表格组件
├── SupplierFormModal.js     # 供应商表单模态框组件（带专业表单校验）
├── SupplierStats.js         # 供应商统计组件
└── README.md               # 说明文档
```

## 支持文件

```
services/
└── api.js                  # API服务层，处理所有HTTP请求

utils/
└── supplierUtils.js        # 供应商相关工具函数
```

## 功能特性

### 供应商表单 (SupplierForm.js)
- ✅ 基本信息：供应商名称、简称、编码
- ✅ 联系信息：联系人、电话、邮箱、地址
- ✅ 财务信息：税号、银行账号、开户银行、法人代表
- ✅ 业务信息：服务类型、首选税率、信用额度、付款条件
- ✅ 评级和备注信息
- ✅ 表单验证和错误提示
- ✅ 支持新建和编辑模式

### 供应商表格 (SupplierTable.js)
- ✅ 完整的供应商列表展示
- ✅ 搜索筛选功能（名称、联系人、联系方式、状态、服务类型）
- ✅ 新建、编辑、删除操作
- ✅ 批量删除功能
- ✅ 分页和排序
- ✅ 响应式设计
- ✅ 状态和评级标识

### 供应商统计 (SupplierStats.js)
- ✅ 供应商总数统计
- ✅ 启用供应商数量和启用率
- ✅ 按服务类型分组统计
- ✅ 按评级分组统计
- ✅ 统计数据可视化展示

### 供应商表单模态框 (SupplierFormModal.js)
- ✅ 模态框形式的供应商表单
- ✅ 分标签页组织表单字段
- ✅ 支持新建和编辑模式
- ✅ 表单验证和提交

## 使用方法

### 1. 独立使用供应商表单
```jsx
import SupplierForm from './components/SupplierManagement/SupplierForm';

<SupplierForm
  supplier={supplierData} // 编辑时传入供应商数据，新建时不传
  onSubmit={(data) => {
    console.log('供应商数据:', data);
  }}
  onCancel={() => {
    console.log('取消操作');
  }}
/>
```

### 2. 独立使用供应商表格
```jsx
import SupplierTable from './components/SupplierManagement/SupplierTable';

<SupplierTable
  onView={(supplier) => {
    console.log('查看供应商:', supplier);
  }}
  onEdit={(supplier) => {
    console.log('编辑供应商:', supplier);
  }}
/>
```

### 3. 使用供应商表单模态框
```jsx
import SupplierFormModal from './components/SupplierManagement/SupplierFormModal';

<SupplierFormModal
  visible={modalVisible}
  supplier={selectedSupplier} // 编辑时传入供应商数据
  onOk={(data) => {
    console.log('供应商数据:', data);
    setModalVisible(false);
  }}
  onCancel={() => {
    setModalVisible(false);
  }}
/>
```

### 4. 使用完整的供应商管理系统
```jsx
import SupplierManagement from './components/SupplierManagement/SupplierManagement';

<SupplierManagement />
```

## 数据结构

### 供应商数据结构
```javascript
{
  id: "supplier_id",
  name: "供应商名称",
  shortName: "简称",
  code: "SUP001",
  contactPerson: "联系人",
  contactPhone: "***********",
  contactEmail: "<EMAIL>",
  address: "供应商地址",
  taxNumber: "税号",
  bankAccount: "银行账号",
  bankName: "开户银行",
  legalPerson: "法人代表",
  serviceTypes: ["influencer", "advertising"],
  preferredTaxRate: "special_6",
  creditLimit: 1000000,
  paymentTerms: "付款条件",
  status: "active",
  rating: 5,
  notes: "备注信息",
  createdAt: "2024-01-01T00:00:00.000Z",
  updatedAt: "2024-01-01T00:00:00.000Z"
}
```

## 常量定义

### 供应商状态
- `active` - 启用
- `inactive` - 禁用
- `pending` - 待审核
- `blacklisted` - 黑名单

### 服务类型
- `influencer` - 达人服务
- `advertising` - 广告投放
- `other` - 其他服务

### 税率选项
- `general_13` - 一般纳税人13%
- `general_9` - 一般纳税人9%
- `general_6` - 一般纳税人6%
- `special_3` - 小规模纳税人3%
- `special_1` - 小规模纳税人1%
- `special_6` - 小规模纳税人6%

## 技术要求

- React 16.12.0+
- Ant Design 3.26.8
- moment.js（用于时间处理）

## 兼容性说明

本组件专门为Ant Design 3.x版本设计，已处理以下兼容性问题：
- 移除了Space组件（3.x版本不支持）
- 替换了Button的danger属性
- 使用了3.x版本支持的API

## API集成

组件已完全集成后端API，支持以下功能：

### 供应商管理API
- `GET /suppliers` - 获取供应商列表
- `GET /suppliers/{id}` - 获取单个供应商
- `POST /suppliers` - 创建供应商
- `PUT /suppliers/{id}` - 更新供应商
- `DELETE /suppliers/{id}` - 删除供应商
- `GET /suppliers/stats` - 获取供应商统计
- `DELETE /suppliers/batch-delete` - 批量删除供应商
- `PUT /suppliers/batch-status` - 批量更新供应商状态

### API配置
API基础URL在 `src/config.js` 中配置，默认为 `${host}/api`

## 数据转换

组件会自动处理前端表单数据与API数据格式之间的转换，确保数据的正确传输和显示。

## 错误处理

- 网络请求失败时会显示错误提示
- 表单验证失败时会显示具体的错误信息
- 支持Token过期自动刷新机制

## 性能优化

- 使用分页加载减少数据传输量
- 实现了防抖搜索避免频繁请求
- 组件懒加载提升页面加载速度
