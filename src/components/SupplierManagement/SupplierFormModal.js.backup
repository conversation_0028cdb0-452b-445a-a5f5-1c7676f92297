import React, { useState, useEffect, useCallback, memo } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Row,
  Col,
  message,
  Tabs,
} from 'antd';
import { supplierAPI } from '../../services/api';
import {
  SERVICE_TYPES,
  TAX_RATES,
  SUPPLIER_RATINGS,
  // validateSupplierForm,
} from '../../utils/supplierUtils';

const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;

// 表单校验规则
const formRules = {
  name: [
    { required: true, message: '请输入供应商名称' },
    { min: 2, max: 100, message: '供应商名称长度应在2-100个字符之间' },
  ],
  code: [
    { required: true, message: '请输入供应商编码' },
    { min: 3, max: 20, message: '供应商编码长度应在3-20个字符之间' },
  ],
  contactPerson: [
    { required: true, message: '请输入联系人' },
    { min: 2, max: 50, message: '联系人长度应在2-50个字符之间' },
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' },
  ],
  contactEmail: [
    { type: 'email', message: '请输入正确的邮箱地址' },
  ],
  serviceTypes: [
    { required: true, message: '请至少选择一种服务类型' },
    { type: 'array', min: 1, message: '请至少选择一种服务类型' },
  ],
  creditLimit: [
    { type: 'number', min: 0, message: '信用额度不能为负数' },
  ],
  rating: [
    { type: 'number', min: 1, max: 5, message: '评级必须在1-5之间' },
  ],
};

class SupplierFormModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      activeKey: 'basic',
    };
  }

  // 添加 shouldComponentUpdate 来减少不必要的重渲染
  shouldComponentUpdate(nextProps, nextState) {
    // 只有在关键 props 或 state 发生变化时才重新渲染
    return (
      nextProps.visible !== this.props.visible ||
      nextProps.supplier !== this.props.supplier ||
      nextState.loading !== this.state.loading ||
      nextState.activeKey !== this.state.activeKey
    );
  }

  componentDidUpdate(prevProps) {
    // 当模态框打开且有供应商数据时，填充表单
    if (this.props.visible && !prevProps.visible && this.props.supplier) {
      // 使用 requestAnimationFrame 确保 DOM 已更新
      requestAnimationFrame(() => {
        // 只设置有值的字段，避免空字符串覆盖placeholder
        const fieldsToSet = {};
        const { supplier } = this.props;

        // 只设置有实际值的字段
        if (supplier.name) fieldsToSet.name = supplier.name;
        if (supplier.shortName) fieldsToSet.shortName = supplier.shortName;
        if (supplier.code) fieldsToSet.code = supplier.code;
        if (supplier.contactPerson) fieldsToSet.contactPerson = supplier.contactPerson;
        if (supplier.contactPhone) fieldsToSet.contactPhone = supplier.contactPhone;
        if (supplier.contactEmail) fieldsToSet.contactEmail = supplier.contactEmail;
        if (supplier.address) fieldsToSet.address = supplier.address;
        if (supplier.taxNumber) fieldsToSet.taxNumber = supplier.taxNumber;
        if (supplier.bankAccount) fieldsToSet.bankAccount = supplier.bankAccount;
        if (supplier.bankName) fieldsToSet.bankName = supplier.bankName;
        if (supplier.legalPerson) fieldsToSet.legalPerson = supplier.legalPerson;
        if (supplier.serviceTypes) fieldsToSet.serviceTypes = supplier.serviceTypes;
        if (supplier.preferredTaxRate) fieldsToSet.preferredTaxRate = supplier.preferredTaxRate;
        if (supplier.creditLimit !== undefined) fieldsToSet.creditLimit = supplier.creditLimit;
        if (supplier.paymentTerms) fieldsToSet.paymentTerms = supplier.paymentTerms;
        if (supplier.status) fieldsToSet.status = supplier.status;
        if (supplier.rating !== undefined) fieldsToSet.rating = supplier.rating;
        if (supplier.notes) fieldsToSet.notes = supplier.notes;

        this.props.form.setFieldsValue(fieldsToSet);
      });
    }

    // 当模态框关闭时，重置表单
    if (!this.props.visible && prevProps.visible) {
      this.props.form.resetFields();
      // 重置内部状态
      this.setState({ activeKey: 'basic' });
    }
  }

  handleOk = () => {
    this.props.form.validateFields(async (err, values) => {
      if (err) {
        message.error('请检查表单填写是否正确');
        // 切换到错误表单项tab
        const firstError = Object.keys(err)[0];
        if (firstError) {
          let targetTab = firstError.split('.')[0];
          if (
            targetTab.includes('basic')
            || targetTab.includes('name')
            || targetTab.includes('code')
            || targetTab.includes('serverTypes')
          ) {
            targetTab = 'basic';
          }
          if (targetTab.includes('contact')) {
            targetTab = 'contact';
          }
          if (targetTab.includes('finance')) {
            targetTab = 'finance';
          }
          if (targetTab.includes('other')) {
            targetTab = 'other';
          }

          this.setState({ activeKey: targetTab });
        }
        return;
      }

      this.setState({ loading: true });

      try {
        const supplierData = {
          ...values,
          status: 'active', // 默认状态为启用
        };
        if (supplierData.preferredTaxRate) {
          supplierData.preferredTaxRate = supplierData.preferredTaxRate.toLocaleLowerCase();
        }

        let response;
        if (this.props.supplier) {
          // 编辑模式
          response = await supplierAPI.updateSupplier(this.props.supplier.id, supplierData);
        } else {
          // 新建模式
          response = await supplierAPI.createSupplier(supplierData);
        }

        if (response.success) {
          message.success(this.props.supplier ? '更新供应商成功' : '创建供应商成功');
          this.props.onOk && this.props.onOk(response.data);
        } else {
          message.error(response.message || '操作失败');
        }
      } catch (error) {
        console.error('Submit supplier failed:', error);
        message.error('操作失败');
      } finally {
        this.setState({ loading: false });
      }
    });
  };
  onChange = (activeKey) => {
    this.setState({ activeKey });
  };
  handleCancel = () => {
    this.props.onCancel && this.props.onCancel();
  };

  render() {
    const { visible, supplier } = this.props;
    const { loading } = this.state;
    const { getFieldDecorator } = this.props.form;
    const isEdit = !!supplier;

    return (
      <Modal
        title={isEdit ? '编辑供应商' : '新建供应商'}
        visible={visible}
        onOk={this.handleOk}
        onCancel={this.handleCancel}
        confirmLoading={loading}
        width={800}
        destroyOnClose
      >
        <Form layout="vertical">
          <Tabs onChange={this.onChange} activeKey={this.state.activeKey}>
            <TabPane tab="基本信息" key="basic">
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="供应商名称">
                    {getFieldDecorator('name', {
                      rules: formRules.name,
                    })(
                      <Input placeholder="请输入供应商名称" />,
                    )}
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="简称">
                    {getFieldDecorator('shortName')(
                      <Input placeholder="请输入供应商简称" />,
                    )}
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="供应商编码">
                    {getFieldDecorator('code', {
                      rules: formRules.code,
                    })(
                      <Input placeholder="请输入供应商编码" />,
                    )}
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="服务类型">
                    {getFieldDecorator('serviceTypes', {
                      rules: formRules.serviceTypes,
                    })(
                      <Select
                        mode="multiple"
                        placeholder="请选择服务类型"
                      >
                        {SERVICE_TYPES.map((type) => (
                          <Option key={type.value} value={type.value}>
                            {type.label}
                          </Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
              </Row>
            </TabPane>

            <TabPane tab="联系信息" key="contact">
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="联系人">
                    {getFieldDecorator('contactPerson', {
                      rules: formRules.contactPerson,
                    })(
                      <Input placeholder="请输入联系人" />,
                    )}
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="联系电话">
                    {getFieldDecorator('contactPhone', {
                      rules: formRules.contactPhone,
                    })(
                      <Input placeholder="请输入联系电话" />,
                    )}
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="联系邮箱">
                    {getFieldDecorator('contactEmail', {
                      rules: formRules.contactEmail,
                    })(
                      <Input placeholder="请输入联系邮箱" />,
                    )}
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="法人代表">
                    {getFieldDecorator('legalPerson')(
                      <Input placeholder="请输入法人代表" />,
                    )}
                  </Form.Item>
                </Col>
              </Row>
              <Form.Item label="地址">
                {getFieldDecorator('address')(
                  <Input placeholder="请输入供应商地址" />,
                )}
              </Form.Item>
            </TabPane>

            <TabPane tab="财务信息" key="finance">
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="税号">
                    {getFieldDecorator('taxNumber')(
                      <Input placeholder="请输入税号" />,
                    )}
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="首选税率">
                    {getFieldDecorator('preferredTaxRate')(
                      <Select placeholder="请选择首选税率">
                        {TAX_RATES.map((rate) => (
                          <Option key={rate.value} value={rate.value}>
                            {rate.label}
                          </Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="银行账号">
                    {getFieldDecorator('bankAccount')(
                      <Input placeholder="请输入银行账号" />,
                    )}
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="开户银行">
                    {getFieldDecorator('bankName')(
                      <Input placeholder="请输入开户银行" />,
                    )}
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="信用额度">
                    {getFieldDecorator('creditLimit', {
                      rules: formRules.creditLimit,
                    })(
                      <InputNumber
                        placeholder="请输入信用额度"
                        style={{ width: '100%' }}
                        min={0}
                        formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                      />,
                    )}
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="付款条件">
                    {getFieldDecorator('paymentTerms')(
                      <Input placeholder="请输入付款条件" />,
                    )}
                  </Form.Item>
                </Col>
              </Row>
            </TabPane>

            <TabPane tab="其他信息" key="other">
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="供应商评级">
                    {getFieldDecorator('rating', {
                      rules: formRules.rating,
                    })(
                      <Select placeholder="请选择供应商评级">
                        {SUPPLIER_RATINGS.map((rating) => (
                          <Option key={rating.value} value={rating.value}>
                            {rating.label}
                          </Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
              </Row>
              <Form.Item label="备注信息">
                {getFieldDecorator('notes')(
                  <TextArea
                    placeholder="请输入备注信息"
                    rows={4}
                  />,
                )}
              </Form.Item>
            </TabPane>
          </Tabs>
        </Form>
      </Modal>
    );
  }
}

export default Form.create()(SupplierFormModal);
