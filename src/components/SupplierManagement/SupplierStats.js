import React, { Component } from 'react';
import { Card, Row, Col, Statistic, Table, message, Spin } from 'antd';
import { supplierAPI } from '../../services/api';
import { getServiceTypeLabel } from '../../utils/supplierUtils';
// import { getServiceTypeLabel } from '../../utils/supplierUtils';

class SupplierStats extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      stats: {
        totalSuppliers: 0,
        activeSuppliers: 0,
        suppliersByServiceType: [],
        suppliersByRating: [],
      },
    };
  }

  componentDidMount() {
    this.loadStats();
  }

  loadStats = async () => {
    this.setState({ loading: true });
    try {
      const response = await supplierAPI.getSupplierStats();
      if (response.success) {
        this.setState({
          stats: response.data,
        });
      } else {
        message.error(response.message || '获取统计数据失败');
      }
    } catch (error) {
      console.error('Load supplier stats failed:', error);
      message.error('获取统计数据失败');
    } finally {
      this.setState({ loading: false });
    }
  };

  render() {
    const { loading, stats } = this.state;

    // 服务类型统计表格列
    const serviceTypeColumns = [
      {
        title: '服务类型',
        dataIndex: 'serviceType',
        key: 'serviceType',
        render: (serviceType) => getServiceTypeLabel(serviceType),
      },
      {
        title: '供应商数量',
        dataIndex: 'count',
        key: 'count',
        align: 'right',
      },
      {
        title: '占比',
        key: 'percentage',
        align: 'right',
        render: (text, record) => {
          const percentage = stats.totalSuppliers > 0
            ? ((record.count / stats.totalSuppliers) * 100).toFixed(1)
            : 0;
          return `${percentage}%`;
        },
      },
    ];

    // 评级统计表格列
    const ratingColumns = [
      {
        title: '评级',
        dataIndex: 'rating',
        key: 'rating',
        render: (rating) => {
          const stars = '★'.repeat(rating) + '☆'.repeat(5 - rating);
          return `${stars} (${rating}星)`;
        },
      },
      {
        title: '供应商数量',
        dataIndex: 'count',
        key: 'count',
        align: 'right',
      },
      {
        title: '占比',
        key: 'percentage',
        align: 'right',
        render: (text, record) => {
          const percentage = stats.totalSuppliers > 0
            ? ((record.count / stats.totalSuppliers) * 100).toFixed(1)
            : 0;
          return `${percentage}%`;
        },
      },
    ];

    if (loading) {
      return (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      );
    }

    return (
      <div>
        {/* 总体统计 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={8}>
            <Card>
              <Statistic
                title="供应商总数"
                value={stats.totalSuppliers}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title="启用供应商"
                value={stats.activeSuppliers}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title="启用率"
                value={
                  stats.totalSuppliers > 0
                    ? ((stats.activeSuppliers / stats.totalSuppliers) * 100).toFixed(1)
                    : 0
                }
                suffix="%"
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 详细统计 */}
        <Row gutter={16}>
          <Col span={12}>
            <Card title="按服务类型统计" style={{ height: '400px' }}>
              <Table
                columns={serviceTypeColumns}
                dataSource={stats.suppliersByServiceType}
                rowKey="serviceType"
                pagination={false}
                size="small"
              />
            </Card>
          </Col>
          <Col span={12}>
            <Card title="按评级统计" style={{ height: '400px' }}>
              <Table
                columns={ratingColumns}
                dataSource={stats.suppliersByRating}
                rowKey="rating"
                pagination={false}
                size="small"
              />
            </Card>
          </Col>
        </Row>

        {/* 刷新按钮 */}
        <div style={{ textAlign: 'center', marginTop: 24 }}>
          <button
            type="button"
            onClick={this.loadStats}
            style={{
              padding: '8px 16px',
              backgroundColor: '#1890ff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
            }}
          >
            刷新统计数据
          </button>
        </div>
      </div>
    );
  }
}

export default SupplierStats;
