import React, { Component } from 'react';
import { Modal, Descriptions, Tag, Card } from 'antd';
import {
  getSupplierStatusConfig,
  getServiceTypeLabel,
  formatDate,
  getRatingDisplay,
} from '../../utils/supplierUtils';

class SupplierDetailModal extends Component {
  formatCurrency = (amount) => {
    try {
      if (!amount || isNaN(amount)) return '¥0';
      return `¥${Number(amount).toLocaleString()}`;
    } catch (error) {
      console.warn('Format currency error:', error);
      return '¥0';
    }
  };

  render() {
    const { visible, supplier, onCancel } = this.props;

    if (!supplier) {
      return null;
    }

    const statusConfig = getSupplierStatusConfig(supplier.status);

    return (
      <Modal
        title={`供应商详情 - ${supplier.name}`}
        visible={visible}
        onCancel={onCancel}
        footer={null}
        width={900}
        destroyOnClose
      >
        <div style={{ padding: '16px 0' }}>
          {/* 基本信息 */}
          <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
            <Descriptions column={2} size="small">
              <Descriptions.Item label="供应商名称">
                {supplier.name}
              </Descriptions.Item>
              <Descriptions.Item label="简称">
                {supplier.shortName || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="供应商编码">
                {supplier.code}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={statusConfig.color}>{statusConfig.label}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="服务类型" span={2}>
                {supplier.serviceTypes?.map((type) => (
                  <Tag key={type} style={{ marginRight: 4 }}>
                    {getServiceTypeLabel(type)}
                  </Tag>
                )) || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="供应商评级">
                {getRatingDisplay(supplier.rating)}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {formatDate(supplier.createdAt)}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* 联系信息 */}
          <Card title="联系信息" size="small" style={{ marginBottom: 16 }}>
            <Descriptions column={2} size="small">
              <Descriptions.Item label="联系人">
                {supplier.contactPerson}
              </Descriptions.Item>
              <Descriptions.Item label="联系电话">
                {supplier.contactPhone}
              </Descriptions.Item>
              <Descriptions.Item label="联系邮箱">
                {supplier.contactEmail || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="法人代表">
                {supplier.legalPerson || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="地址" span={2}>
                {supplier.address || '-'}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* 财务信息 */}
          <Card title="财务信息" size="small" style={{ marginBottom: 16 }}>
            <Descriptions column={2} size="small">
              <Descriptions.Item label="税号">
                {supplier.taxNumber || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="首选税率">
                {supplier.preferredTaxRate || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="银行账号">
                {supplier.bankAccount || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="开户银行">
                {supplier.bankName || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="信用额度">
                {this.formatCurrency(supplier.creditLimit)}
              </Descriptions.Item>
              <Descriptions.Item label="付款条件">
                {supplier.paymentTerms || '-'}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* 备注信息 */}
          {supplier.notes && (
            <Card title="备注信息" size="small">
              <div style={{
                padding: '12px',
                background: '#fafafa',
                borderRadius: '4px',
                lineHeight: '1.6',
              }}
              >
                {supplier.notes}
              </div>
            </Card>
          )}
        </div>
      </Modal>
    );
  }
}

export default SupplierDetailModal;
