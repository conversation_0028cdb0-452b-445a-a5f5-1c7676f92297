import React from 'react';
import { Route, Redirect } from 'react-router-dom';
import { usePagePermission } from '../../hooks/usePermission';
import { Result, Button } from 'antd';

/**
 * 受保护的路由组件
 * 用于需要权限验证的路由
 */
const ProtectedRoute = ({
  children,
  permissions = [],
  redirectTo = '/403',
  ...routeProps
}) => {
  const { hasPageAccess, isSuperAdmin } = usePagePermission(permissions);

  return (
    <Route
      {...routeProps}
      render={(props) => {
        // 超级管理员或有权限访问
        if (isSuperAdmin || hasPageAccess) {
          return children;
        }

        // 无权限，重定向或显示403页面
        if (redirectTo === '/403') {
          return (
            <Result
              status="403"
              title="403"
              subTitle="抱歉，您没有权限访问此页面。"
              extra={
                <Button type="primary" onClick={() => props.history.goBack()}>
                  返回上一页
                </Button>
              }
            />
          );
        }

        return <Redirect to={redirectTo} />;
      }}
    />
  );
};

export default ProtectedRoute;
