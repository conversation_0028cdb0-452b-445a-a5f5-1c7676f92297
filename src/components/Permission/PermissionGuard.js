import React from 'react';
import { Result, Button } from 'antd';
import { usePermission } from '../../hooks/usePermission';

/**
 * 权限守卫组件
 * 用于包装需要权限控制的组件
 */
const PermissionGuard = ({
  children,
  permissions = [],
  requireAll = false,
  fallback = null,
  showNoPermission = true,
}) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions, isSuperAdmin } = usePermission();

  // 超级管理员直接通过
  if (isSuperAdmin) {
    return children;
  }

  // 检查权限
  let hasAccess = false;

  if (permissions.length === 0) {
    // 没有权限要求，直接通过
    hasAccess = true;
  } else if (typeof permissions === 'string') {
    // 单个权限
    hasAccess = hasPermission(permissions);
  } else if (Array.isArray(permissions)) {
    // 多个权限
    hasAccess = requireAll
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions);
  }

  // 有权限，渲染子组件
  if (hasAccess) {
    return children;
  }

  // 无权限，渲染降级组件
  if (fallback) {
    return fallback;
  }

  // 显示无权限提示
  if (showNoPermission) {
    return (
      <Result
        status="403"
        title="403"
        subTitle="抱歉，您没有权限访问此功能。"
        extra={
          <Button type="primary" onClick={() => window.history.back()}>
            返回
          </Button>
        }
      />
    );
  }

  // 不显示任何内容
  return null;
};

/**
 * 页面权限守卫组件
 * 用于页面级别的权限控制
 */
export const PageGuard = ({ children, permissions = [], requireAll = false }) => {
  return (
    <PermissionGuard
      permissions={permissions}
      requireAll={requireAll}
      showNoPermission
    >
      {children}
    </PermissionGuard>
  );
};

/**
 * 功能权限守卫组件
 * 用于功能级别的权限控制，无权限时不显示
 */
export const FeatureGuard = ({ children, permissions = [], requireAll = false }) => {
  return (
    <PermissionGuard
      permissions={permissions}
      requireAll={requireAll}
      showNoPermission={false}
    >
      {children}
    </PermissionGuard>
  );
};

/**
 * 按钮权限守卫组件
 * 用于按钮级别的权限控制
 */
export const ButtonGuard = ({
  children,
  permissions = [],
  requireAll = false,
  // disabled = false,
}) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions, isSuperAdmin } = usePermission();

  // 超级管理员直接通过
  if (isSuperAdmin) {
    return children;
  }

  // 检查权限
  let hasAccess = false;

  if (permissions.length === 0) {
    hasAccess = true;
  } else if (typeof permissions === 'string') {
    hasAccess = hasPermission(permissions);
  } else if (Array.isArray(permissions)) {
    hasAccess = requireAll
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions);
  }

  // 无权限时禁用按钮
  if (!hasAccess) {
    if (React.isValidElement(children)) {
      return React.cloneElement(children, {
        disabled: true,
        title: '您没有权限执行此操作',
      });
    }
    return <div style={{ display: 'none' }} />;
  }

  return children || null;
};

export default PermissionGuard;
