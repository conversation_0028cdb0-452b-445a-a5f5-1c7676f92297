/* 现代化仪表板样式 */
.modern-dashboard {
  padding: 0;
}

/* 欢迎区域 */
.welcome-section {
  background: var(--gradient-primary);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-2xl);
  margin-bottom: var(--spacing-xl);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.welcome-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.welcome-content {
  position: relative;
  z-index: 1;
}

.welcome-title {
  font-size: 30px;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: white;
}

.welcome-subtitle {
  font-size: 18px;
  margin: 0;
  opacity: 0.9;
}

.welcome-actions {
  position: relative;
  z-index: 1;
}

.welcome-actions .ant-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-weight: 600;
  backdrop-filter: blur(10px);
  transition: all 300ms ease;
}

.welcome-actions .ant-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* 统计卡片 */
.stats-row {
  margin-bottom: 32px;
}

.stat-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  transition: all 300ms ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  transition: width 300ms ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
}

.stat-card:hover::before {
  width: 8px;
}

.stat-card-primary::before {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card-success::before {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
}

.stat-card-warning::before {
  background: linear-gradient(135deg, #faad14 0%, #d48806 100%);
}

.stat-card-info::before {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
}

.stat-card .ant-statistic-title {
  color: #595959;
  font-weight: 500;
  margin-bottom: 8px;
}

.stat-card .ant-statistic-content {
  color: #262626;
  font-weight: 700;
}

.stat-card .ant-statistic-content-prefix {
  margin-right: 8px;
  color: #1890ff;
}

.stat-trend {
  margin-top: 16px;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #ff4d4f;
}

.stat-progress {
  margin-top: 8px;
}

/* 内容区域 */
.content-row {
  margin-bottom: 32px;
}

.content-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  height: 100%;
}

.content-card .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 24px 24px 16px;
}

.content-card .ant-card-head-title {
  font-weight: 600;
  font-size: 18px;
  color: #262626;
}

.content-card .ant-card-body {
  padding: 24px;
}

/* 项目列表 */
.project-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.project-item:last-child {
  border-bottom: none;
}

.project-info {
  width: 100%;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.project-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.project-details {
  display: flex;
  gap: 24px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #595959;
}

.project-details span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.project-progress {
  margin-top: 8px;
}

/* 活动时间线 */
.activity-timeline {
  margin-top: 16px;
}

.activity-timeline .ant-timeline-item-tail {
  border-left-color: #f0f0f0;
}

.activity-avatar {
  background: #1890ff;
  border: 2px solid #ffffff;
}

.activity-content {
  margin-left: 16px;
}

.activity-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.activity-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #8c8c8c;
}

/* 快速操作 */
.quick-actions-card {
  margin-bottom: 32px;
}

.quick-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 300ms ease;
  border: 2px solid transparent;
}

.quick-action:hover {
  background: #f5f5f5;
  border-color: #d9d9d9;
  transform: translateY(-2px);
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  margin-bottom: 16px;
  transition: all 300ms ease;
}

.action-icon-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.action-icon-success {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
}

.action-icon-warning {
  background: linear-gradient(135deg, #faad14 0%, #d48806 100%);
}

.action-icon-info {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
}

.quick-action:hover .action-icon {
  transform: scale(1.1);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
}

.action-text {
  font-weight: 500;
  color: #262626;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }
  
  .welcome-title {
    font-size: 24px;
  }
  
  .welcome-subtitle {
    font-size: 16px;
  }
  
  .project-details {
    flex-direction: column;
    gap: 8px;
  }
  
  .activity-meta {
    flex-direction: column;
    gap: 4px;
  }
}
