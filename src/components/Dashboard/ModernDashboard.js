import React, { Component } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Icon,
  // Progress,
  // List,
  // Avatar,
  // Button,
  // Tag,
  // Timeline,
  // Divider,
} from 'antd';
import './ModernDashboard.css';
import { statisticsAPI } from '../../services/api';

class ModernDashboard extends Component {
  constructor(props) {
    super(props);
    this.state = {
      // loading: false,
      dashboardData: {
        totalProjects: 0,
        activeProjects: 0,
        currentMonthRevenue: {
          amount: 0,
          previousAmount: 0,
          growthRate: 0,
          period: '',
        },
        currentYearRevenue: {
          amount: 0,
          previousAmount: 0,
          growthRate: 0,
          period: '',
        },
        recentProjects: [],
        recentActivities: [],
      },
    };
  }

  getStatusColor = (status) => {
    const colors = {
      active: '#52c41a',
      planning: '#1890ff',
      completed: '#722ed1',
      paused: '#faad14',
      cancelled: '#ff4d4f',
    };
    return colors[status] || '#d9d9d9';
  };

  getStatusText = (status) => {
    const texts = {
      active: '进行中',
      planning: '规划中',
      completed: '已完成',
      paused: '已暂停',
      cancelled: '已取消',
    };
    return texts[status] || '未知';
  };

  getActivityIcon = (type) => {
    const icons = {
      project: 'project',
      payment: 'dollar',
      approval: 'check-circle',
      completion: 'trophy',
    };
    return icons[type] || 'info-circle';
  };

  loadDashboardData = async () => {
    try {
      // 从后端加载数据
      const response = await statisticsAPI.getStatistics();
      const { data } = response;
      // data: {
      //   type: 'object',
      //   properties: {
      //     totalProjects: { type: 'number', description: '项目总数' },
      //     currentMonthRevenue: {
      //       type: 'object',
      //       properties: {
      //         amount: { type: 'number', description: '当月总收入' },
      //         previousAmount: { type: 'number', description: '上月总收入' },
      //         growthRate: { type: 'number', description: '环比增长率(%)' },
      //         period: { type: 'string', description: '当前月份(YYYY-MM)' }
      //       }
      //     },
      //     currentYearRevenue: {
      //       type: 'object',
      //       properties: {
      //         amount: { type: 'number', description: '今年总收入' },
      //         previousAmount: { type: 'number', description: '去年总收入' },
      //         growthRate: { type: 'number', description: '同比增长率(%)' },
      //         period: { type: 'string', description: '当前年份(YYYY)' }
      //       }
      //     },
      //     generatedAt: { type: 'string', format: 'date-time', description: '生成时间' }
      //   }
      // },
      this.setState({ dashboardData: data });
    } catch (error) {
      console.error('加载数据失败:', error);
    }
  };

  componentDidMount() {
    // 从后端加载数据
    this.loadDashboardData();
  }

  render() {
    const { dashboardData } = this.state;
    const { totalProjects,
      activeProjects,
      currentMonthRevenue,
      currentYearRevenue,
    } = dashboardData;

    return (
      <div className="modern-dashboard">
        {/* 欢迎区域 */}
        <div className="welcome-section">
          <div className="welcome-content">
            <h1 className="welcome-title">欢迎回来！</h1>
            <p className="welcome-subtitle">
              今天是 {new Date().toLocaleDateString('zh-CN', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              weekday: 'long',
            })}
            </p>
          </div>
          <div className="welcome-actions">
            {/* <Button type="primary" size="large" icon="plus">
              新建项目
            </Button> */}
          </div>
        </div>

        {/* 统计卡片 */}
        <Row gutter={[24, 24]} className="stats-row">
          <Col xs={24} sm={12} lg={8}>
            <Card className="stat-card stat-card-primary">
              <Statistic
                title="项目总数"
                value={totalProjects}
                prefix={<Icon type="project" />}
                suffix="个"
              />
              <div className="stat-trend">
                <Icon type="arrow-up" className="trend-up" />
                <span>上月 {activeProjects}</span>
              </div>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={8}>
            <Card className="stat-card stat-card-success">
              <Statistic
                title="当月总收入"
                value={currentMonthRevenue.amount}
                prefix={<Icon type="dollar" />}
                precision={0}
                formatter={(value) => `¥${value.toLocaleString()}`}
              />
              <div className="stat-trend">
                <Icon type="arrow-up" className="trend-up" />
                <span>较上月 {currentMonthRevenue.growthRate}%</span>
              </div>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={8}>
            <Card className="stat-card stat-card-warning">
              <Statistic
                title="今年总收入"
                value={currentYearRevenue.amount}
                prefix={<Icon type="dollar" />}
                precision={0}
                formatter={(value) => `¥${value.toLocaleString()}`}
              />
              <div className="stat-trend">
                <Icon type="arrow-up" className="trend-up" />
                <span>较去年 {currentYearRevenue.growthRate}%</span>
              </div>
            </Card>
          </Col>
        </Row>

      </div>
    );
  }
}

export default ModernDashboard;
