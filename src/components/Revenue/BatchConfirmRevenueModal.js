import React, { Component } from 'react';
import {
  Modal,
  // Form,
  Table,
  InputNumber,
  DatePicker,
  Input,
  Button,
  message,
  Typography,
  Divider,
} from 'antd';
import moment from 'moment';
import { formatAmount } from '../../utils/revenueUtils';

const { TextArea } = Input;
const { Text } = Typography;

class BatchConfirmRevenueModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      revenueData: [],
    };
  }

  componentDidMount() {
    this.initializeRevenueData();
  }

  componentDidUpdate(prevProps) {
    if (prevProps.revenues !== this.props.revenues) {
      this.initializeRevenueData();
    }
  }

  initializeRevenueData = () => {
    const { revenues } = this.props;
    const revenueData = revenues.map((revenue) => ({
      id: revenue.id,
      title: revenue.title,
      plannedAmount: revenue.plannedAmount,
      actualAmount: revenue.plannedAmount, // 默认使用预计金额
      confirmedDate: moment(),
      notes: '',
    }));
    this.setState({ revenueData });
  };

  handleActualAmountChange = (id, value) => {
    this.setState((prevState) => ({
      revenueData: prevState.revenueData.map((item) =>
        (item.id === id ? { ...item, actualAmount: value } : item)),
    }));
  };

  handleConfirmedDateChange = (id, date) => {
    this.setState((prevState) => ({
      revenueData: prevState.revenueData.map((item) =>
        (item.id === id ? { ...item, confirmedDate: date } : item)),
    }));
  };

  handleNotesChange = (id, notes) => {
    this.setState((prevState) => ({
      revenueData: prevState.revenueData.map((item) =>
        (item.id === id ? { ...item, notes } : item)),
    }));
  };

  handleSubmit = () => {
    const { revenueData } = this.state;

    // 验证数据
    const invalidItems = revenueData.filter((item) =>
      !item.actualAmount || item.actualAmount < 0);

    if (invalidItems.length > 0) {
      message.error('请确保所有收入的实际金额都已正确填写');
      return;
    }

    this.setState({ loading: true });

    const confirmData = revenueData.map((item) => ({
      id: item.id,
      actualAmount: item.actualAmount,
      confirmedDate: item.confirmedDate ? item.confirmedDate.format('YYYY-MM-DD') : null,
      notes: item.notes,
    }));

    this.props.onConfirm(confirmData)
      .then(() => {
        message.success('批量确认收入成功');
        this.props.onCancel();
      })
      .catch((error) => {
        console.error('Batch confirm revenues failed:', error);
        message.error('批量确认收入失败');
      })
      .finally(() => {
        this.setState({ loading: false });
      });
  };

  getColumns = () => [
    {
      title: '收入标题',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      ellipsis: true,
    },
    {
      title: '预计金额',
      dataIndex: 'plannedAmount',
      key: 'plannedAmount',
      width: 120,
      render: (amount) => formatAmount(amount),
    },
    {
      title: '实际金额',
      key: 'actualAmount',
      width: 150,
      render: (_, record) => (
        <InputNumber
          value={record.actualAmount}
          onChange={(value) => this.handleActualAmountChange(record.id, value)}
          style={{ width: '100%' }}
          formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
          precision={2}
          min={0}
        />
      ),
    },
    {
      title: '确认时间',
      key: 'confirmedDate',
      width: 150,
      render: (_, record) => (
        <DatePicker
          value={record.confirmedDate}
          onChange={(date) => this.handleConfirmedDateChange(record.id, date)}
          style={{ width: '100%' }}
        />
      ),
    },
    {
      title: '备注',
      key: 'notes',
      width: 200,
      render: (_, record) => (
        <TextArea
          value={record.notes}
          onChange={(e) => this.handleNotesChange(record.id, e.target.value)}
          rows={2}
          placeholder="备注（可选）"
        />
      ),
    },
  ];

  render() {
    const { visible, onCancel, revenues } = this.props;
    const { loading, revenueData } = this.state;

    if (!revenues || revenues.length === 0) return null;

    return (
      <Modal
        title={`批量确认收入 (${revenues.length}条)`}
        visible={visible}
        onCancel={onCancel}
        footer={null}
        width={1200}
        destroyOnClose
      >
        <div style={{ marginBottom: 16 }}>
          <Text type="secondary">
            请为每条收入记录填写实际收入金额和确认时间
          </Text>
        </div>

        <Divider />

        <Table
          columns={this.getColumns()}
          dataSource={revenueData}
          rowKey="id"
          pagination={false}
          scroll={{ y: 400 }}
          size="small"
        />

        <div style={{ textAlign: 'right', marginTop: 24 }}>
          <Button onClick={onCancel} style={{ marginRight: 8 }}>
            取消
          </Button>
          <Button
            type="primary"
            onClick={this.handleSubmit}
            loading={loading}
          >
            批量确认收入
          </Button>
        </div>
      </Modal>
    );
  }
}

export default BatchConfirmRevenueModal;
