import React, { Component } from 'react';
import { Tabs, Button } from 'antd';
import RevenueTable from './RevenueTable';
import RevenueForm from './RevenueForm';
import RevenueStats from './RevenueStats';
import { ProjectPermissionGuard } from '../Auth/PermissionGuard';

const { TabPane } = Tabs;

class RevenueManagement extends Component {
  constructor(props) {
    super(props);
    this.state = {
      activeTab: 'list',
      modalVisible: false,
      editingRevenue: null,
      // selectedProjectId: null,
    };
  }

  handleTabChange = (key) => {
    this.setState({ activeTab: key });
  };

  handleAdd = () => {
    this.setState({
      modalVisible: true,
      editingRevenue: null,
    });
  };

  handleEdit = (revenue) => {
    this.setState({
      modalVisible: true,
      editingRevenue: revenue,
    });
  };

  handleView = (revenue) => {
    // 可以打开详情模态框或跳转到详情页
    console.log('View revenue:', revenue);
  };

  handleModalCancel = () => {
    this.setState({
      modalVisible: false,
      editingRevenue: null,
    });
  };

  handleFormSubmit = (data) => {
    console.log('Revenue saved:', data);
    this.setState({
      modalVisible: false,
      editingRevenue: null,
    });
    // 刷新列表
    if (this.revenueTableRef) {
      this.revenueTableRef.loadData();
    }
  };

  render() {
    const { activeTab, modalVisible, editingRevenue } = this.state;

    return (
      <div style={{ padding: '20px' }}>
        <Tabs
          activeKey={activeTab}
          onChange={this.handleTabChange}
          type="card"
          tabBarExtraContent={
            activeTab === 'list' && (
              <ProjectPermissionGuard action="create">
                <Button type="primary" onClick={this.handleAdd}>
                  新建收入
                </Button>
              </ProjectPermissionGuard>
            )
          }
        >
          <TabPane tab="收入列表" key="list">
            <RevenueTable
              ref={(ref) => { this.revenueTableRef = ref; }}
              onEdit={this.handleEdit}
              onView={this.handleView}
            />
          </TabPane>

          <TabPane tab="新建收入" key="form">
            <RevenueForm
              onSubmit={(data) => {
                console.log('收入数据:', data);
                // 这里可以处理表单提交后的逻辑，比如切换到列表页
                this.setState({ activeTab: 'list' });
              }}
            />
          </TabPane>

          <TabPane tab="收入统计" key="stats">
            <RevenueStats />
          </TabPane>
        </Tabs>

        {/* 编辑收入模态框 */}

      </div>
    );
  }
}

export default RevenueManagement;
