import React, { Component } from 'react';
import {
  Form,
  Input,
  Select,
  // DatePicker,
  InputNumber,
  Button,
  Row,
  Col,
  Card,
  message,
} from 'antd';
import moment from 'moment';
import { REVENUE_TYPE, validateRevenueData } from '../../utils/revenueUtils';
import { revenueAPI } from '../../services/api';

const { Option } = Select;
const { TextArea } = Input;

// 表单校验规则
const formRules = {
  title: [
    { required: true, message: '请输入收入标题' },
    { min: 2, max: 100, message: '收入标题长度应在2-100个字符之间' },
  ],
  revenueType: [
    { required: true, message: '请选择收入类型' },
  ],
  plannedAmount: [
    { required: true, message: '请输入预计收入金额' },
    { type: 'number', min: 0.01, message: '预计收入金额必须大于0' },
  ],
  plannedDate: [
    { required: false, message: '请选择预计收入时间' },
  ],
  actualAmount: [
    { type: 'number', min: 0, message: '实际收入金额不能为负数' },
  ],
  invoiceAmount: [
    { type: 'number', min: 0, message: '开票金额不能为负数' },
  ],
};

class RevenueForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      // currentStatus: 'planned',
    };
  }

  componentDidMount() {
    // 如果有初始数据，设置表单值
    this.props.form.setFieldsValue({ title: this.props.projectName });
    if (this.props.initialData) {
      setTimeout(() => {
        const data = this.props.initialData;
        const { projectName } = this.props;

        // 只设置有值的字段，避免空字符串覆盖placeholder
        const fieldsToSet = {};

        // 标题字段特殊处理
        fieldsToSet.title = data.title || projectName;

        // 其他字段只有在有值时才设置
        if (data.revenueType) fieldsToSet.revenueType = data.revenueType;
        if (data.status) fieldsToSet.status = data.status;
        if (data.plannedAmount !== undefined && data.plannedAmount !== null) {
          fieldsToSet.plannedAmount = data.plannedAmount;
        }
        if (data.actualAmount !== undefined && data.actualAmount !== null) {
          fieldsToSet.actualAmount = data.actualAmount;
        }
        if (data.invoiceAmount !== undefined && data.invoiceAmount !== null) {
          fieldsToSet.invoiceAmount = data.invoiceAmount;
        }
        if (data.plannedDate) fieldsToSet.plannedDate = moment(data.plannedDate);
        if (data.confirmedDate) fieldsToSet.confirmedDate = moment(data.confirmedDate);
        if (data.invoiceDate) fieldsToSet.invoiceDate = moment(data.invoiceDate);
        if (data.receivedDate) fieldsToSet.receivedDate = moment(data.receivedDate);
        if (data.milestone) fieldsToSet.milestone = data.milestone;
        if (data.invoiceNumber) fieldsToSet.invoiceNumber = data.invoiceNumber;
        if (data.paymentTerms) fieldsToSet.paymentTerms = data.paymentTerms;
        if (data.notes) fieldsToSet.notes = data.notes;

        this.props.form.setFieldsValue(fieldsToSet);
        // this.setState({ currentStatus: data.status || 'planned' });
      }, 100);
    }
  }

  handleSubmit = (e) => {
    e.preventDefault();
    this.props.form.validateFields(async (err, values) => {
      if (err) {
        message.error('请检查表单填写是否正确');
        return;
      }

      this.setState({ loading: true });

      try {
        // 格式化数据
        const formData = {
          ...values,
          // plannedDate: values.plannedDate ? values.plannedDate.format('YYYY-MM-DD') : null,
          // confirmedDate: values.confirmedDate ? values.confirmedDate.format('YYYY-MM-DD') : null,
          // invoiceDate: values.invoiceDate ? values.invoiceDate.format('YYYY-MM-DD') : null,
          // receivedDate: values.receivedDate ? values.receivedDate.format('YYYY-MM-DD') : null,
        };

        // 验证数据
        const validation = validateRevenueData(formData);
        if (!validation.isValid) {
          message.error('数据验证失败');
          return;
        }

        let response;
        if (this.props.initialData && this.props.initialData.id) {
          // 编辑模式
          response = await revenueAPI.updateRevenue(this.props.initialData.id, formData);
        } else {
          // 新增模式
          if (!this.props.projectId) {
            message.error('缺少项目ID');
            return;
          }
          response = await revenueAPI.createRevenue(this.props.projectId, formData);
        }

        if (response.success) {
          message.success('收入保存成功');
          if (this.props.onSubmit) {
            this.props.onSubmit(response.data);
          }
        } else {
          message.error(response.message || '保存失败');
        }
      } catch (error) {
        console.error('Submit revenue failed:', error);
        message.error('保存失败');
      } finally {
        this.setState({ loading: false });
      }
    });
  };

  // handleReset = () => {
  //   this.props.form.resetFields();
  //   // this.setState({ currentStatus: 'planned' });
  // };

  // handleStatusChange = (status) => {
  //   this.setState({ currentStatus: status });

  //   // 根据状态自动设置相关日期
  //   const { form } = this.props;
  //   const now = moment();

  //   switch (status) {
  //     case 'confirmed':
  //       if (!form.getFieldValue('confirmedDate')) {
  //         form.setFieldsValue({ confirmedDate: now });
  //       }
  //       break;
  //     case 'invoiced':
  //       if (!form.getFieldValue('invoiceDate')) {
  //         form.setFieldsValue({ invoiceDate: now });
  //       }
  //       break;
  //     case 'received':
  //       if (!form.getFieldValue('receivedDate')) {
  //         form.setFieldsValue({ receivedDate: now });
  //       }
  //       break;
  //     default:
  //       break;
  //   }
  // };

  render() {
    const { loading } = this.state;
    const { hideActions = false, form } = this.props;
    const { getFieldDecorator } = form;
    const isEditing = !!(this.props.initialData && this.props.initialData.id);

    return (
      <Card title={isEditing ? '编辑收入' : '新建收入'} style={{ margin: '20px 0' }}>
        <Form layout="vertical" onSubmit={this.handleSubmit}>
          {/* 基本信息 */}
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="收入标题">
                {getFieldDecorator('title', {
                  rules: formRules.title,
                })(
                  <Input placeholder="请输入收入标题" />,
                )}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="收入类型">
                {getFieldDecorator('revenueType', {
                  rules: formRules.revenueType,
                })(
                  <Select placeholder="请选择收入类型">
                    {REVENUE_TYPE.map((type) => (
                      <Option key={type.value} value={type.value}>{type.label}</Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item label="预计收入金额">
                {getFieldDecorator('plannedAmount', {
                  rules: formRules.plannedAmount,
                })(
                  <InputNumber
                    placeholder="请输入预计金额"
                    style={{ width: '100%' }}
                    formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                  />,
                )}
              </Form.Item>
            </Col>
            {/* <Col span={6}>
              <Form.Item label="收入状态">
                {getFieldDecorator('status', {
                  initialValue: 'planned',
                })(
                  <Select
                    placeholder="请选择收入状态"
                    onChange={this.handleStatusChange}
                  >
                    {REVENUE_STATUS.map((status) => (
                      <Option key={status.value} value={status.value}>{status.label}</Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col> */}
          </Row>

          {/* 金额信息 */}
          <Row gutter={16}>


            {/* <Col span={6}>
              <Form.Item label="预计收入时间">
                {getFieldDecorator('plannedDate', {
                  rules: formRules.plannedDate,
                })(
                  <DatePicker style={{ width: '100%' }} />,
                )}
              </Form.Item>
            </Col> */}

          </Row>


          {/* 其他信息 */}
          {/* <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="里程碑描述">
                {getFieldDecorator('milestone')(
                  <TextArea
                    placeholder="请输入里程碑描述"
                    rows={3}
                  />,
                )}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="付款条件">
                {getFieldDecorator('paymentTerms')(
                  <TextArea
                    placeholder="请输入付款条件"
                    rows={3}
                  />,
                )}
              </Form.Item>
            </Col>
          </Row> */}

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="备注说明">
                {getFieldDecorator('notes')(
                  <TextArea
                    placeholder="请输入备注说明"
                    rows={3}
                  />,
                )}
              </Form.Item>
            </Col>
          </Row>

          {!hideActions && (
            <Row gutter={16} style={{ marginTop: 24 }}>
              <Col span={24} style={{ textAlign: 'center' }}>
                <Button onClick={this.handleReset} style={{ marginRight: 16 }}>
                  重置
                </Button>
                <Button type="primary" loading={loading} htmlType="submit">
                  保存收入
                </Button>
              </Col>
            </Row>
          )}
        </Form>
      </Card>
    );
  }
}

export default Form.create()(RevenueForm);
