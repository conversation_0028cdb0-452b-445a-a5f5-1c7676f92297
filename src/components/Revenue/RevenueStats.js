import React, { Component } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Select,
  DatePicker,
  Spin,
  message,
} from 'antd';
import {
  LineChart,
  Line,
  <PERSON>Chart,
  Bar,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import moment from 'moment';
import {
  // REVENUE_STATUS,
  // REVENUE_TYPE,
  getStatusConfig,
  getTypeLabel,
  formatAmount,
} from '../../utils/revenueUtils';
import { revenueAPI } from '../../services/api';

const { Option } = Select;
const { RangePicker } = DatePicker;

// 图表颜色配置
const CHART_COLORS = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2', '#eb2f96', '#fa8c16'];

class RevenueStats extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      statsData: null,
      filters: {
        dateRange: [moment().subtract(12, 'months'), moment()],
        projectId: '',
      },
    };
  }

  componentDidMount() {
    this.loadStats();
  }

  componentDidUpdate(prevProps) {
    // 如果项目ID变化，重新加载统计数据
    if (prevProps.projectId !== this.props.projectId) {
      this.loadStats();
    }
  }

  loadStats = async () => {
    this.setState({ loading: true });

    try {
      const { filters } = this.state;

      const params = {};

      // 处理日期范围
      if (filters.dateRange && filters.dateRange.length === 2) {
        params.startDate = filters.dateRange[0].format('YYYY-MM-DD');
        params.endDate = filters.dateRange[1].format('YYYY-MM-DD');
      }

      // 如果传入了项目ID，优先使用传入的项目ID
      if (this.props.projectId) {
        params.projectId = this.props.projectId;
      } else if (filters.projectId) {
        params.projectId = filters.projectId;
      }

      const response = await revenueAPI.getRevenueStats(params);

      if (response.success) {
        this.setState({ statsData: response.data });
      } else {
        message.error(response.message || '获取统计数据失败');
      }
    } catch (error) {
      console.error('Load stats failed:', error);
      message.error('获取统计数据失败');
    } finally {
      this.setState({ loading: false });
    }
  };

  handleFilterChange = (key, value) => {
    this.setState((prevState) => ({
      filters: {
        ...prevState.filters,
        [key]: value,
      },
    }), () => {
      this.loadStats();
    });
  };

  renderOverviewCards = () => {
    const { statsData } = this.state;
    if (!statsData) return null;

    const completionRate = statsData.totalPlannedRevenue > 0
      ? Math.round((statsData.totalReceivedRevenue / statsData.totalPlannedRevenue) * 100)
      : 0;

    return (
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="预计总收入"
              value={statsData.totalPlannedRevenue}
              formatter={(value) => formatAmount(value)}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="实际总收入"
              value={statsData.totalActualRevenue}
              formatter={(value) => formatAmount(value)}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已收款金额"
              value={statsData.totalReceivedRevenue}
              formatter={(value) => formatAmount(value)}
              valueStyle={{ color: '#13c2c2' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="收款完成率"
              value={completionRate}
              suffix="%"
              valueStyle={{ color: completionRate >= 80 ? '#52c41a' : '#faad14' }}
            />
            <Progress
              percent={completionRate}
              size="small"
              status={completionRate >= 100 ? 'success' : 'active'}
              style={{ marginTop: 8 }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  renderStatusChart = () => {
    const { statsData } = this.state;
    if (!statsData || !statsData.revenueByStatus) return null;

    const data = statsData.revenueByStatus.map((item) => ({
      name: getStatusConfig(item.status).label,
      value: item.totalAmount,
      count: item.count,
    }));

    return (
      <Card title="按状态分布" style={{ marginBottom: 24 }}>
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${entry.name + index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
              ))}
            </Pie>
            <Tooltip formatter={(value) => formatAmount(value)} />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </Card>
    );
  };

  renderTypeChart = () => {
    const { statsData } = this.state;
    if (!statsData || !statsData.revenueByType) return null;

    const data = statsData.revenueByType.map((item) => ({
      name: getTypeLabel(item.type),
      amount: item.totalAmount,
      count: item.count,
    }));

    return (
      <Card title="按类型分布" style={{ marginBottom: 24 }}>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis tickFormatter={(value) => `¥${(value / 10000).toFixed(0)}万`} />
            <Tooltip formatter={(value) => formatAmount(value)} />
            <Legend />
            <Bar dataKey="amount" fill="#1890ff" />
          </BarChart>
        </ResponsiveContainer>
      </Card>
    );
  };

  renderTrendChart = () => {
    const { statsData } = this.state;
    if (!statsData || !statsData.monthlyRevenueTrend) return null;

    const data = statsData.monthlyRevenueTrend.map((item) => ({
      month: item.month,
      planned: item.plannedAmount,
      actual: item.actualAmount,
    }));

    return (
      <Card title="收入趋势" style={{ marginBottom: 24 }}>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis tickFormatter={(value) => `¥${(value / 10000).toFixed(0)}万`} />
            <Tooltip formatter={(value) => formatAmount(value)} />
            <Legend />
            <Line
              type="monotone"
              dataKey="planned"
              stroke="#1890ff"
              name="预计收入"
              strokeDasharray="5 5"
            />
            <Line
              type="monotone"
              dataKey="actual"
              stroke="#52c41a"
              name="实际收入"
            />
          </LineChart>
        </ResponsiveContainer>
      </Card>
    );
  };

  renderStatusTable = () => {
    const { statsData } = this.state;
    if (!statsData || !statsData.revenueByStatus) return null;

    const columns = [
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: (status) => {
          const config = getStatusConfig(status);
          return <span style={{ color: config.color }}>{config.label}</span>;
        },
      },
      {
        title: '数量',
        dataIndex: 'count',
        key: 'count',
      },
      {
        title: '金额',
        dataIndex: 'totalAmount',
        key: 'totalAmount',
        render: (amount) => formatAmount(amount),
      },
      {
        title: '占比',
        key: 'percentage',
        render: (_, record) => {
          const total = statsData.revenueByStatus.reduce((sum, item) => sum + item.totalAmount, 0);
          const percentage = total > 0 ? ((record.totalAmount / total) * 100).toFixed(1) : 0;
          return `${percentage}%`;
        },
      },
    ];

    return (
      <Card title="状态统计详情" style={{ marginBottom: 24 }}>
        <Table
          columns={columns}
          dataSource={statsData.revenueByStatus}
          pagination={false}
          size="small"
          rowKey="status"
        />
      </Card>
    );
  };

  render() {
    const { loading, filters } = this.state;

    return (
      <div>
        {/* 筛选条件 */}
        <Card style={{ marginBottom: 24 }}>
          <Row gutter={16}>
            <Col span={8}>
              <RangePicker
                value={filters.dateRange}
                onChange={(dates) => this.handleFilterChange('dateRange', dates)}
                style={{ width: '100%' }}
                placeholder={['开始日期', '结束日期']}
              />
            </Col>
            <Col span={8}>
              <Select
                placeholder="选择项目"
                value={filters.projectId}
                onChange={(value) => this.handleFilterChange('projectId', value)}
                allowClear
                style={{ width: '100%' }}
              >
                {/* 这里可以从props或API获取项目列表 */}
                <Option value="">全部项目</Option>
              </Select>
            </Col>
          </Row>
        </Card>

        <Spin spinning={loading}>
          {/* 概览卡片 */}
          {this.renderOverviewCards()}

          <Row gutter={16}>
            <Col span={12}>
              {this.renderStatusChart()}
            </Col>
            <Col span={12}>
              {this.renderTypeChart()}
            </Col>
          </Row>

          {/* 趋势图表 */}
          {this.renderTrendChart()}

          <Row gutter={16}>
            <Col span={12}>
              {this.renderStatusTable()}
            </Col>
            <Col span={12}>
              {/* 可以添加更多统计表格 */}
            </Col>
          </Row>
        </Spin>
      </div>
    );
  }
}

export default RevenueStats;
