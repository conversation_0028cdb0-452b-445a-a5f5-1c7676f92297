import React, { Component } from 'react';
import {
  Modal,
  Form,
  InputNumber,
  DatePicker,
  Input,
  Button,
  message,
  Row,
  Col,
  Divider,
  Typography,
} from 'antd';
import moment from 'moment';
import { formatAmount } from '../../utils/revenueUtils';

const { TextArea } = Input;
const { Text } = Typography;

class ConfirmRevenueModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
    };
  }

  handleSubmit = (e) => {
    e.preventDefault();
    this.props.form.validateFields((err, values) => {
      if (err) {
        return;
      }

      this.setState({ loading: true });

      const confirmData = {
        actualAmount: values.actualAmount,
        confirmedDate: values.confirmedDate ? values.confirmedDate.format('YYYY-MM-DD') : null,
        notes: values.notes,
      };

      this.props.onConfirm(confirmData)
        .then(() => {
          message.success('收入确认成功');
          this.props.onCancel();
        })
        .catch((error) => {
          console.error('Confirm revenue failed:', error);
          message.error('收入确认失败');
        })
        .finally(() => {
          this.setState({ loading: false });
        });
    });
  };

  render() {
    const { visible, onCancel, revenue } = this.props;
    const { getFieldDecorator } = this.props.form;
    const { loading } = this.state;

    if (!revenue) return null;

    return (
      <Modal
        title="确认收入"
        visible={visible}
        onCancel={onCancel}
        footer={null}
        width={600}
        destroyOnClose
      >
        <div style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Text strong>收入标题：</Text>
              <Text>{revenue.title}</Text>
            </Col>
            <Col span={12}>
              <Text strong>预计金额：</Text>
              <Text>{formatAmount(revenue.plannedAmount)}</Text>
            </Col>
          </Row>
        </div>

        <Divider />

        <Form layout="vertical" onSubmit={this.handleSubmit}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="实际收入金额" required>
                {getFieldDecorator('actualAmount', {
                  initialValue: revenue.plannedAmount,
                  rules: [
                    { required: true, message: '请输入实际收入金额' },
                    { type: 'number', min: 0, message: '金额必须大于等于0' },
                  ],
                })(
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder="请输入实际收入金额"
                    formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                    precision={2}
                  />,
                )}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="确认收入时间">
                {getFieldDecorator('confirmedDate', {
                  initialValue: moment(),
                })(
                  <DatePicker
                    style={{ width: '100%' }}
                    placeholder="请选择确认收入时间"
                  />,
                )}
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="确认备注">
            {getFieldDecorator('notes')(
              <TextArea
                rows={4}
                placeholder="请输入确认备注（可选）"
              />,
            )}
          </Form.Item>

          <Form.Item style={{ textAlign: 'right', marginTop: 24 }}>
            <Button onClick={onCancel} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
            >
              确认收入
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    );
  }
}

export default Form.create()(ConfirmRevenueModal);
