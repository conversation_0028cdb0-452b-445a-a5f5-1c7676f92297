import React, { Component } from 'react';
import {
  Table,
  Button,
  Tag,
  Popconfirm,
  message,
  // Input,
  Select,
  DatePicker,
  Row,
  Col,
  Card,
  Tooltip,
  // Progress,
  Dropdown,
  Menu,
  Icon,
  Modal,
} from 'antd';
// import moment from 'moment';
import {
  REVENUE_STATUS,
  REVENUE_TYPE,
  // SORT_OPTIONS,
  getStatusConfig,
  getTypeLabel,
  formatAmount,
  formatDate,
  // calculateCompletionRate,
  isOverdue,
  getNextStatusOptions,
} from '../../utils/revenueUtils';
import { revenueAPI } from '../../services/api';
import RevenueForm from './RevenueForm';
import ConfirmRevenueModal from './ConfirmRevenueModal';
import BatchConfirmRevenueModal from './BatchConfirmRevenueModal';
// import { ProjectPermissionGuard } from '../Auth/PermissionGuard';

const { Option } = Select;
const { RangePicker } = DatePicker;
// const { Search } = Input;

class RevenueTable extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      loading: false,
      selectedRowKeys: [],
      editingRevenue: null,
      modalVisible: null,
      confirmModalVisible: false,
      batchConfirmModalVisible: false,
      confirmingRevenue: null,
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0,
      },
      filters: {
        projectId: '',
        status: undefined,
        revenueType: undefined,
        dateRange: [],
        search: '',
      },
      sorter: {
        sortBy: 'createdAt',
        sortOrder: 'desc',
      },
    };
  }

  componentDidMount() {
    this.loadData();
  }

  componentDidUpdate(prevProps) {
    // 如果项目ID变化，重新加载数据
    if (prevProps.projectId !== this.props.projectId) {
      this.loadData();
    }
  }

  loadData = async () => {
    this.setState({ loading: true });

    try {
      const { pagination, filters, sorter } = this.state;

      const params = {
        page: pagination.current,
        pageSize: pagination.pageSize,
        sortBy: sorter.sortBy,
        sortOrder: sorter.sortOrder,
        ...filters,
      };

      // 如果传入了项目ID，优先使用传入的项目ID
      if (this.props.projectId) {
        params.projectId = this.props.projectId;
      }

      // 处理日期范围
      if (filters.dateRange && filters.dateRange.length === 2) {
        params.startDate = filters.dateRange[0].format('YYYY-MM-DD');
        params.endDate = filters.dateRange[1].format('YYYY-MM-DD');
      }

      // 移除空值
      Object.keys(params).forEach((key) => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key];
        }
      });

      const response = await revenueAPI.getRevenues(params);

      if (response.success) {
        this.setState({
          dataSource: response.data.revenues || [],
          pagination: {
            ...pagination,
            total: response.data.total || 0,
          },
        });

        // 通知父组件数据变化
        if (this.props.onDataChange) {
          this.props.onDataChange(response.data.revenues || []);
        }
      } else {
        message.error(response.message || '获取收入列表失败');
      }
    } catch (error) {
      console.error('Load revenues failed:', error);
      message.error('获取收入列表失败');
    } finally {
      this.setState({ loading: false });
    }
  };

  handleTableChange = (pagination, filters, sorter) => {
    const newSorter = {
      sortBy: sorter.field || 'plannedDate',
      sortOrder: sorter.order === 'ascend' ? 'asc' : 'desc',
    };

    this.setState((prevState) => ({
      pagination: {
        ...prevState.pagination,
        current: pagination.current,
        pageSize: pagination.pageSize,
      },
      sorter: newSorter,
    }), () => {
      this.loadData();
    });
  };

  handleFilterChange = (key, value) => {
    this.setState((prevState) => ({
      filters: {
        ...prevState.filters,
        [key]: value,
      },
      pagination: {
        ...prevState.pagination,
        current: 1, // 重置到第一页
      },
    }), () => {
      this.loadData();
    });
  };

  handleSearch = (value) => {
    this.handleFilterChange('search', value);
  };

  handleReset = () => {
    this.setState((prevState) => ({
      filters: {
        projectId: '',
        status: undefined,
        revenueType: undefined,
        dateRange: [],
        search: '',
      },
      pagination: {
        ...prevState.pagination,
        current: 1,
      },
    }), () => {
      this.loadData();
    });
  };

  handleDelete = async (id) => {
    try {
      const response = await revenueAPI.deleteRevenue(id);
      if (response.success) {
        message.success('删除成功');
        this.loadData();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      console.error('Delete revenue failed:', error);
      message.error('删除失败');
    }
  };

  handleStatusChange = async (id, newStatus) => {
    try {
      const response = await revenueAPI.updateRevenue(id, { status: newStatus });
      if (response.success) {
        message.success('状态更新成功');
        this.loadData();
      } else {
        message.error(response.message || '状态更新失败');
      }
    } catch (error) {
      console.error('Update status failed:', error);
      message.error('状态更新失败');
    }
  };

  handleBatchStatusChange = async (status) => {
    const { selectedRowKeys } = this.state;
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要操作的收入记录');
      return;
    }

    try {
      const response = await revenueAPI.batchUpdateStatus(selectedRowKeys, status);
      if (response.success) {
        message.success('批量状态更新成功');
        this.setState({ selectedRowKeys: [] });
        this.loadData();
      } else {
        message.error(response.message || '批量状态更新失败');
      }
    } catch (error) {
      console.error('Batch update status failed:', error);
      message.error('批量状态更新失败');
    }
  };


   handleModalCancel = () => {
     this.setState({
       modalVisible: false,
       editingRevenue: null,
     });
   };

   handleFormSubmit = (data) => {
     console.log('Revenue saved:', data);
     this.setState({
       modalVisible: false,
       editingRevenue: null,
     });
     // 重新加载项目收入数据
     this.loadData();
   };

  handleAdd = () => {
    this.setState({
      modalVisible: true,
      editingRevenue: null,
    });
  };

  // 单笔确认收入
  handleConfirmRevenue = (revenue) => {
    this.setState({
      confirmModalVisible: true,
      confirmingRevenue: revenue,
    });
  };

  // 批量确认收入
  handleBatchConfirmRevenue = () => {
    const { selectedRowKeys, dataSource } = this.state;
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要确认的收入记录');
      return;
    }

    // 过滤出可以确认的收入（状态为pending的）
    const selectedRevenues = dataSource.filter((revenue) =>
      selectedRowKeys.includes(revenue.id) && revenue.status === 'receiving');

    if (selectedRevenues.length === 0) {
      message.warning('所选收入记录中没有可确认的记录（只能确认待确认状态的收入）');
      return;
    }

    this.setState({
      batchConfirmModalVisible: true,
    });
  };

  // 确认收入提交
  handleConfirmSubmit = async (confirmData) => {
    const { confirmingRevenue } = this.state;
    try {
      const response = await revenueAPI.confirmRevenue(confirmingRevenue.id, confirmData);
      if (response.success) {
        this.loadData();
        return Promise.resolve();
      } else {
        return Promise.reject(new Error(response.message || '确认失败'));
      }
    } catch (error) {
      return Promise.reject(error);
    }
  };

  // 批量确认收入提交
  handleBatchConfirmSubmit = async (revenuesData) => {
    try {
      const response = await revenueAPI.batchConfirmRevenues(revenuesData);
      if (response.success) {
        this.setState({ selectedRowKeys: [] });
        this.loadData();
        return Promise.resolve();
      } else {
        return Promise.reject(new Error(response.message || '批量确认失败'));
      }
    } catch (error) {
      return Promise.reject(error);
    }
  };

  // 关闭确认模态框
  handleConfirmModalCancel = () => {
    this.setState({
      confirmModalVisible: false,
      batchConfirmModalVisible: false,
      confirmingRevenue: null,
    });
  };

  getColumns = () => {
    return [
      {
        title: '收入标题',
        dataIndex: 'title',
        key: 'title',
        width: 200,
        ellipsis: true,
        render: (text, record) => (
          <Tooltip title={text}>
            <span style={{ cursor: 'pointer' }} onClick={() => this.props.onView && this.props.onView(record)}>
              {text}
            </span>
          </Tooltip>
        ),
      },
      {
        title: '收入类型',
        dataIndex: 'revenueType',
        key: 'revenueType',
        width: 120,
        render: (type) => getTypeLabel(type),
      },
      {
        title: '收入状态',
        dataIndex: 'status',
        key: 'status',
        width: 120,
        render: (status, record) => {
          const config = getStatusConfig(status);
          const overdue = isOverdue(record.plannedDate, status);
          return (
            <Tag color={overdue ? '#f5222d' : config.color}>
              {overdue ? '逾期' : config.label}
            </Tag>
          );
        },
      },
      {
        title: '预计金额',
        dataIndex: 'plannedAmount',
        key: 'plannedAmount',
        width: 120,
        sorter: true,
        render: (amount) => formatAmount(amount),
      },
      {
        title: '实际金额',
        dataIndex: 'actualAmount',
        key: 'actualAmount',
        width: 120,
        sorter: true,
        render: (amount) => formatAmount(amount),
      },
      // {
      //   title: '完成率',
      //   key: 'completionRate',
      //   width: 120,
      //   render: (_, record) => {
      //     const rate = calculateCompletionRate(record.actualAmount, record.plannedAmount);
      //     let progressStatus = 'normal';
      //     if (rate >= 100) {
      //       progressStatus = 'success';
      //     } else if (rate >= 80) {
      //       progressStatus = 'active';
      //     }
      //     return (
      //       <Progress
      //         percent={rate}
      //         size="small"
      //         status={progressStatus}
      //       />
      //     );
      //   },
      // },
      // {
      //   title: '预计时间',
      //   dataIndex: 'plannedDate',
      //   key: 'plannedDate',
      //   width: 120,
      //   sorter: true,
      //   render: (date) => formatDate(date),
      // },
      {
        title: '收款时间',
        dataIndex: 'receivedDate',
        key: 'receivedDate',
        width: 120,
        render: (date) => formatDate(date),
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        fixed: 'right',
        render: (_, record) => {
          const nextStatuses = getNextStatusOptions(record.status);

          const statusMenu = (
            <Menu onClick={({ key }) => this.handleStatusChange(record.id, key)}>
              {nextStatuses.map((status) => {
                const config = getStatusConfig(status);
                return (
                  <Menu.Item key={status}>
                    <Tag color={config.color}>{config.label}</Tag>
                  </Menu.Item>
                );
              })}
            </Menu>
          );

          return (
            <span>
              {/* <ProjectPermissionGuard action="edit"> */}
              <Button
                type="link"
                size="small"
                onClick={() => this.props.onEdit && this.props.onEdit(record)}
              >
                编辑
              </Button>
              {/* </ProjectPermissionGuard> */}

              {/* 确认收入按钮 - 只有待确认状态的收入才显示 */}
              {record.status === 'receiving' && (
                <Button
                  type="link"
                  size="small"
                  onClick={() => this.handleConfirmRevenue(record)}
                  style={{ color: '#52c41a' }}
                >
                  确认收入
                </Button>
              )}

              {nextStatuses.length > 0 && (
              // <ProjectPermissionGuard action="edit">
              <Dropdown overlay={statusMenu} trigger={['click']}>
                <Button type="link" size="small">
                  状态 <Icon type="down" />
                </Button>
              </Dropdown>
              // </ProjectPermissionGuard>
              )}

              {/* <ProjectPermissionGuard action="delete"> */}
              <Popconfirm
                title="确定要删除这条收入记录吗？"
                onConfirm={() => this.handleDelete(record.id)}
                okText="确定"
                cancelText="取消"
              >
                <Button type="link" size="small" style={{ color: '#ff4d4f' }}>
                  删除
                </Button>
              </Popconfirm>
              {/* </ProjectPermissionGuard> */}
            </span>
          );
        },
      },
    ];
  };


  render() {
    const {
      dataSource,
      loading,
      selectedRowKeys,
      pagination,
      filters,
      modalVisible,
      editingRevenue,
      confirmModalVisible,
      batchConfirmModalVisible,
      confirmingRevenue,
    } = this.state;

    const rowSelection = {
      selectedRowKeys,
      onChange: (keys) => this.setState({ selectedRowKeys: keys }),
    };

    // const batchMenu = (
    //   <Menu onClick={({ key }) => this.handleBatchStatusChange(key)}>
    //     {REVENUE_STATUS.map((status) => (
    //       <Menu.Item key={status.value}>
    //         <Tag color={status.color}>{status.label}</Tag>
    //       </Menu.Item>
    //     ))}
    //   </Menu>
    // );


    return (
      <Card title="收入管理">
        {/* 筛选条件 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={4}>
            <Select
              placeholder="收入状态"
              value={filters.status}
              onChange={(value) => this.handleFilterChange('status', value)}
              allowClear
              style={{ width: '100%' }}
            >
              {REVENUE_STATUS.map((status) => (
                <Option key={status.value} value={status.value}>{status.label}</Option>
              ))}
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="收入类型"
              value={filters.revenueType}
              onChange={(value) => this.handleFilterChange('revenueType', value)}
              allowClear
              style={{ width: '100%' }}
            >
              {REVENUE_TYPE.map((type) => (
                <Option key={type.value} value={type.value}>{type.label}</Option>
              ))}
            </Select>
          </Col>
          <Col span={6}>
            <RangePicker
              value={filters.dateRange}
              onChange={(dates) => this.handleFilterChange('dateRange', dates)}
              style={{ width: '100%' }}
              placeholder={['开始日期', '结束日期']}
            />
          </Col>
          <Col span={4}>
            <Button onClick={this.handleReset}>重置</Button>
            <Button type="primary" onClick={this.handleAdd} style={{ marginLeft: 8 }}>新增收入</Button>
          </Col>
        </Row>

        {/* 批量操作 */}
        {selectedRowKeys.length > 0 && (
          <Row style={{ marginBottom: 16 }}>
            <Col span={24}>
              <span style={{ marginRight: 8 }}>
                已选择 {selectedRowKeys.length} 项
              </span>
              {/* <ProjectPermissionGuard action="edit" /> */}

              {/* <Dropdown overlay={batchMenu} trigger={['click']}>
                <Button size="small">
                  批量更新状态
                  <Icon type="down" />
                </Button>
              </Dropdown> */}

              <Button
                size="small"
                type="primary"
                onClick={this.handleBatchConfirmRevenue}
                style={{ marginLeft: 8 }}
              >
                批量确认收入
              </Button>
            </Col>
          </Row>
        )}

        {/* 数据表格 */}
        <Table
          rowSelection={rowSelection}
          columns={this.getColumns()}
          dataSource={dataSource}
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={this.handleTableChange}
          rowKey="id"
          scroll={{ x: 1200 }}
        />
        <Modal
          title={editingRevenue ? '编辑收入' : '新建收入'}
          visible={modalVisible}
          onCancel={this.handleModalCancel}
          footer={null}
          width={1000}
          destroyOnClose
        >
          <RevenueForm
            initialData={editingRevenue}
            projectName={this.props.projectName}
            projectId={this.props.projectId}
            onSubmit={this.handleFormSubmit}
            hideActions={false}
          />
        </Modal>

        {/* 单笔确认收入模态框 */}
        <ConfirmRevenueModal
          visible={confirmModalVisible}
          revenue={confirmingRevenue}
          onConfirm={this.handleConfirmSubmit}
          onCancel={this.handleConfirmModalCancel}
        />

        {/* 批量确认收入模态框 */}
        <BatchConfirmRevenueModal
          visible={batchConfirmModalVisible}
          revenues={dataSource.filter((revenue) =>
            selectedRowKeys.includes(revenue.id) && revenue.status === 'receiving')}
          onConfirm={this.handleBatchConfirmSubmit}
          onCancel={this.handleConfirmModalCancel}
        />
      </Card>

    );
  }
}

export default RevenueTable;
