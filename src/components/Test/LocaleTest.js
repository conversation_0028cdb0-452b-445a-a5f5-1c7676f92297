import React, { Component } from 'react';
import {
  Card,
  DatePicker,
  TimePicker,
  Row,
  Col,
  // Form,
  Button,
  message,
  Divider,
  Typography,
  Table,
  // Pagination,
  Select,
  Modal,
  Popconfirm,
} from 'antd';
import { localeUtils } from '../../config/locale';
import moment from 'moment';

const { Title, Text } = Typography;
const { RangePicker, WeekPicker, MonthPicker } = DatePicker;
const { Option } = Select;

class LocaleTest extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedDate: null,
      selectedDateRange: null,
      selectedWeek: null,
      selectedMonth: null,
      selectedTime: null,
      modalVisible: false,
      tableData: [
        {
          key: '1',
          name: '项目A',
          amount: 150000,
          percent: 85.5,
          date: '2024-01-15',
          status: 'active',
        },
        {
          key: '2',
          name: '项目B',
          amount: 280000,
          percent: 92.3,
          date: '2024-02-20',
          status: 'completed',
        },
        {
          key: '3',
          name: '项目C',
          amount: 95000,
          percent: 67.8,
          date: '2024-03-10',
          status: 'pending',
        },
      ],
    };
  }

  handleDateChange = (field) => (date) => {
    this.setState({ [field]: date });
    if (date) {
      message.success(`选择了日期: ${date.format('YYYY-MM-DD')}`);
    }
  };

  handleDateRangeChange = (dates) => {
    this.setState({ selectedDateRange: dates });
    if (dates && dates.length === 2) {
      message.success(`选择了日期范围: ${dates[0].format('YYYY-MM-DD')} 至 ${dates[1].format('YYYY-MM-DD')}`);
    }
  };

  showModal = () => {
    this.setState({ modalVisible: true });
  };

  handleModalOk = () => {
    this.setState({ modalVisible: false });
    message.success('操作成功！');
  };

  handleModalCancel = () => {
    this.setState({ modalVisible: false });
  };

  handleDelete = () => {
    message.success('删除成功！');
  };

  render() {
    const {
      selectedDate,
      selectedDateRange,
      selectedWeek,
      selectedMonth,
      selectedTime,
      modalVisible,
      tableData,
    } = this.state;

    const columns = [
      {
        title: '项目名称',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '金额',
        dataIndex: 'amount',
        key: 'amount',
        render: (amount) => localeUtils.formatCurrency(amount),
      },
      {
        title: '完成率',
        dataIndex: 'percent',
        key: 'percent',
        render: (percent) => localeUtils.formatPercent(percent),
      },
      {
        title: '创建日期',
        dataIndex: 'date',
        key: 'date',
        render: (date) => localeUtils.formatDate(date),
      },
      {
        title: '相对时间',
        dataIndex: 'date',
        key: 'relativeTime',
        render: (date) => localeUtils.getRelativeTime(date),
      },
      {
        title: '操作',
        key: 'action',
        render: () => (
          <div>
            <Button type="link" size="small" onClick={this.showModal}>
              编辑
            </Button>
            <Popconfirm
              title="确定要删除这个项目吗？"
              onConfirm={this.handleDelete}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" size="small" style={{ color: '#ff4d4f' }}>
                删除
              </Button>
            </Popconfirm>
          </div>
        ),
      },
    ];

    return (
      <div style={{ padding: '24px' }}>
        <Title level={2}>国际化测试页面</Title>
        <Text type="secondary">测试 Ant Design 组件的中文本地化效果</Text>

        <Divider />

        {/* 日期选择器测试 */}
        <Card title="日期选择器测试" style={{ marginBottom: 24 }}>
          <Row gutter={[16, 16]}>
            <Col span={6}>
              <Text strong>日期选择器:</Text>
              <DatePicker
                style={{ width: '100%', marginTop: 8 }}
                placeholder="请选择日期"
                value={selectedDate}
                onChange={this.handleDateChange('selectedDate')}
              />
            </Col>
            <Col span={6}>
              <Text strong>日期范围选择器:</Text>
              <RangePicker
                style={{ width: '100%', marginTop: 8 }}
                placeholder={['开始日期', '结束日期']}
                value={selectedDateRange}
                onChange={this.handleDateRangeChange}
              />
            </Col>
            <Col span={6}>
              <Text strong>周选择器:</Text>
              <WeekPicker
                style={{ width: '100%', marginTop: 8 }}
                placeholder="请选择周"
                value={selectedWeek}
                onChange={this.handleDateChange('selectedWeek')}
              />
            </Col>
            <Col span={6}>
              <Text strong>月份选择器:</Text>
              <MonthPicker
                style={{ width: '100%', marginTop: 8 }}
                placeholder="请选择月份"
                value={selectedMonth}
                onChange={this.handleDateChange('selectedMonth')}
              />
            </Col>
          </Row>
          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col span={6}>
              <Text strong>时间选择器:</Text>
              <TimePicker
                style={{ width: '100%', marginTop: 8 }}
                placeholder="请选择时间"
                value={selectedTime}
                onChange={this.handleDateChange('selectedTime')}
              />
            </Col>
          </Row>
        </Card>

        {/* 格式化工具测试 */}
        <Card title="格式化工具测试" style={{ marginBottom: 24 }}>
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Text strong>货币格式化:</Text>
              <div style={{ marginTop: 8 }}>
                <div>原始值: 150000</div>
                <div>格式化后: {localeUtils.formatCurrency(150000)}</div>
              </div>
            </Col>
            <Col span={8}>
              <Text strong>百分比格式化:</Text>
              <div style={{ marginTop: 8 }}>
                <div>原始值: 85.5</div>
                <div>格式化后: {localeUtils.formatPercent(85.5)}</div>
              </div>
            </Col>
            <Col span={8}>
              <Text strong>日期格式化:</Text>
              <div style={{ marginTop: 8 }}>
                <div>当前时间: {localeUtils.formatDate(new Date())}</div>
                <div>相对时间: {localeUtils.getRelativeTime(moment().subtract(2, 'hours'))}</div>
              </div>
            </Col>
          </Row>
        </Card>

        {/* 表格和分页测试 */}
        <Card title="表格和分页测试" style={{ marginBottom: 24 }}>
          <Table
            columns={columns}
            dataSource={tableData}
            pagination={{
              total: 50,
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
              pageSizeOptions: ['10', '20', '50', '100'],
            }}
          />
        </Card>

        {/* 其他组件测试 */}
        <Card title="其他组件测试">
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Text strong>下拉选择:</Text>
              <Select
                style={{ width: '100%', marginTop: 8 }}
                placeholder="请选择状态"
                allowClear
              >
                <Option value="active">启用</Option>
                <Option value="inactive">禁用</Option>
                <Option value="pending">待审核</Option>
              </Select>
            </Col>
            <Col span={8}>
              <Button type="primary" onClick={this.showModal}>
                打开模态框
              </Button>
            </Col>
          </Row>
        </Card>

        {/* 模态框测试 */}
        <Modal
          title="模态框测试"
          visible={modalVisible}
          onOk={this.handleModalOk}
          onCancel={this.handleModalCancel}
          okText="确定"
          cancelText="取消"
        >
          <p>这是一个测试模态框，用于验证按钮文本的中文显示。</p>
          <DatePicker
            style={{ width: '100%' }}
            placeholder="在模态框中选择日期"
          />
        </Modal>
      </div>
    );
  }
}

export default LocaleTest;
