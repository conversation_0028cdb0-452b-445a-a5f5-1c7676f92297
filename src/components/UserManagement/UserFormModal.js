import React, { useState } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Row,
  Col,
  Avatar,
  Upload,
  Button,
} from 'antd';
import { userApi } from '../../services/api';
import { useDepartments } from '../../store/hooks';

const { Option } = Select;
const { TextArea } = Input;

const UserFormModal = ({ visible, user, roles, onCancel, onSuccess, form }) => {
  const [loading, setLoading] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState(user?.avatar || '');

  const { departments } = useDepartments();
  console.log('[ departments ] >', departments);

  const isEdit = !!user;

  // 表单提交
  const handleSubmit = () => {
    form.validateFields((err, values) => {
      if (err) {
        message.error('请检查表单填写是否正确');
        return;
      }

      submitForm(values);
    });
  };

  const submitForm = async (values) => {
    try {
      setLoading(true);

      const formData = {
        ...values,
        avatar: avatarUrl,
      };

      let response;
      if (user) {
        // 编辑用户
        response = await userApi.updateUser(user.id, formData);
      } else {
        // 新增用户 - 通常用户是通过钉钉同步创建的，这里主要用于更新信息
        message.info('用户通常通过钉钉同步创建，请使用同步功能');
        return;
      }

      if (response.success) {
        onSuccess();
      } else {
        message.error(response.message || '操作失败');
      }
    } catch (error) {
      console.error('User form submit failed:', error);
      message.error('操作失败');
    } finally {
      setLoading(false);
    }
  };

  // 头像上传前处理
  const beforeUpload = (file) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('只能上传 JPG/PNG 格式的图片!');
      return false;
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('图片大小不能超过 2MB!');
      return false;
    }
    return true;
  };

  // 头像上传变化
  const handleAvatarChange = (info) => {
    if (info.file.status === 'uploading') {
      return;
    }
    if (info.file.status === 'done') {
      // 获取上传后的URL
      const newAvatarUrl = info.file.response?.data?.url || '';
      setAvatarUrl(newAvatarUrl);
    }
  };

  return (
    <Modal
      title={isEdit ? '编辑用户' : '新增用户'}
      visible={visible}
      onOk={handleSubmit}
      onCancel={onCancel}
      confirmLoading={loading}
      width={600}
      destroyOnClose
    >
      <Form layout="vertical">
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item label="头像">
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Avatar
                  size={64}
                  src={avatarUrl}
                  icon="user"
                  style={{ marginRight: 16 }}
                />
                <Upload
                  name="file"
                  action="/api/upload"
                  beforeUpload={beforeUpload}
                  onChange={handleAvatarChange}
                  showUploadList={false}
                >
                  <Button>更换头像</Button>
                </Upload>
              </div>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="姓名">
              {form.getFieldDecorator('name', {
                initialValue: user?.name || '',
                rules: [
                  { required: true, message: '请输入姓名' },
                  { max: 50, message: '姓名不能超过50个字符' },
                ],
              })(
                <Input placeholder="请输入姓名" />,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="手机号">
              {form.getFieldDecorator('mobile', {
                initialValue: user?.mobile || '',
                rules: [
                  { required: true, message: '请输入手机号' },
                  {
                    pattern: /^1[3-9]\d{9}$/,
                    message: '请输入正确的手机号',
                  },
                ],
              })(
                <Input placeholder="请输入手机号" />,
              )}
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="邮箱">
              {form.getFieldDecorator('email', {
                initialValue: user?.email || '',
                rules: [
                  { type: 'email', message: '请输入正确的邮箱地址' },
                ],
              })(
                <Input placeholder="请输入邮箱" />,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="钉钉用户ID">
              {form.getFieldDecorator('dingTalkUserId', {
                initialValue: user?.dingTalkUserId || '',
              })(
                <Input placeholder="钉钉用户ID" disabled />,
              )}
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="所属部门">
              {form.getFieldDecorator('departmentId', {
                initialValue: user?.departmentId || undefined,
                rules: [{ required: true, message: '请选择所属部门' }],
              })(
                <Select placeholder="请选择所属部门">
                  {departments.map((dept) => (
                    <Option key={dept.id} value={dept.id}>
                      {dept.name}
                    </Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="职位">
              {form.getFieldDecorator('position', {
                initialValue: user?.position || '',
              })(
                <Input placeholder="请输入职位" />,
              )}
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="状态">
              {form.getFieldDecorator('status', {
                initialValue: user?.status || 'active',
                valuePropName: 'checked',
                getValueFromEvent: (checked) => (checked ? 'active' : 'inactive'),
                getValueProps: (value) => ({ checked: value === 'active' }),
              })(
                <Switch
                  checkedChildren="正常"
                  unCheckedChildren="禁用"
                />,
              )}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="默认角色">
              {form.getFieldDecorator('defaultRoleId', {
                initialValue: user?.roles?.[0]?.id || undefined,
              })(
                <Select placeholder="请选择默认角色" allowClear>
                  {roles.map((role) => (
                    <Option key={role.id} value={role.id}>
                      {role.name}
                    </Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
        </Row>

        <Form.Item label="备注">
          {form.getFieldDecorator('remark', {
            initialValue: user?.remark || '',
          })(
            <TextArea
              placeholder="请输入备注信息"
              rows={3}
              maxLength={200}
            />,
          )}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create()(UserFormModal);
