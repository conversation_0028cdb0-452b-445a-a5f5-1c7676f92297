import React, { useState } from 'react';
import {
  Modal,
  Form,
  Select,
  Switch,
  message,
  Alert,
  Progress,
  List,
  Avatar,
  Tag,
  Divider,
} from 'antd';
import { userApi } from '../../services/api';

const { Option } = Select;

const SyncUsersModal = ({ visible, onCancel, onSuccess, departments = [], form }) => {
  const [syncing, setSyncing] = useState(false);
  const [syncProgress, setSyncProgress] = useState(0);
  const [syncResults, setSyncResults] = useState(null);
  const [syncLogs, setSyncLogs] = useState([]);

  // 开始同步
  const handleSync = () => {
    form.validateFields((err, values) => {
      if (err) {
        message.error('请检查表单填写是否正确');
        return;
      }

      setSyncing(true);
      setSyncProgress(0);
      setSyncResults(null);
      setSyncLogs([]);

      // 模拟同步进度
      simulateProgress();

      // 调用同步API
      syncUsers(values);
    });
  };

  // 同步用户
  const syncUsers = async (values) => {
    try {
      const response = await userApi.syncDingTalkUsers(values);

      if (response.success) {
        setSyncResults(response.data);
        setSyncProgress(100);
        message.success('用户同步完成');
      } else {
        message.error(response.message || '用户同步失败');
      }
    } catch (error) {
      console.error('Sync users failed:', error);
      message.error('用户同步失败');
    } finally {
      setSyncing(false);
    }
  };

  // 模拟同步进度
  const simulateProgress = () => {
    let progress = 0;
    const timer = setInterval(() => {
      progress += Math.random() * 15;
      if (progress >= 90) {
        clearInterval(timer);
        progress = 90;
      }
      setSyncProgress(progress);
    }, 500);

    // 5秒后清除定时器
    setTimeout(() => {
      clearInterval(timer);
    }, 5000);
  };

  // 完成同步
  const handleComplete = () => {
    onSuccess();
  };

  return (
    <Modal
      title="同步钉钉用户"
      visible={visible}
      onOk={syncResults ? handleComplete : handleSync}
      onCancel={onCancel}
      confirmLoading={syncing}
      okText={syncResults ? '完成' : '开始同步'}
      width={600}
      destroyOnClose
    >
      {!syncing && !syncResults && (
      <div>
        <Alert
          message="同步说明"
          description={
            <div>
              <p>• 将从钉钉获取指定部门的用户信息并同步到系统中</p>
              <p>• 已存在的用户将更新基本信息，新用户将被创建</p>
              <p>• 同步过程中请勿关闭此窗口</p>
            </div>
              }
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Form
          form={form}
          layout="vertical"
          initialValues={{
            departmentIds: [],
            updateExisting: true,
            syncAvatar: true,
            autoActivate: false,
          }}
        >
          <Form.Item
            label="同步部门"
            name="departmentIds"
            rules={[{ required: true, message: '请选择要同步的部门' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择要同步的部门"
              style={{ width: '100%' }}
            >
              {departments.map((dept) => (
                <Option key={dept.id} value={dept.id}>
                  {dept.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item label="同步选项">
            <div>
              <div style={{ marginBottom: 8 }}>
                <Form.Item
                  name="updateExisting"
                  valuePropName="checked"
                  style={{ display: 'inline-block', margin: 0 }}
                >
                  <Switch size="small" />
                </Form.Item>
                <span style={{ marginLeft: 8 }}>更新已存在用户的信息</span>
              </div>
              <div style={{ marginBottom: 8 }}>
                <Form.Item
                  name="syncAvatar"
                  valuePropName="checked"
                  style={{ display: 'inline-block', margin: 0 }}
                >
                  <Switch size="small" />
                </Form.Item>
                <span style={{ marginLeft: 8 }}>同步用户头像</span>
              </div>
              <div>
                <Form.Item
                  name="autoActivate"
                  valuePropName="checked"
                  style={{ display: 'inline-block', margin: 0 }}
                >
                  <Switch size="small" />
                </Form.Item>
                <span style={{ marginLeft: 8 }}>自动激活新用户</span>
              </div>
            </div>
          </Form.Item>
        </Form>
      </div>
      )}

      {syncing && (
      <div>
        <div style={{ textAlign: 'center', marginBottom: 16 }}>
          <Progress
            type="circle"
            percent={Math.round(syncProgress)}
            status={syncProgress < 100 ? 'active' : 'success'}
          />
        </div>
        <div style={{ textAlign: 'center', marginBottom: 16 }}>
          <p>正在同步用户信息，请稍候...</p>
        </div>

        {syncLogs.length > 0 && (
        <div>
          <Divider>同步日志</Divider>
          <div style={{ maxHeight: 200, overflowY: 'auto' }}>
            {syncLogs.map((log) => (
              <div
                key={`${log.type}-${log.message}-${log.timestamp || ''}`}
                style={{ marginBottom: 4, fontSize: '12px' }}
              >
                <Tag color={log.type === 'success' ? 'green' : 'orange'} size="small">
                  {log.type === 'success' ? '成功' : '处理'}
                </Tag>
                {log.message}
              </div>
            ))}
          </div>
        </div>
        )}
      </div>
      )}

      {syncResults && (
      <div>
        <Alert
          message="同步完成"
          description={`成功同步 ${syncResults.total || 0} 个用户`}
          type="success"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <div style={{ marginBottom: 16 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <span>新增用户: <Tag color="green">{syncResults.created || 0}</Tag></span>
            <span>更新用户: <Tag color="blue">{syncResults.updated || 0}</Tag></span>
            <span>跳过用户: <Tag color="orange">{syncResults.skipped || 0}</Tag></span>
            <span>失败用户: <Tag color="red">{syncResults.failed || 0}</Tag></span>
          </div>
        </div>

        {syncResults.users && syncResults.users.length > 0 && (
        <div>
          <Divider>同步结果详情</Divider>
          <List
            size="small"
            dataSource={syncResults.users.slice(0, 10)}
            renderItem={(user) => (
              <List.Item>
                <List.Item.Meta
                  avatar={<Avatar size="small" src={user.avatar} icon="user" />}
                  title={
                    <div>
                      {user.name}
                      {(() => {
                        let tagColor = 'orange';
                        if (user.action === 'created') {
                          tagColor = 'green';
                        } else if (user.action === 'updated') {
                          tagColor = 'blue';
                        }
                        let tagText = '跳过';
                        if (user.action === 'created') {
                          tagText = '新增';
                        } else if (user.action === 'updated') {
                          tagText = '更新';
                        }
                        return (
                          <Tag
                            color={tagColor}
                            size="small"
                            style={{ marginLeft: 8 }}
                          >
                            {tagText}
                          </Tag>
                        );
                      })()}
                    </div>
                  }
                  description={user.mobile}
                />
              </List.Item>
            )}
          />
          {syncResults.users.length > 10 && (
          <div style={{ textAlign: 'center', marginTop: 8, color: '#666' }}>
            还有 {syncResults.users.length - 10} 个用户未显示...
          </div>
          )}
        </div>
        )}
      </div>
      )}
    </Modal>
  );
};

export default Form.create()(SyncUsersModal);
