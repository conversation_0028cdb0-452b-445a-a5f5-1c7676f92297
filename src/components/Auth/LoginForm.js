import React, { Component } from 'react';
import {
  Form,
  Input,
  Button,
  Card,
  message,
  Icon,
  Checkbox,
  Row,
  Col,
} from 'antd';
import authService from '../../services/auth';

// 登录表单校验规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名' },
    { min: 3, max: 50, message: '用户名长度应在3-50个字符之间' },
  ],
  password: [
    { required: true, message: '请输入密码' },
    { min: 6, message: '密码至少需要6个字符' },
  ],
};

class LoginForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
    };
  }

  handleSubmit = (e) => {
    e.preventDefault();
    this.props.form.validateFields(async (err, values) => {
      if (err) {
        message.error('请检查表单填写是否正确');
        return;
      }

      this.setState({ loading: true });

      try {
        const response = await authService.login({
          username: values.username,
          password: values.password,
        });

        if (response.success) {
          message.success('登录成功');

          // 调用父组件的成功回调
          if (this.props.onLoginSuccess) {
            this.props.onLoginSuccess(response.data);
          }
        } else {
          message.error(response.message || '登录失败');
        }
      } catch (error) {
        console.error('Login failed:', error);
        message.error(error.message || '登录失败，请检查用户名和密码');
      } finally {
        this.setState({ loading: false });
      }
    });
  };

  render() {
    const { loading } = this.state;
    const { form } = this.props;
    const { getFieldDecorator } = form;

    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px',
      }}
      >
        <Card
          title={
            <div style={{ textAlign: 'center' }}>
              <Icon type="project" style={{ fontSize: '32px', color: '#1890ff', marginRight: '8px' }} />
              <span style={{ fontSize: '24px', fontWeight: 'bold' }}>项目管理系统</span>
            </div>
          }
          style={{
            width: '100%',
            maxWidth: '400px',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
            borderRadius: '12px',
          }}
          bodyStyle={{ padding: '32px' }}
        >
          <Form onSubmit={this.handleSubmit} layout="vertical">
            <Form.Item label="用户名">
              {getFieldDecorator('username', {
                rules: loginRules.username,
              })(
                <Input
                  prefix={<Icon type="user" style={{ color: 'rgba(0,0,0,.25)' }} />}
                  placeholder="请输入用户名"
                  size="large"
                />,
              )}
            </Form.Item>

            <Form.Item label="密码">
              {getFieldDecorator('password', {
                rules: loginRules.password,
              })(
                <Input.Password
                  prefix={<Icon type="lock" style={{ color: 'rgba(0,0,0,.25)' }} />}
                  placeholder="请输入密码"
                  size="large"
                />,
              )}
            </Form.Item>

            <Form.Item>
              <Row>
                <Col span={12}>
                  {getFieldDecorator('remember', {
                    valuePropName: 'checked',
                    initialValue: true,
                  })(
                    <Checkbox>记住我</Checkbox>,
                  )}
                </Col>
                <Col span={12} style={{ textAlign: 'right' }}>
                  <a href="#" style={{ color: '#1890ff' }}>
                    忘记密码？
                  </a>
                </Col>
              </Row>
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                size="large"
                style={{
                  width: '100%',
                  height: '48px',
                  fontSize: '16px',
                  fontWeight: 'bold',
                }}
              >
                登录
              </Button>
            </Form.Item>

            <div style={{ textAlign: 'center', marginTop: '16px' }}>
              <span style={{ color: '#666' }}>
                还没有账号？
                <a href="#" style={{ color: '#1890ff', marginLeft: '4px' }}>
                  立即注册
                </a>
              </span>
            </div>
          </Form>

          {/* 演示账号信息 */}
          <div style={{
            marginTop: '24px',
            padding: '16px',
            background: '#f5f5f5',
            borderRadius: '8px',
            fontSize: '12px',
            color: '#666',
          }}
          >
            <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>演示账号：</div>
            <div>管理员：admin / 123456</div>
            <div>项目经理：pm001 / 123456</div>
            <div>品牌经理：bm001 / 123456</div>
          </div>
        </Card>
      </div>
    );
  }
}

export default Form.create()(LoginForm);
