import React from 'react';
import { Button, Tooltip } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import { usePermissionRefresh } from '../../hooks/usePermissionRefresh';

/**
 * 权限刷新按钮组件
 * 提供手动刷新用户权限的功能
 */
const PermissionRefreshButton = ({
  type = 'text',
  size = 'small',
  showText = false,
  tooltip = '刷新权限',
  ...props
}) => {
  const { refreshing, refreshPermissions } = usePermissionRefresh();

  const handleRefresh = async () => {
    await refreshPermissions();
  };

  return (
    <Tooltip title={tooltip}>
      <Button
        type={type}
        size={size}
        icon={<ReloadOutlined />}
        loading={refreshing}
        onClick={handleRefresh}
        {...props}
      >
        {showText && '刷新权限'}
      </Button>
    </Tooltip>
  );
};

export default PermissionRefreshButton;
