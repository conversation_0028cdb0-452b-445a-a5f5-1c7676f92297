import React, { Component } from 'react';
import {
  Card,
  Spin,
  Button,
  Alert,
  Icon,
  message,
} from 'antd';
import authService from '../../services/auth';

class DingTalkAuth extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: true,
      error: null,
      retryCount: 0,
      authStatus: '正在检查认证状态...',
    };
  }

  componentDidMount() {
    // 组件挂载后立即尝试钉钉免登
    this.attemptDingTalkLogin();
  }

  // 显示认证成功
  showAuthSuccess = () => {
    this.setState({
      loading: false,
      error: null,
      authStatus: '✅ 钉钉免登认证成功！',
    });
  };

  // 显示认证错误
  showAuthError = (msg) => {
    this.setState({
      loading: false,
      error: msg,
      authStatus: `❌ ${msg}`,
    });
  };

  attemptDingTalkLogin = async () => {
    this.setState({
      loading: true,
      error: null,
      authStatus: '正在进行钉钉免登认证...',
    });

    try {
      const result = await authService.dingtalkLogin();

      if (result.success) {
        this.showAuthSuccess();
        message.success('钉钉免登成功');

        // 调用父组件的成功回调
        if (this.props.onLoginSuccess) {
          this.props.onLoginSuccess(result.data);
        }
      } else {
        throw new Error(result.message || '钉钉免登失败');
      }
    } catch (error) {
      console.error('DingTalk auth failed:', error);
      this.showAuthError(error.message || '钉钉免登失败');
    }
  };

  handleRetry = () => {
    const { retryCount } = this.state;
    if (retryCount < 3) {
      this.setState({ retryCount: retryCount + 1 });
      this.attemptDingTalkLogin();
    } else {
      message.error('重试次数过多，请联系管理员');
    }
  };

  handleFallbackLogin = () => {
    // 如果钉钉免登失败，可以提供备用登录方式
    if (this.props.onFallbackLogin) {
      this.props.onFallbackLogin();
    }
  };

  render() {
    const { loading, error, retryCount, authStatus } = this.state;

    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px',
      }}
      >
        <Card
          style={{
            width: '100%',
            maxWidth: '400px',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
            borderRadius: '12px',
            textAlign: 'center',
          }}
          bodyStyle={{ padding: '40px' }}
        >
          {/* 钉钉Logo和标题 */}
          <div style={{ marginBottom: '32px' }}>
            <Icon
              type="dingding"
              style={{
                fontSize: '48px',
                color: '#0089ff',
                marginBottom: '16px',
              }}
            />
            <h2 style={{
              margin: 0,
              fontSize: '24px',
              fontWeight: 'bold',
              color: '#333',
            }}
            >
              项目财务管理系统
            </h2>
            <p style={{
              margin: '8px 0 0 0',
              color: '#666',
              fontSize: '14px',
            }}
            >
              {authStatus}
            </p>
          </div>

          {/* 加载状态 */}
          {loading && (
            <div style={{ marginBottom: '24px' }}>
              <Spin size="large" />
              <p style={{ marginTop: '16px', color: '#666' }}>
                正在进行钉钉免登验证，请稍候...
              </p>
            </div>
          )}

          {/* 错误状态 */}
          {error && !loading && (
            <div style={{ marginBottom: '24px' }}>
              <Alert
                message="免登失败"
                description={error}
                type="error"
                showIcon
                style={{ marginBottom: '16px', textAlign: 'left' }}
              />

              <div style={{ display: 'flex', gap: '12px', justifyContent: 'center' }}>
                {retryCount < 3 && (
                  <Button
                    type="primary"
                    onClick={this.handleRetry}
                    icon="reload"
                  >
                    重试 ({3 - retryCount} 次)
                  </Button>
                )}

                <Button
                  onClick={this.handleFallbackLogin}
                  icon="login"
                >
                  其他登录方式
                </Button>
              </div>
            </div>
          )}

          {/* 帮助信息 */}
          <div style={{
            marginTop: '32px',
            padding: '16px',
            background: '#f5f5f5',
            borderRadius: '8px',
            fontSize: '12px',
            color: '#666',
            textAlign: 'left',
          }}
          >
            <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>
              <Icon type="info-circle" style={{ marginRight: '4px' }} />
              使用说明：
            </div>
            <div>• 请确保您已在钉钉中打开此应用</div>
            <div>• 首次使用需要授权应用访问您的基本信息</div>
            <div>• 如遇问题请联系系统管理员</div>
          </div>

          {/* 版权信息 */}
          <div style={{
            marginTop: '24px',
            fontSize: '12px',
            color: '#999',
          }}
          >
            @CanTV 项目财务管理系统 ©2024
          </div>
        </Card>
      </div>
    );
  }
}

export default DingTalkAuth;
