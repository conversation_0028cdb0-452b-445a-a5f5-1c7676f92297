import React, { Component } from 'react';
import {
  Dropdown,
  Menu,
  Avatar,
  Icon,
  message,
  Modal,
} from 'antd';
import authService from '../../services/auth';

class UserInfo extends Component {
  constructor(props) {
    super(props);
    this.state = {
      userInfo: authService.getUserInfo(),
    };
  }

  componentDidMount() {
    // 监听用户信息变化
    this.checkUserInfo();
  }

  checkUserInfo = () => {
    const userInfo = authService.getUserInfo();
    this.setState({ userInfo });
  };

  handleLogout = () => {
    Modal.confirm({
      title: '确认退出',
      content: '您确定要退出登录吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await authService.logout();
          message.success('退出登录成功');

          // 调用父组件的退出回调
          if (this.props.onLogout) {
            this.props.onLogout();
          }
        } catch (error) {
          console.error('Logout failed:', error);
          message.error('退出登录失败');
        }
      },
    });
  };

  handleMenuClick = ({ key }) => {
    switch (key) {
      case 'profile':
        message.info('个人资料功能开发中...');
        break;
      case 'settings':
        message.info('设置功能开发中...');
        break;
      case 'logout':
        this.handleLogout();
        break;
      default:
        break;
    }
  };

  render() {
    const { userInfo } = this.state;

    if (!userInfo) {
      return null;
    }

    const menu = (
      <Menu onClick={this.handleMenuClick}>
        <Menu.Item key="profile">
          <Icon type="user" />
          个人资料
        </Menu.Item>
        <Menu.Item key="settings">
          <Icon type="setting" />
          设置
        </Menu.Item>
        <Menu.Divider />
        <Menu.Item key="logout">
          <Icon type="logout" />
          退出登录
        </Menu.Item>
      </Menu>
    );

    return (
      <Dropdown overlay={menu} placement="bottomRight">
        <div style={{
          display: 'flex',
          alignItems: 'center',
          cursor: 'pointer',
          padding: '0 12px',
          height: '100%',
        }}
        >
          <Avatar
            size="small"
            icon="user"
            src={userInfo.avatar}
            style={{ marginRight: '8px' }}
          />
          <span style={{ color: '#fff', marginRight: '4px' }}>
            {userInfo.name || userInfo.username}
          </span>
          <Icon type="down" style={{ color: '#fff', fontSize: '12px' }} />
        </div>
      </Dropdown>
    );
  }
}

export default UserInfo;
