# 项目管理组件

这是一个完整的项目管理系统组件集合，包含项目表单、项目列表表格和品牌管理功能。已集成真实的后端API接口。

## 组件结构

```
ProjectManagement/
├── ProjectManagement.js    # 主页面组件（整合所有功能）
├── ProjectForm.js          # 项目表单组件（带专业表单校验）
├── ProjectTable.js         # 项目列表表格组件
├── BrandManagement.js      # 品牌管理组件
├── BrandFormModal.js       # 品牌表单模态框组件（带专业表单校验）
├── ProjectDemo.js          # 演示页面组件
└── README.md              # 说明文档
```

## 支持文件

```
services/
└── api.js                  # API服务层，处理所有HTTP请求

utils/
└── projectUtils.js         # 项目相关工具函数

mock/
└── projectData.js          # 模拟数据（用户数据等）
```

## 功能特性

### 项目表单 (ProjectForm.js)
- ✅ 单据类型选择（项目立项表）
- ✅ 品牌选择（从品牌库获取）
- ✅ 项目执行周期时间段选择
- ✅ 项目名称输入
- ✅ 预算信息：项目规划预算、达人预算、投流预算、其他预算
- ✅ 成本信息：预估达人返点、达人成本、投流成本、其他成本
- ✅ 自动计算：项目利润 = 项目规划预算 - 成本 + 返点
- ✅ 自动计算：项目毛利 = 项目利润 / 规划预算
- ✅ 执行PM选择
- ✅ 内容媒介多选
- ✅ 合同类型选择（年框、季框、单次、PO单、京任务）
- ✅ 项目结算规则富文本输入
- ✅ KPI富文本输入
- ✅ 附件上传功能
- ✅ 表单验证

### 项目表格 (ProjectTable.js)
- ✅ 完整的项目列表展示
- ✅ 搜索筛选功能（项目名称、品牌、合同类型、状态）
- ✅ 新建、编辑、删除操作
- ✅ 批量删除功能
- ✅ 分页和排序
- ✅ 响应式设计
- ✅ 利润和毛利率颜色标识

### 品牌管理 (BrandManagement.js)
- ✅ 品牌信息的增删改查
- ✅ 品牌状态管理（启用/禁用）
- ✅ 品牌编码唯一性验证
- ✅ 批量操作支持

## 使用方法

### 1. 独立使用项目表单
```jsx
import ProjectForm from './components/ProjectManagement/ProjectForm';

<ProjectForm 
  onSubmit={(data) => {
    console.log('表单数据:', data);
  }}
  initialData={existingProjectData} // 可选，用于编辑模式
  hideActions={false} // 可选，是否隐藏操作按钮
/>
```

### 2. 独立使用项目表格
```jsx
import ProjectTable from './components/ProjectManagement/ProjectTable';

<ProjectTable />
```

### 3. 独立使用品牌管理
```jsx
import BrandManagement from './components/ProjectManagement/BrandManagement';

<BrandManagement />
```

### 4. 使用完整的项目管理系统
```jsx
import ProjectManagement from './components/ProjectManagement/ProjectManagement';

<ProjectManagement />
```

### 5. 查看演示页面
```jsx
import ProjectDemo from './components/ProjectManagement/ProjectDemo';

<ProjectDemo />
```

## 数据结构

### 项目数据结构
```javascript
{
  id: 1,
  documentType: 'project_initiation',
  brand: 1,
  executionPeriod: ['2024-01-01', '2024-03-31'],
  projectName: '春季营销推广项目',
  planningBudget: 1000000,
  talentBudget: 300000,
  adBudget: 400000,
  otherBudget: 50000,
  estimatedTalentRebate: 30000,
  talentCost: 280000,
  adCost: 380000,
  otherCost: 45000,
  projectProfit: 325000, // 自动计算
  grossMargin: 32.5, // 自动计算
  executivePM: 1,
  contentMedia: [3, 6],
  contractType: 'quarterly',
  settlementRules: '<p>按季度结算...</p>',
  kpi: '<p>目标曝光量：1000万...</p>',
  attachments: [],
  status: 'executing',
  createTime: '2024-01-01 10:00:00',
  updateTime: '2024-01-15 14:30:00'
}
```

### 品牌数据结构
```javascript
{
  id: 1,
  name: '品牌A',
  code: 'BRAND_A',
  status: 'active',
  createTime: '2024-01-01'
}
```

## 技术要求

- React 16.12.0+
- Ant Design 3.26.8
- moment.js（用于时间处理）

## 兼容性说明

本组件专门为Ant Design 3.x版本设计，已处理以下兼容性问题：
- 移除了Space组件（3.x版本不支持）
- 替换了Button的danger属性
- 使用了3.x版本支持的API

## 路由配置

在App.js中已添加以下路由：
- `/project` - 完整项目管理系统
- `/demo` - 组件演示页面

## API集成

组件已完全集成后端API，支持以下功能：

### 品牌管理API
- `GET /api/brands` - 获取品牌列表
- `POST /api/brands` - 创建品牌
- `PUT /api/brands` - 更新品牌
- `DELETE /api/brands/{id}` - 删除品牌

### 项目管理API
- `GET /api/projects` - 获取项目列表
- `POST /api/projects` - 创建项目
- `PUT /api/projects/{id}` - 更新项目
- `DELETE /api/projects/{id}` - 删除项目
- `GET /api/projects/stats` - 获取项目统计

### 文件上传API
- `POST /api/upload` - 上传文件

### API配置
API基础URL在 `src/config.js` 中配置，默认为 `${host}/api`

## 数据转换

组件使用 `dataTransform` 工具进行前端和API数据格式的转换：
- `projectToAPI()` - 将前端项目数据转换为API格式
- `projectFromAPI()` - 将API项目数据转换为前端格式
- `brandToAPI()` - 将前端品牌数据转换为API格式
- `brandFromAPI()` - 将API品牌数据转换为前端格式

## 自定义配置

可以通过修改以下文件来自定义配置：
- `services/api.js` - 修改API请求逻辑
- `utils/projectUtils.js` - 修改选项数据和计算逻辑
- `mock/projectData.js` - 修改模拟数据（用户数据等）
