import React, { Component } from 'react';
import {
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Button,
  Row,
  Col,
  Card,
  Upload,
  Icon,
  message,
} from 'antd';
import moment from 'moment';
import {
  DOCUMENT_TYPES,
  CONTRACT_TYPES,
  CONTRACT_SIGNING_STATUS,
  calculateProjectProfit,
  calculateGrossMargin,
  formatCurrency,
  formatPercentage,
} from '../../utils/projectUtils';
import { brandAPI, projectAPI, dataTransform, uploadAPI, departmentAPI } from '../../services/api';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

// 表单校验规则
const formRules = {
  documentType: [{ required: true, message: '请选择单据类型' }],
  brand: [{ required: true, message: '请选择品牌' }],
  executionPeriod: [{ required: true, message: '请选择项目执行周期', type: 'array', min: 2 }],
  projectName: [
    { required: true, message: '请输入项目名称' },
    { min: 2, max: 100, message: '项目名称长度应在2-100个字符之间' },
  ],
  planningBudget: [
    { required: true, message: '请输入项目规划预算' },
    { type: 'number', min: 0.01, message: '项目规划预算必须大于0' },
  ],
  executivePM: [{ required: true, message: '请选择执行PM' }],
  contentMedia: [
    { required: true, message: '请至少选择一个内容媒介' },
    { type: 'array', min: 1, message: '请至少选择一个内容媒介' },
  ],
  contractType: [{ required: true, message: '请选择合同类型' }],
  contractSigningStatus: [{ required: true, message: '请选择合同签署状态' }],
  settlementRules: [
    { required: true, message: '请输入项目结算规则' },
    { min: 10, message: '项目结算规则至少需要10个字符' },
  ],
  kpi: [
    { required: true, message: '请输入KPI指标' },
    { min: 10, message: 'KPI指标至少需要10个字符' },
  ],
  expectedPaymentMonth: [
    { required: true, message: '请选择预计回款月份' },
  ],
  paymentTermDays: [
    { required: true, message: '请输入账期天数' },
    { type: 'number', min: 0, max: 9999, message: '账期天数应在0-9999之间' },
  ],
};

class ProjectForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      brandOptions: [],
      pmOptions: [], // 执行PM选项
      mediaOptions: [], // 内容媒介选项
      calculatedProfit: 0,
      calculatedMargin: 0,
      // 用于实时显示人民币大写的字段值
      fieldValues: {
        talentBudget: 0,
        adBudget: 0,
        otherBudget: 0,
        talentCost: 0,
        adCost: 0,
        otherCost: 0,
        talentRebateIncome: 0,
        paymentTermDays: 0,
      },
    };
  }

  componentDidMount() {
    this.loadOptions();
    // 如果有初始数据，设置表单值
    if (this.props.initialData) {
      setTimeout(() => {
        const formData = this.transformInitialData(this.props.initialData);
        console.log('[ formData ] >', formData);
        this.props.form.setFieldsValue(formData);

        // 同步更新 fieldValues 用于人民币大写显示
        this.setState({
          fieldValues: {
            talentBudget: formData.talentBudget || 0,
            adBudget: formData.adBudget || 0,
            otherBudget: formData.otherBudget || 0,
            talentCost: formData.talentCost || 0,
            adCost: formData.adCost || 0,
            otherCost: formData.otherCost || 0,
            talentRebateIncome: formData.talentRebateIncome || 0,
            paymentTermDays: formData.paymentTermDays || 0,
          },
        });

        // 如果没有总预算但有分项预算，自动计算总预算
        const hasSubBudgets = formData.talentBudget || formData.adBudget || formData.otherBudget;
        if (!formData.planningBudget && hasSubBudgets) {
          this.handleSubBudgetChange('talentBudget')(formData.talentBudget);
        } else {
          this.calculateProfitAndMargin();
        }
      }, 100);
    }
  }

  // 转换初始数据，确保表单字段是正确的类型
  transformInitialData = (initialData) => {
    try {
      const transformedData = { ...initialData };
      console.log('[ transformedData ] >', transformedData);
      // documentType
      if (transformedData.documentType) {
        transformedData.documentType = transformedData.documentType.toLowerCase();
      }

      // contractType
      if (transformedData.contractType) {
        transformedData.contractType = transformedData.contractType.toUpperCase();
      }

      // 处理品牌字段 - 如果是对象，提取ID
      if (transformedData.brand && typeof transformedData.brand === 'object') {
        transformedData.brandId = transformedData.brand.id || transformedData.brand.value;
        // delete transformedData.brand;
      }

      if (transformedData.contractSigningStatus) {
        transformedData.contractSigningStatus = transformedData.contractSigningStatus.toLowerCase();
      }

      // 处理执行PM字段 - 如果是对象，提取userid
      if (transformedData.executivePM && typeof transformedData.executivePM === 'object') {
        transformedData.executivePM = transformedData.executivePM.userid ||
                                      transformedData.executivePM.value ||
                                      transformedData.executivePM.id;
      }

      // 处理内容媒介字段 - 如果是对象数组，提取userid数组
      if (transformedData.contentMedia) {
        if (Array.isArray(transformedData.contentMedia)) {
          transformedData.contentMedia = transformedData.contentMedia.map((media) => {
            if (typeof media === 'object') {
              return media.userid || media.value || media.id;
            }
            return media;
          });
        } else if (typeof transformedData.contentMedia === 'string') {
          // 如果是逗号分隔的字符串，转换为数组
          transformedData.contentMedia = transformedData.contentMedia.split(',').map((s) => s.trim());
        }
      }

      // 处理执行周期 - 确保是moment对象数组
      if (transformedData.executionPeriod && Array.isArray(transformedData.executionPeriod)) {
        transformedData.executionPeriod = transformedData.executionPeriod.map((date) => {
          if (typeof date === 'string') {
            return moment(date);
          }
          return date;
        });
      }

      // 处理预计回款月份 - 确保是moment对象
      if (transformedData.expectedPaymentMonth && typeof transformedData.expectedPaymentMonth === 'string') {
        transformedData.expectedPaymentMonth = moment(transformedData.expectedPaymentMonth, 'YYYY-MM');
      }

      // 处理字段名映射：将API字段名映射到表单字段名
      if (transformedData.estimatedTalentRebate !== undefined) {
        transformedData.talentRebateIncome = transformedData.estimatedTalentRebate;
        delete transformedData.estimatedTalentRebate;
      }

      // 移除空字符串字段，避免覆盖placeholder
      const finalData = {};
      Object.keys(transformedData).forEach((key) => {
        const value = transformedData[key];
        // 只有当值不为空字符串时才设置
        if (value !== '') {
          finalData[key] = value;
        }
      });

      return finalData;
    } catch (error) {
      console.warn('Transform initial data error:', error);
      return initialData;
    }
  };

  loadOptions = async () => {
    try {
      // 加载品牌选项
      const brandResponse = await brandAPI.getBrands({ status: 'active' });
      let brandOptions = [];
      if (brandResponse.success) {
        brandOptions = brandResponse.data.brands.map((brand) => ({
          value: brand.id,
          label: brand.name,
        }));
      }

      // 加载执行PM选项（从项目管理部门获取）
      let pmOptions = [];
      const pmResponse = await departmentAPI.getDepartmentUsers({
        deptId: '',
        size: 100,
      });
      if (pmResponse.success) {
        pmOptions = pmResponse.data.list.map((user) => ({
          value: user.userid,
          label: user.name,
          avatar: user.avatar,
          mobile: user.mobile,
          email: user.email,
        }));
      }

      // 加载内容媒介选项（从内容部门获取）
      let mediaOptions = [];
      const mediaResponse = await departmentAPI.getDepartmentUsers({
        deptId: '',
        size: 100,
      });
      if (mediaResponse.success) {
        mediaOptions = mediaResponse.data.list.map((user) => ({
          value: user.userid,
          label: user.name,
          avatar: user.avatar,
          mobile: user.mobile,
          email: user.email,
        }));
      }

      this.setState({
        brandOptions,
        pmOptions,
        mediaOptions,
      });
    } catch (error) {
      console.error('Load options failed:', error);
      message.error('加载选项数据失败');
      this.setState({
        brandOptions: [],
        pmOptions: [],
        mediaOptions: [],
      });
    }
  };

  // 更新字段值到state，用于实时显示人民币大写
  updateFieldValue = (fieldName, value) => {
    this.setState((prevState) => ({
      fieldValues: {
        ...prevState.fieldValues,
        [fieldName]: value || 0,
      },
    }));
  };

  handleBudgetChange = (fieldName) => (value) => {
    // 更新字段值到state
    this.updateFieldValue(fieldName, value);

    // 预算或成本字段变化时，重新计算利润和毛利
    setTimeout(() => {
      this.calculateProfitAndMargin();
    }, 100);
  };

  // 处理分项预算变化，自动计算总预算
  handleSubBudgetChange = (fieldName) => (value) => {
    // 更新字段值到state
    this.updateFieldValue(fieldName, value);

    setTimeout(() => {
      const formValues = this.props.form.getFieldsValue();
      const {
        talentBudget = 0,
        adBudget = 0,
        otherBudget = 0,
      } = formValues;

      const totalBudget = (talentBudget || 0) + (adBudget || 0) + (otherBudget || 0);

      // 自动更新项目总预算
      this.props.form.setFieldsValue({
        planningBudget: totalBudget,
      });

      // 重新计算利润和毛利
      this.calculateProfitAndMargin();
    }, 100);
  };

  calculateProfitAndMargin = () => {
    const formValues = this.props.form.getFieldsValue();
    const {
      planningBudget = 0,
      talentCost = 0,
      adCost = 0,
      otherCost = 0,
      talentRebateIncome = 0, // 重命名为收入
    } = formValues;

    const profit = calculateProjectProfit(
      planningBudget,
      talentCost,
      adCost,
      otherCost,
      talentRebateIncome, // 使用新的字段名
    );
    const margin = calculateGrossMargin(profit, planningBudget);

    this.setState({
      calculatedProfit: profit,
      calculatedMargin: parseFloat(margin),
    });
  };

  // 验证预算分配是否合理
  validateBudgetAllocation = () => {
    const formValues = this.props.form.getFieldsValue();
    const {
      planningBudget = 0,
      talentBudget = 0,
      adBudget = 0,
      otherBudget = 0,
    } = formValues;

    const totalBudget = (talentBudget || 0) + (adBudget || 0) + (otherBudget || 0);
    const difference = Math.abs(planningBudget - totalBudget);

    // 允许小数点误差
    if (difference > 0.01 && planningBudget > 0) {
      return {
        isValid: false,
        message: `预算分配不匹配：总预算 ¥${planningBudget.toLocaleString()}，分项预算合计 ¥${totalBudget.toLocaleString()}`,
      };
    }

    return { isValid: true };
  };

  handleSubmit = (e) => {
    e.preventDefault();
    this.props.form.validateFields(async (err, values) => {
      if (err) {
        message.error('请检查表单填写是否正确');
        return;
      }

      this.setState({ loading: true });

      try {
        // 添加计算的利润和毛利到表单数据，并处理字段名映射
        const formData = {
          ...values,
          // 将新字段名映射回API期望的字段名
          estimatedTalentRebate: values.talentRebateIncome || 0,
          projectProfit: this.state.calculatedProfit,
          grossMargin: this.state.calculatedMargin,
        };

        const projectData = dataTransform.projectToAPI(formData);
        let response;

        if (this.props.initialData && this.props.initialData.id) {
          // 编辑模式
          response = await projectAPI.updateProject({
            ...projectData,
            id: this.props.initialData.id,
          });
        } else {
          // 新增模式
          console.log(JSON.stringify(projectData));
          response = await projectAPI.createProject(projectData);
        }

        if (response.success) {
          message.success('项目保存成功');
          if (this.props.onSubmit) {
            this.props.onSubmit(response.data);
          }
        } else {
          message.error(response.message || '保存失败');
        }
      } catch (error) {
        console.error('Submit project failed:', error);
        message.error('保存失败');
      } finally {
        this.setState({ loading: false });
      }
    });
  };

  handleReset = () => {
    this.props.form.resetFields();
    this.setState({
      calculatedProfit: 0,
      calculatedMargin: 0,
    });
  };

  // 人民币转大写
  convertCurrencyToChinese = (amount) => {
    if (!amount || amount === 0) {
      return '零元整';
    }

    const num = Math.floor(Math.abs(amount));
    const digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    const units = ['', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿'];

    if (num === 0) {
      return '零元整';
    }

    const numStr = num.toString();
    let result = '';
    let zeroFlag = false; // 标记是否需要添加零

    for (let i = 0; i < numStr.length; i++) {
      const digit = parseInt(numStr[i], 10);
      const unitIndex = numStr.length - i - 1;

      if (digit === 0) {
        // 当前位是0
        if (unitIndex === 4 || unitIndex === 8) {
          // 万位或亿位，即使是0也要加单位（如果前面有数字）
          if (result && !result.endsWith('万') && !result.endsWith('亿')) {
            result += units[unitIndex];
          }
        }
        zeroFlag = true;
      } else {
        // 当前位不是0
        if (zeroFlag && result) {
          result += '零';
        }
        result += digits[digit];
        if (unitIndex > 0) {
          result += units[unitIndex];
        }
        zeroFlag = false;
      }
    }

    // 清理结果
    result = result
      .replace(/零+/g, '零') // 多个零合并为一个
      .replace(/零万/g, '万') // 零万 -> 万
      .replace(/零亿/g, '亿') // 零亿 -> 亿
      .replace(/零元/g, '元') // 零元 -> 元
      .replace(/零$/, ''); // 去掉末尾的零

    return `${result }元整`;
  };

  render() {
    const {
      loading,
      brandOptions,
      pmOptions,
      mediaOptions,
      calculatedProfit,
      calculatedMargin,
    } = this.state;
    const { hideActions = false, form } = this.props;
    const { getFieldDecorator } = form;

    const uploadProps = {
      name: 'file',
      multiple: true,
      customRequest: async (options) => {
        const { file, onSuccess, onError } = options;
        try {
          console.log('[ file ] >', file);
          const response = await uploadAPI.uploadProjectFile(file);
          console.log('[ response ] >', response);
          if (response.success) {
            onSuccess(response.data);
            message.success(`${file.name} 文件上传成功`);
            // 更新表单中的附件字段
            const currentAttachments = form.getFieldValue('attachments') || [];
            console.log('[ currentAttachments ] >', currentAttachments);
            form.setFieldsValue({
              attachments: currentAttachments.fileList,
            });
            console.log('[ form.getFieldValue(\'attachments\') ] >', form.getFieldValue('attachments'));
          } else {
            onError(new Error(response.message || '上传失败'));
          }
        } catch (error) {
          console.log('[ error ] >', error);
          onError(error);
          message.error(`${file.name} 文件上传失败`);
        }
      },
    };

    return (
      <div style={{ padding: '16px 0' }}>
        <Form layout="vertical" onSubmit={this.handleSubmit}>
          {/* 基本信息 */}
          <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="项目名称">
                  {getFieldDecorator('projectName', {
                    rules: formRules.projectName,
                  })(
                    <Input placeholder="请输入项目名称" />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="项目执行周期">
                  {getFieldDecorator('executionPeriod', {
                    rules: formRules.executionPeriod,
                  })(
                    <RangePicker style={{ width: '100%' }} />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="单据类型">
                  {getFieldDecorator('documentType', {
                    rules: formRules.documentType,
                  })(
                    <Select placeholder="请选择单据类型">
                      {DOCUMENT_TYPES.map((type) => (
                        <Option key={type.value} value={type.value}>{type.label}</Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>

              <Col span={6}>
                <Form.Item label="品牌">
                  {getFieldDecorator('brand', {
                    rules: formRules.brand,
                  })(
                    <Select placeholder="请选择品牌">
                      {brandOptions.map((brand) => (
                        <Option key={brand.value} value={brand.value}>{brand.label}</Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="执行PM">
                  {getFieldDecorator('executivePM', {
                    rules: formRules.executivePM,
                  })(
                    <Select placeholder="请选择执行PM" showSearch optionFilterProp="children">
                      {pmOptions.map((user) => (
                        <Option
                          key={user.value}
                          value={user.value}
                          title={user.email || user.mobile}
                        >
                          {user.label}
                        </Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="内容媒介">
                  {getFieldDecorator('contentMedia', {
                    rules: formRules.contentMedia,
                  })(
                    <Select
                      mode="multiple"
                      placeholder="请选择内容媒介"
                      showSearch
                      optionFilterProp="children"
                    >
                      {mediaOptions.map((user) => (
                        <Option
                          key={user.value}
                          value={user.value}
                          title={user.email || user.mobile}
                        >
                          {user.label}
                        </Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="合同类型">
                  {getFieldDecorator('contractType', {
                    rules: formRules.contractType,
                  })(
                    <Select placeholder="请选择合同类型">
                      {CONTRACT_TYPES.map((type) => (
                        <Option key={type.value} value={type.value}>{type.label}</Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={6}>
                <Form.Item label="合同签署状态">
                  {getFieldDecorator('contractSigningStatus', {
                    rules: formRules.contractSigningStatus,
                  })(
                    <Select placeholder="请选择合同签署状态">
                      {CONTRACT_SIGNING_STATUS.map((status) => (
                        <Option key={status.value} value={status.value}>{status.label}</Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
            </Row>

          </Card>

          {/* 预算规划 */}
          <Card title="预算规划" size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={6}>
                <Form.Item label="项目总预算" extra="自动计算：达人预算 + 投流预算 + 其他预算">
                  {getFieldDecorator('planningBudget', {
                    rules: formRules.planningBudget,
                  })(
                    <InputNumber
                      placeholder="自动计算总预算"
                      style={{ width: '100%', backgroundColor: '#f5f5f5' }}
                      formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                      onChange={this.handleBudgetChange('planningBudget')}
                      disabled
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="达人预算" extra={this.convertCurrencyToChinese(this.state.fieldValues.talentBudget)}>
                  {getFieldDecorator('talentBudget', {
                    initialValue: 0,
                  })(
                    <InputNumber
                      placeholder="请输入达人预算"
                      style={{ width: '100%' }}
                      formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                      onChange={this.handleSubBudgetChange('talentBudget')}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="投流预算" extra={this.convertCurrencyToChinese(this.state.fieldValues.adBudget)}>
                  {getFieldDecorator('adBudget', {
                    initialValue: 0,
                  })(
                    <InputNumber
                      placeholder="请输入投流预算"
                      style={{ width: '100%' }}
                      formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                      onChange={this.handleSubBudgetChange('adBudget')}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="其他预算" extra={this.convertCurrencyToChinese(this.state.fieldValues.otherBudget)}>
                  {getFieldDecorator('otherBudget', {
                    initialValue: 0,
                  })(
                    <InputNumber
                      placeholder="请输入其他预算"
                      style={{ width: '100%' }}
                      formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                      onChange={this.handleSubBudgetChange('otherBudget')}
                    />,
                  )}
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 成本信息 */}
          <Card title="成本信息" size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item label="达人成本" extra={this.convertCurrencyToChinese(this.state.fieldValues.talentCost)}>
                  {getFieldDecorator('talentCost', {
                    initialValue: 0,
                  })(
                    <InputNumber
                      placeholder="请输入达人成本"
                      style={{ width: '100%' }}
                      formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                      onChange={this.handleBudgetChange('talentCost')}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="投流成本" extra={this.convertCurrencyToChinese(this.state.fieldValues.adCost)}>
                  {getFieldDecorator('adCost', {
                    initialValue: 0,
                  })(
                    <InputNumber
                      placeholder="请输入投流成本"
                      style={{ width: '100%' }}
                      formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                      onChange={this.handleBudgetChange('adCost')}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="其他成本" extra={this.convertCurrencyToChinese(this.state.fieldValues.otherCost)}>
                  {getFieldDecorator('otherCost', {
                    initialValue: 0,
                  })(
                    <InputNumber
                      placeholder="请输入其他成本"
                      style={{ width: '100%' }}
                      formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                      onChange={this.handleBudgetChange('otherCost')}
                    />,
                  )}
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 收入信息 */}
          <Card title="收入信息" size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item label="达人返点收入" extra={this.convertCurrencyToChinese(this.state.fieldValues.talentRebateIncome)}>
                  {getFieldDecorator('talentRebateIncome', {
                    initialValue: 0,
                  })(
                    <InputNumber
                      placeholder="请输入达人返点收入"
                      style={{ width: '100%' }}
                      formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                      onChange={this.handleBudgetChange('talentRebateIncome')}
                    />,
                  )}
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 利润分析 */}
          <Card title="利润分析" size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="项目利润" extra="项目总预算 - 总成本 + 达人返点收入">
                  <Input
                    value={formatCurrency(calculatedProfit)}
                    disabled
                    style={{
                      color: calculatedProfit >= 0 ? '#52c41a' : '#f5222d',
                      fontWeight: 'bold',
                      fontSize: '16px',
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="项目毛利率" extra="项目利润 / 项目总预算">
                  <Input
                    value={formatPercentage(calculatedMargin)}
                    disabled
                    style={{
                      color: calculatedMargin >= 0 ? '#52c41a' : '#f5222d',
                      fontWeight: 'bold',
                      fontSize: '16px',
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 其他信息 */}
          <Card title="其他信息" size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="项目结算规则">
                  {getFieldDecorator('settlementRules', {
                    rules: formRules.settlementRules,
                  })(
                    <TextArea
                      placeholder="请输入项目结算规则"
                      rows={3}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="KPI指标">
                  {getFieldDecorator('kpi', {
                    rules: formRules.kpi,
                  })(
                    <TextArea
                      placeholder="请输入KPI指标"
                      rows={3}
                    />,
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item label="预计回款月份">
                  {getFieldDecorator('expectedPaymentMonth', {
                    rules: formRules.expectedPaymentMonth,
                  })(
                    <DatePicker
                      picker="month"
                      placeholder="请选择预计回款月份"
                      style={{ width: '100%' }}
                      format="YYYY-MM"
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="账期天数">
                  {getFieldDecorator('paymentTermDays', {
                    rules: formRules.paymentTermDays,
                  })(
                    <InputNumber
                      placeholder="请输入账期天数"
                      style={{ width: '100%' }}
                      min={0}
                      max={9999}
                      formatter={(value) => (value ? `T+${value}` : '')}
                      parser={(value) => (value ? value.replace('T+', '') : '')}
                      addonAfter="天"
                      onChange={(value) => this.updateFieldValue('paymentTermDays', value)}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="附件">
                  {getFieldDecorator('attachments', {
                    initialValue: [],
                  })(
                    <Upload {...uploadProps}>
                      <Button>
                        <Icon type="upload" /> 点击上传文件
                      </Button>
                    </Upload>,
                  )}
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {!hideActions && (
            <Row gutter={16} style={{ marginTop: 16, textAlign: 'center' }}>
              <Col span={24}>
                <Button onClick={this.handleReset} style={{ marginRight: 16 }}>
                  重置
                </Button>
                <Button type="primary" loading={loading} htmlType="submit">
                  保存项目
                </Button>
              </Col>
            </Row>
          )}
        </Form>
      </div>
    );
  }
}

export default Form.create()(ProjectForm);
