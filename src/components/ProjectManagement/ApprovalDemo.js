import React, { Component } from 'react';
import { Card, Button, message } from 'antd';
import WeeklyBudgetTable from './WeeklyBudgetTable';

// 模拟项目数据
const mockProjectId = 'project_001';

class ApprovalDemo extends Component {
  constructor(props) {
    super(props);
    this.state = {
      projectId: mockProjectId,
    };
  }

  handleView = (record) => {
    message.info(`查看周预算: ${record.title}`);
  };

  handleEdit = (record) => {
    message.info(`编辑周预算: ${record.title}`);
  };

  render() {
    const { projectId } = this.state;

    return (
      <div style={{ padding: '24px' }}>
        <Card title="周预算审批功能演示" style={{ marginBottom: 16 }}>
          <p>此页面演示周预算审批功能，包括：</p>
          <ul>
            <li>发起对公付款审批</li>
            <li>查看审批状态和进度</li>
            <li>审批状态同步</li>
          </ul>
          <p><strong>使用说明：</strong></p>
          <ol>
            <li>在周预算列表中点击"发起审批"按钮</li>
            <li>填写审批表单信息（付款金额、收款账号等）</li>
            <li>提交后可点击"审批状态"查看审批进度</li>
            <li>支持上传发票和附件</li>
          </ol>
        </Card>

        <WeeklyBudgetTable
          projectId={projectId}
          onView={this.handleView}
          onEdit={this.handleEdit}
        />
      </div>
    );
  }
}

export default ApprovalDemo;
