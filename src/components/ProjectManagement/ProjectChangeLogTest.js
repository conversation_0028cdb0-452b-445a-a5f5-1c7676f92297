import React, { Component } from 'react';
import { Card, Button, message, Row, Col, Tag, Divider } from 'antd';
import ProjectChangeLog from './ProjectChangeLog';

/**
 * 项目变更记录功能测试组件
 */
class ProjectChangeLogTest extends Component {
  constructor(props) {
    super(props);
    this.state = {
      changeLogVisible: false,
      testProject: null,
    };
  }

  // 生成测试项目数据
  generateTestProject = () => {
    return {
      id: 'test-project-001',
      projectName: '测试项目 - 春季营销推广活动',
      brand: '品牌A',
      contractType: 'QUARTERLY_FRAME',
      contractSigningStatus: 'SIGNED',
      status: 'ACTIVE',
      executionPeriod: ['2024-03-01', '2024-05-31'],
      planningBudget: 1000000,
      projectProfit: 250000,
      grossMargin: 25.0,
      executivePM: 'user001',
      contentMedia: ['user003', 'user006'],
      createTime: '2024-01-15 10:30:00',
      updateTime: '2024-01-20 14:20:00',
    };
  };

  // 生成测试变更记录数据（用于演示字段映射）
  generateMockChangeLog = () => {
    return {
      id: 'change-001',
      changeType: 'UPDATE',
      changeTitle: '更新项目基本信息',
      changeDetails: {
        description: '修改了项目的合同签署状态和执行PM',
      },
      beforeData: {
        contractSigningStatus: 'PENDING',
        executorPM: 'user001',
        planningBudget: 800000,
        contractType: 'SINGLE',
        status: 'DRAFT',
      },
      afterData: {
        contractSigningStatus: 'SIGNED',
        executorPM: 'user002',
        planningBudget: 1000000,
        contractType: 'QUARTERLY_FRAME',
        status: 'ACTIVE',
      },
      changedFields: ['contractSigningStatus', 'executorPM', 'planningBudget', 'contractType', 'status'],
      operatorId: 'admin001',
      operatorName: '管理员',
      operatorIP: '*************',
      reason: '客户要求调整合同类型和预算',
      createdAt: '2024-01-20 14:30:00',
    };
  };

  handleOpenChangeLog = () => {
    const testProject = this.generateTestProject();
    this.setState({
      changeLogVisible: true,
      testProject,
    });
  };

  handleCloseChangeLog = () => {
    this.setState({
      changeLogVisible: false,
      testProject: null,
    });
  };

  render() {
    const { changeLogVisible, testProject } = this.state;

    return (
      <div style={{ padding: '20px' }}>
        <Card title="项目变更记录功能测试" style={{ marginBottom: '20px' }}>
          <Row gutter={16}>
            <Col span={12}>
              <h4>功能特点：</h4>
              <ul style={{ paddingLeft: '20px' }}>
                <li>✅ 时间轴展示变更历史</li>
                <li>✅ 变更类型分类和颜色标识</li>
                <li>✅ 详细的字段变更对比</li>
                <li>✅ 操作人员和时间信息</li>
                <li>✅ 变更原因和描述</li>
                <li>✅ 筛选和搜索功能</li>
                <li>✅ 分页加载更多</li>
              </ul>
            </Col>
            <Col span={12}>
              <h4>变更类型：</h4>
              <div style={{ marginBottom: 16 }}>
                <Tag color="#52c41a">创建</Tag>
                <Tag color="#1890ff">更新</Tag>
                <Tag color="#f5222d">删除</Tag>
                <Tag color="#722ed1">状态变更</Tag>
                <Tag color="#fa8c16">预算调整</Tag>
                <Tag color="#13c2c2">团队变更</Tag>
                <Tag color="#eb2f96">合同变更</Tag>
              </div>
            </Col>
          </Row>

          <Divider />

          <div style={{ textAlign: 'center', marginBottom: 16 }}>
            <Button
              type="primary"
              size="large"
              onClick={this.handleOpenChangeLog}
              icon="history"
            >
              查看项目变更记录
            </Button>
          </div>

          <Divider />

          <div style={{ fontSize: '14px', color: '#666' }}>
            <h4>测试项目信息：</h4>
            <Row gutter={16}>
              <Col span={12}>
                <p><strong>项目名称：</strong>测试项目 - 春季营销推广活动</p>
                <p><strong>项目ID：</strong>test-project-001</p>
                <p><strong>所属品牌：</strong>品牌A</p>
                <p><strong>项目状态：</strong>进行中</p>
              </Col>
              <Col span={12}>
                <p><strong>合同类型：</strong>季框</p>
                <p><strong>签署状态：</strong>已签署</p>
                <p><strong>规划预算：</strong>¥1,000,000</p>
                <p><strong>项目利润：</strong>¥250,000</p>
              </Col>
            </Row>

            <h4 style={{ marginTop: 16 }}>字段映射演示：</h4>
            <Row gutter={16}>
              <Col span={12}>
                <h5>枚举值转换：</h5>
                <ul style={{ fontSize: '12px', paddingLeft: '16px' }}>
                  <li><code>PENDING</code> → 待签署</li>
                  <li><code>SIGNED</code> → 已签署</li>
                  <li><code>NO_CONTRACT</code> → 无合同</li>
                  <li><code>QUARTERLY_FRAME</code> → 季框</li>
                  <li><code>SINGLE</code> → 单次</li>
                  <li><code>ACTIVE</code> → 进行中</li>
                  <li><code>DRAFT</code> → 草稿</li>
                </ul>
              </Col>
              <Col span={12}>
                <h5>用户ID映射：</h5>
                <ul style={{ fontSize: '12px', paddingLeft: '16px' }}>
                  <li><code>user001</code> → 张三</li>
                  <li><code>user002</code> → 李四</li>
                  <li><code>user003</code> → 王五</li>
                  <li><code>admin001</code> → 管理员</li>
                </ul>
                <h5>货币格式化：</h5>
                <ul style={{ fontSize: '12px', paddingLeft: '16px' }}>
                  <li><code>800000</code> → ¥800,000</li>
                  <li><code>1000000</code> → ¥1,000,000</li>
                </ul>
              </Col>
            </Row>
          </div>

          <div style={{
            marginTop: 16,
            padding: '12px',
            backgroundColor: '#f0f9ff',
            border: '1px solid #bae6fd',
            borderRadius: '4px',
          }}
          >
            <h4 style={{ margin: '0 0 8px 0', color: '#1890ff' }}>💡 功能说明：</h4>
            <ul style={{ margin: 0, paddingLeft: '20px', fontSize: '13px' }}>
              <li>点击按钮打开变更记录弹窗</li>
              <li>支持按变更类型筛选记录</li>
              <li>支持按日期范围筛选</li>
              <li>支持搜索操作人员</li>
              <li>显示详细的字段变更对比</li>
              <li>包含操作人员、时间、IP等信息</li>
            </ul>
          </div>

          <div style={{
            marginTop: 12,
            padding: '12px',
            backgroundColor: '#fff7e6',
            border: '1px solid #ffd591',
            borderRadius: '4px',
          }}
          >
            <h4 style={{ margin: '0 0 8px 0', color: '#fa8c16' }}>⚠️ 注意事项：</h4>
            <ul style={{ margin: 0, paddingLeft: '20px', fontSize: '13px' }}>
              <li>变更记录数据来自后端API接口</li>
              <li>需要确保项目ID存在且有变更记录</li>
              <li>如果没有数据会显示空状态</li>
              <li>支持实时刷新最新变更记录</li>
            </ul>
          </div>
        </Card>

        <Card title="界面设计特点">
          <Row gutter={16}>
            <Col span={8}>
              <h4>🎨 视觉设计</h4>
              <ul style={{ fontSize: '13px' }}>
                <li>时间轴布局，清晰展示历史</li>
                <li>颜色编码区分变更类型</li>
                <li>卡片式设计，信息层次分明</li>
                <li>响应式布局，适配不同屏幕</li>
              </ul>
            </Col>
            <Col span={8}>
              <h4>🔍 交互体验</h4>
              <ul style={{ fontSize: '13px' }}>
                <li>筛选条件实时生效</li>
                <li>分页加载，性能优化</li>
                <li>悬停提示，信息丰富</li>
                <li>一键刷新，数据同步</li>
              </ul>
            </Col>
            <Col span={8}>
              <h4>📊 信息展示</h4>
              <ul style={{ fontSize: '13px' }}>
                <li>字段变更前后对比</li>
                <li>操作人员和时间信息</li>
                <li>变更原因和描述</li>
                <li>IP地址和用户代理</li>
              </ul>
            </Col>
          </Row>
        </Card>

        {/* 项目变更记录弹窗 */}
        <ProjectChangeLog
          visible={changeLogVisible}
          projectId={testProject?.id}
          project={testProject}
          onCancel={this.handleCloseChangeLog}
        />
      </div>
    );
  }
}

export default ProjectChangeLogTest;
