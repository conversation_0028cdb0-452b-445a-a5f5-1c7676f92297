import React, { Component } from 'react';
import { Card, Button, message, Divider } from 'antd';
import ProjectEditModal from './ProjectEditModal';

/**
 * 项目编辑功能测试组件
 */
class ProjectEditTest extends Component {
  constructor(props) {
    super(props);
    this.state = {
      editModalVisible: false,
      testProject: null,
    };
  }

  // 模拟项目数据
  generateTestProject = () => {
    return {
      id: 'test-project-001',
      projectName: '测试项目 - 春季营销推广活动',
      brand: '品牌A',
      contractType: 'QUARTERLY_FRAME',
      contractSigningStatus: 'signing',
      executionPeriod: ['2024-03-01', '2024-05-31'],
      planningBudget: 1000000,
      projectProfit: 250000,
      grossMargin: 25.0,
      executivePM: 'user001',
      contentMedia: ['user003', 'user006'],
      createTime: '2024-01-15 10:30:00',
      updateTime: '2024-01-20 14:20:00',
      // 其他只读字段
      talentBudget: 300000,
      adBudget: 400000,
      otherBudget: 300000,
      talentCost: 280000,
      adCost: 380000,
      otherCost: 45000,
      talentRebateIncome: 30000,
      settlementRules: '按季度结算，每月预付70%，季度末结清余款',
      kpi: '目标曝光量：1000万\n目标转化率：3%\n目标ROI：1:4',
      expectedPaymentMonth: '2024-06',
      paymentTermDays: 180,
    };
  };

  handleOpenEditModal = () => {
    const testProject = this.generateTestProject();
    this.setState({
      editModalVisible: true,
      testProject,
    });
  };

  handleCloseEditModal = () => {
    this.setState({
      editModalVisible: false,
      testProject: null,
    });
  };

  handleEditSubmit = (formData) => {
    console.log('编辑提交的数据:', formData);
    message.success('项目编辑成功！请查看控制台输出。');
    this.handleCloseEditModal();
  };

  render() {
    const { editModalVisible, testProject } = this.state;

    return (
      <div style={{ padding: '20px' }}>
        <Card title="项目编辑功能测试" style={{ marginBottom: '20px' }}>
          <div style={{ marginBottom: 16 }}>
            <h4>功能说明：</h4>
            <ul>
              <li>✅ 独立的编辑弹窗，与新建项目分离</li>
              <li>✅ 只允许编辑：执行PM、内容媒介、合同签署状态</li>
              <li>✅ 其他字段以只读信息方式展示</li>
              <li>✅ 包含项目基本信息和财务信息展示</li>
              <li>✅ 专业的表单验证</li>
            </ul>
          </div>

          <Divider />

          <div style={{ textAlign: 'center' }}>
            <Button 
              type="primary" 
              size="large"
              onClick={this.handleOpenEditModal}
            >
              打开项目编辑弹窗
            </Button>
          </div>

          <Divider />

          <div style={{ fontSize: '14px', color: '#666' }}>
            <h4>测试项目数据：</h4>
            <ul>
              <li><strong>项目名称：</strong>测试项目 - 春季营销推广活动</li>
              <li><strong>所属品牌：</strong>品牌A</li>
              <li><strong>合同类型：</strong>季框</li>
              <li><strong>当前签署状态：</strong>签署中</li>
              <li><strong>规划预算：</strong>¥1,000,000</li>
              <li><strong>项目利润：</strong>¥250,000</li>
              <li><strong>毛利率：</strong>25.0%</li>
            </ul>
          </div>

          <div style={{ 
            marginTop: 16, 
            padding: '12px', 
            backgroundColor: '#f0f9ff', 
            border: '1px solid #bae6fd', 
            borderRadius: '4px' 
          }}>
            <strong>💡 使用提示：</strong>
            <br />• 点击按钮打开编辑弹窗
            <br />• 只有执行PM、内容媒介、合同签署状态可以编辑
            <br />• 其他信息以只读方式展示
            <br />• 提交后会在控制台输出编辑的数据
          </div>
        </Card>

        {/* 项目编辑弹窗 */}
        <ProjectEditModal
          visible={editModalVisible}
          project={testProject}
          onCancel={this.handleCloseEditModal}
          onSubmit={this.handleEditSubmit}
        />
      </div>
    );
  }
}

export default ProjectEditTest;
