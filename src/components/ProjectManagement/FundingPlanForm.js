import React, { Component } from 'react';
import {
  Form,
  Input,
  // Select,
  Button,
  Row,
  Col,
  InputNumber,
  message,
  Icon,
} from 'antd';
import moment from 'moment';
import { fundingPlanAPI } from '../../services/api';
import MonthWeekRangePicker from './MonthWeekRangePicker';


// const { Option } = Select;

// 表单校验规则
const formRules = {
  weekRange: [
    { required: true, message: '请选择周期' },
  ],
  title: [
    { required: true, message: '请输入资金计划标题' },
    { min: 2, max: 100, message: '标题长度应在2-100个字符之间' },
  ],
  monthWeek: [
    { required: true, message: '请选择月份和周次' },
  ],
  plannedAmount: [
    { required: true, message: '请输入计划金额' },
    { type: 'number', min: 0.01, message: '计划金额必须大于0' },
  ],
  budgetId: [
    { required: true, message: '请选择关联预算' },
  ],
};

class FundingPlanForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      monthOptions: [],
      weekOptions: [],
      selectedMonth: null,
    };
  }

  componentDidMount() {
    this.generateMonthOptions();

    // 如果是编辑模式，填充表单数据
    if (this.props.fundingPlan) {
      this.fillFormData();
    }
  }

  componentDidUpdate(prevProps) {
    // 当fundingPlan属性变化时，重新填充表单
    if (this.props.fundingPlan !== prevProps.fundingPlan) {
      if (this.props.fundingPlan) {
        this.fillFormData();
      } else {
        this.props.form.resetFields();
      }
    }
  }

  // 生成月份选项（基于项目执行周期）
  generateMonthOptions = () => {
    const { projectInfo } = this.props;
    const monthOptions = [];

    if (projectInfo && projectInfo.executionPeriod) {
      const startDate = moment(projectInfo.executionPeriod[0]);
      const endDate = moment(projectInfo.executionPeriod[1]);

      const current = startDate.clone().startOf('month');
      while (current.isSameOrBefore(endDate, 'month')) {
        monthOptions.push({
          value: current.format('YYYY-MM'),
          label: current.format('YYYY年MM月'),
          year: current.year(),
          month: current.month() + 1,
        });
        current.add(1, 'month');
      }
    }

    this.setState({ monthOptions });
  };

  // 生成周次选项
  generateWeekOptions = (monthValue) => {
    if (!monthValue) return [];

    const [year, month] = monthValue.split('-');
    const monthStart = moment(`${year}-${month}-01`);
    const monthEnd = monthStart.clone().endOf('month');

    const weekOptions = [];
    let weekNumber = 1;
    const current = monthStart.clone();

    while (current.isSameOrBefore(monthEnd)) {
      const weekStart = current.clone();
      const weekEnd = current.clone().add(6, 'days');

      // 确保周结束日期不超过月末
      if (weekEnd.isAfter(monthEnd)) {
        weekEnd.set('date', monthEnd.date());
      }

      weekOptions.push({
        value: weekNumber,
        label: `第${weekNumber}周 (${weekStart.format('DD')}-${weekEnd.format('DD')}日)`,
        weekStart: weekStart.format('YYYY-MM-DD'),
        weekEnd: weekEnd.format('YYYY-MM-DD'),
      });

      current.add(7, 'days');
      weekNumber++;

      // 如果下一周的开始日期已经超过了当月，则停止
      if (current.isAfter(monthEnd)) {
        break;
      }
    }

    return weekOptions;
  };

  fillFormData = () => {
    const { fundingPlan } = this.props;
    if (fundingPlan) {
      const monthValue = `${fundingPlan.year}-${String(fundingPlan.month).padStart(2, '0')}`;
      this.setState((prevState) => ({
        ...prevState,
        selectedMonth: monthValue,
        weekOptions: this.generateWeekOptions(monthValue),
      }));

      this.props.form.setFieldsValue({
        title: fundingPlan.title,
        monthWeek: `${monthValue}-${fundingPlan.weekOfMonth}`,
        plannedAmount: fundingPlan.plannedAmount,
        paidAmount: fundingPlan.paidAmount || 0,
        budgetId: fundingPlan.budgetId,
        status: fundingPlan.status || 'draft',
        remarks: fundingPlan.remarks,
      });
    }
  };

  handleMonthWeekChange = (value) => {
    if (!value) return;

    const [yearMonth, week] = value.split('-');
    const [year, month] = yearMonth.split('-');
    console.log(year);
    // 生成标题
    const title = `${this.props.projectInfo?.projectName || ''}-${month}月第${week}周资金计划`;
    this.props.form.setFieldsValue({ title });
  };

  handleSubmit = (e) => {
    e.preventDefault();
    this.props.form.validateFields(async (err, values) => {
      if (err) {
        message.error('请检查表单填写是否正确');
        return;
      }

      this.setState({ loading: true });

      try {
        console.log(values);
        const { year, month, weekOfMonth } = values.weekRange;

        const fundingPlanData = {
          title: values.title,
          year: parseInt(year, 10),
          month: parseInt(month, 10),
          weekOfMonth: parseInt(weekOfMonth, 10),
          plannedAmount: values.plannedAmount,
          paidAmount: values.paidAmount || 0,
          budgetId: this.props.budget.id,
          // status: values.status || 'draft',
          remarks: values.remarks,
        };
        console.log(fundingPlanData);

        let response;
        if (this.props.fundingPlan) {
          // 编辑模式
          response = await fundingPlanAPI.updateFundingPlan(
            this.props.fundingPlan.id,
            fundingPlanData,
          );
        } else {
          // 新建模式
          response = await fundingPlanAPI.createFundingPlan(fundingPlanData);
        }

        if (response.success) {
          message.success(this.props.fundingPlan ? '更新资金计划成功' : '创建资金计划成功');
          this.props.onSubmit && this.props.onSubmit(response.data);
        } else {
          console.log('[ response ] >', response);
          message.error(response.message || '操作失败');
        }
      } catch (error) {
        console.error('Submit funding plan failed:', error);
        message.error(error.message || '操作失败');
      } finally {
        this.setState({ loading: false });
      }
    });
  };

  handleReset = () => {
    this.props.form.resetFields();
    this.setState({ selectedMonth: null, weekOptions: [] });
  };

  handleCancel = () => {
    this.props.onCancel && this.props.onCancel();
  };
  handleWeekRangeChange = (value) => {
    console.log('[ value ] >', value);
  };

  render() {
    const { loading, monthOptions, weekOptions } = this.state;
    const { fundingPlan } = this.props;
    const { getFieldDecorator } = this.props.form;
    const isEdit = !!fundingPlan;
    console.log('[ weekOptions ] >', weekOptions);
    // 生成月份+周次的组合选项
    const monthWeekOptions = [];
    monthOptions.forEach((month) => {
      const weeks = this.generateWeekOptions(month.value);
      weeks.forEach((week) => {
        monthWeekOptions.push({
          value: `${month.value}-${week.value}`,
          label: `${month.label}${week.label}`,
        });
      });
    });

    return (
      <div style={{ padding: '16px' }}>
        <Form layout="vertical" onSubmit={this.handleSubmit}>
          {/* 基本信息 */}
          <Row gutter={12}>
            <Form.Item label="周期" style={{ marginBottom: 0 }}>
              {getFieldDecorator('weekRange', {
                rules: formRules.weekRange,
              })(
                <MonthWeekRangePicker
                  style={{ width: '100%' }}
                  placeholder="请选择月份和周期"
                  onChange={this.handleWeekRangeChange}
                  // projectStartDate={projectStartDate}
                  // projectEndDate={projectEndDate}
                />,
              )}
            </Form.Item>
            <Col span={12}>
              <Form.Item label="计划金额">
                {getFieldDecorator('plannedAmount', {
                  rules: formRules.plannedAmount,
                })(
                  <InputNumber
                    placeholder="请输入计划金额"
                    style={{ width: '100%' }}
                    min={0}
                    step={1000}
                    formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                  />,
                )}
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="计划标题">
                {getFieldDecorator('title', {
                  rules: formRules.title,
                })(
                  <Input
                    placeholder="请输入资金计划标题"
                    prefix={<Icon type="edit" />}
                  />,
                )}
              </Form.Item>
            </Col>
          </Row>


          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="已付金额">
                {getFieldDecorator('paidAmount', {
                  initialValue: 0,
                })(
                  <InputNumber
                    placeholder="请输入已付金额"
                    style={{ width: '100%' }}
                    min={0}
                    step={1000}
                    formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                  />,
                )}
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="备注">
                {getFieldDecorator('remarks')(
                  <Input.TextArea
                    placeholder="请输入备注信息"
                    rows={3}
                  />,
                )}
              </Form.Item>
            </Col>
          </Row>

          {/* 操作按钮 */}
          <Row>
            <Col span={24} style={{ textAlign: 'center', marginTop: 24 }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                size="large"
                style={{ marginRight: 16 }}
                disabled={loading}
                onClick={this.handleSubmit}
              >
                <Icon type={isEdit ? 'save' : 'plus'} />
                {isEdit ? '更新计划' : '创建计划'}
              </Button>
              <Button
                onClick={this.handleReset}
                size="large"
                style={{ marginRight: 16 }}
              >
                <Icon type="reload" />
                重置表单
              </Button>
              <Button
                onClick={this.handleCancel}
                size="large"
              >
                <Icon type="close" />
                取消
              </Button>
            </Col>
          </Row>
        </Form>
      </div>
    );
  }
}

export default Form.create()(FundingPlanForm);
