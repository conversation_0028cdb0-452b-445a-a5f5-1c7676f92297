/* WeeklyBudgetForm 美化样式 */

.formContainer {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding: 20px;
}

.mainCard {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: none;
  overflow: hidden;
  transition: all 0.3s ease;
}

.mainCard:hover {
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.cardHeader {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  padding: 24px 32px;
  border-bottom: none;
  position: relative;
  overflow: hidden;
}

.cardHeader::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.cardTitle {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  gap: 12px;
}

.titleIcon {
  font-size: 28px;
  color: rgba(255, 255, 255, 0.9);
}

.sectionCard {
  border: 1px solid #e8f4fd;
  border-radius: 12px;
  background: #fafcff;
  margin-bottom: 24px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.sectionCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.sectionCard:hover {
  border-color: #91d5ff;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.1);
}

.sectionCard:hover::before {
  width: 6px;
}

.sectionHeader {
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);
  border-bottom: 1px solid #e8f4fd;
  padding: 16px 24px;
  font-weight: 600;
  color: #1890ff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.sectionIcon {
  font-size: 18px;
  color: #1890ff;
}

.formItem {
  margin-bottom: 24px;
}

.formLabel {
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.requiredMark {
  color: #ff4d4f;
}

.helpIcon {
  color: #8c8c8c;
  cursor: help;
}

.inputField {
  border-radius: 8px;
  border: 2px solid #d9d9d9;
  transition: all 0.3s ease;
  padding: 12px 16px;
  font-size: 14px;
}

.inputField:hover {
  border-color: #40a9ff;
}

.inputField:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.selectField {
  border-radius: 8px;
}

.selectField .ant-select-selector {
  border: 2px solid #d9d9d9 !important;
  border-radius: 8px !important;
  padding: 8px 12px !important;
  transition: all 0.3s ease !important;
}

.selectField:hover .ant-select-selector {
  border-color: #40a9ff !important;
}

.selectField.ant-select-focused .ant-select-selector {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.numberInput {
  border-radius: 8px;
  border: 2px solid #d9d9d9;
  transition: all 0.3s ease;
}

.numberInput:hover {
  border-color: #40a9ff;
}

.numberInput:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.textArea {
  border-radius: 8px;
  border: 2px solid #d9d9d9;
  transition: all 0.3s ease;
  resize: vertical;
}

.textArea:hover {
  border-color: #40a9ff;
}

.textArea:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.buttonGroup {
  text-align: center;
  padding: 32px 0;
  background: #fafafa;
  border-radius: 12px;
  margin-top: 24px;
}

.primaryButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  height: 48px;
  padding: 0 32px;
  font-size: 16px;
  font-weight: 600;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  margin-right: 16px;
}

.primaryButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 24px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.secondaryButton {
  border: 2px solid #d9d9d9;
  border-radius: 8px;
  height: 48px;
  padding: 0 24px;
  font-size: 16px;
  font-weight: 500;
  background: #fff;
  color: #595959;
  transition: all 0.3s ease;
  margin-right: 16px;
}

.secondaryButton:hover {
  border-color: #40a9ff;
  color: #1890ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cancelButton {
  border: 2px solid #ff7875;
  border-radius: 8px;
  height: 48px;
  padding: 0 24px;
  font-size: 16px;
  font-weight: 500;
  background: #fff;
  color: #ff4d4f;
  transition: all 0.3s ease;
}

.cancelButton:hover {
  background: #ff4d4f;
  color: #fff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  border-radius: 16px;
}

.progressBar {
  margin: 16px 0;
}

.alertMessage {
  margin-bottom: 24px;
  border-radius: 8px;
  border: none;
}

.stepIndicator {
  margin-bottom: 32px;
  background: #fff;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.fieldGroup {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.fieldGroup:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.calculatedField {
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
  border: 2px solid #b7eb8f;
  border-radius: 8px;
  padding: 12px 16px;
  font-weight: 600;
  color: #389e0d;
}

.errorField {
  border-color: #ff4d4f !important;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
}

.successField {
  border-color: #52c41a !important;
  box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .formContainer {
    padding: 12px;
  }
  
  .cardHeader {
    padding: 16px 20px;
  }
  
  .cardTitle {
    font-size: 20px;
  }
  
  .sectionHeader {
    padding: 12px 16px;
  }
  
  .buttonGroup {
    padding: 20px 0;
  }
  
  .primaryButton,
  .secondaryButton,
  .cancelButton {
    height: 40px;
    font-size: 14px;
    margin-bottom: 12px;
    width: 100%;
    margin-right: 0;
  }
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slideInUp {
  animation: slideInUp 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fadeIn {
  animation: fadeIn 0.4s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}
