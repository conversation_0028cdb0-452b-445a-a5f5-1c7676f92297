import React, { Component } from 'react';
import { Modal } from 'antd';
// import FundingPlanTable from './FundingPlanTable';
import FundingPlanManagement from './FundingPlanManagement';
// import moment from 'moment';


class FundingPlanModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      // userCache: {}, // 用户ID到用户名的映射缓存
      // brandCache: {}, // 品牌ID到品牌名的映射缓存
    };
  }


  render() {
    const { visible, onCancel } = this.props;
    try {
      return (
        <Modal
          title="资金计划"
          visible={visible}
          onCancel={onCancel}
          footer={null}
          width={1200}
          destroyOnClose
        >
          <FundingPlanManagement selectedBudget={this.props.selectedBudget} />
        </Modal>
      );
    } catch (error) {
      console.error('ProjectDetailModal render error:', error);
      return (
        <div style={{ padding: '20px', textAlign: 'center' }}>
          <p>项目详情加载失败，请稍后重试</p>
          <p style={{ color: '#999', fontSize: '12px' }}>错误信息：{error.message}</p>
        </div>
      );
    }
  }
}

export default FundingPlanModal;
