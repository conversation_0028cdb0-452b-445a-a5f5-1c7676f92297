import React, { Component } from 'react';
import {
  Table,
  Button,
  Select,
  Row,
  Col,
  Card,
  message,
  Modal,
  Tag,
  Divider,
  Popconfirm,
} from 'antd';
import { fundingPlanAPI } from '../../services/api';

const { Option } = Select;
const { confirm } = Modal;

class FundingPlanTable extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      fundingPlans: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      filters: {
        year: undefined,
        month: undefined,
        isPaid: undefined,
      },
      selectedRowKeys: [],
    };
  }

  componentDidMount() {
    this.loadFundingPlans();
  }

  // 加载资金计划列表
  loadFundingPlans = async (params = {}) => {
    this.setState({ loading: true });
    console.log('this.props.selectedBudget', this.props.selectedBudget);
    try {
      const queryParams = {
        projectId: this.props.projectId,
        budgetId: this.props.selectedBudget.id,
        page: this.state.pagination.current,
        pageSize: this.state.pagination.pageSize,
        ...this.state.filters,
        ...params,
      };

      const response = await fundingPlanAPI.getFundingPlans(queryParams);

      if (response.success) {
        this.setState((prevState) => ({
          fundingPlans: response.data.fundingPlans || [],
          pagination: {
            ...prevState.pagination,
            total: response.data.total || 0,
            current: response.data.page || 1,
          },
        }));
      } else {
        message.error(response.message || '加载资金计划列表失败');
      }
    } catch (error) {
      console.error('Load funding plans failed:', error);
      message.error('加载资金计划列表失败');
    } finally {
      this.setState({ loading: false });
    }
  };

  // 处理筛选变化
  handleFilterChange = (key, value) => {
    this.setState(
      (prevState) => {
        const newFilters = {
          ...prevState.filters,
          [key]: value,
        };
        return { filters: newFilters };
      },
      () => {
        this.loadFundingPlans({ ...this.state.filters, page: 1 });
      },
    );
  };

  // 处理分页变化
  handleTableChange = (pagination, filters, sorter) => {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...this.state.filters,
    };

    if (sorter.field) {
      params.sortBy = sorter.field;
      params.sortOrder = sorter.order === 'ascend' ? 'asc' : 'desc';
    }

    this.setState((prevState) => ({
      pagination: {
        ...prevState.pagination,
        current: pagination.current,
        pageSize: pagination.pageSize,
      },
    }));

    this.loadFundingPlans(params);
  };

  // 处理新建
  handleCreate = () => {
    this.props.onEdit && this.props.onEdit(null);
  };

  // 处理编辑
  handleEdit = (record) => {
    this.props.onEdit && this.props.onEdit(record);
  };

  // 处理查看
  handleView = (record) => {
    this.props.onView && this.props.onView(record);
  };

  // 处理删除
  handleDelete = async (id) => {
    try {
      const response = await fundingPlanAPI.deleteFundingPlan(id);
      if (response.success) {
        message.success('删除成功');
        this.loadFundingPlans();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      console.error('Delete funding plan failed:', error);
      message.error('删除失败');
    }
  };

  // 处理批量删除
  handleBatchDelete = () => {
    const { selectedRowKeys } = this.state;
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的资金计划');
      return;
    }

    confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedRowKeys.length} 个资金计划吗？`,
      onOk: async () => {
        try {
          const response = await fundingPlanAPI.batchDeleteFundingPlans(selectedRowKeys);
          if (response.success) {
            message.success('批量删除成功');
            this.setState({ selectedRowKeys: [] });
            this.loadFundingPlans();
          } else {
            message.error(response.message || '批量删除失败');
          }
        } catch (error) {
          console.error('Batch delete failed:', error);
          message.error('批量删除失败');
        }
      },
    });
  };


  render() {
    const { loading, fundingPlans, pagination, filters, selectedRowKeys } = this.state;

    const columns = [
      {
        title: '计划标题',
        dataIndex: 'title',
        key: 'title',
        width: 200,
        ellipsis: true,
      },
      {
        title: '时间',
        key: 'time',
        width: 120,
        render: (text, record) => (
          <span>{record.year}年{record.month}月第{record.weekOfMonth}周</span>
        ),
        sorter: true,
      },
      {
        title: '计划金额',
        dataIndex: 'plannedAmount',
        key: 'plannedAmount',
        width: 120,
        render: (amount) => (
          <span style={{ fontWeight: 'bold', color: '#1890ff' }}>
            ¥{amount?.toLocaleString()}
          </span>
        ),
        sorter: true,
      },
      {
        title: '已付金额',
        dataIndex: 'paidAmount',
        key: 'paidAmount',
        width: 120,
        render: (amount) => (
          <span style={{ fontWeight: 'bold', color: '#52c41a' }}>
            ¥{(amount || 0).toLocaleString()}
          </span>
        ),
        sorter: true,
      },
      {
        title: '计划状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        render: (status) => {
          const statusConfig = {
            draft: { color: 'default', text: '草稿' },
            submitted: { color: 'processing', text: '已提交' },
            approved: { color: 'success', text: '已审批' },
            executing: { color: 'warning', text: '执行中' },
            completed: { color: 'success', text: '已完成' },
            cancelled: { color: 'error', text: '已取消' },
          };
          const config = statusConfig[status] || { color: 'default', text: status };
          return <Tag color={config.color}>{config.text}</Tag>;
        },
      },
      {
        title: '备注',
        dataIndex: 'remarks',
        key: 'remarks',
        ellipsis: true,
        render: (remarks) => remarks || '-',
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 120,
        render: (createdAt) => {
          if (!createdAt) return '-';
          return new Date(createdAt).toLocaleDateString();
        },
        sorter: true,
      },
      {
        title: '操作',
        key: 'action',
        width: 150,
        fixed: 'right',
        render: (text, record) => (
          <div>
            <Button
              type="link"
              size="small"
              onClick={() => this.handleView(record)}
            >
              查看
            </Button>
            <Divider type="vertical" />
            <Button
              type="link"
              size="small"
              onClick={() => this.handleEdit(record)}
            >
              编辑
            </Button>
            <Divider type="vertical" />
            <Popconfirm
              title="确定要删除这个资金计划吗？"
              onConfirm={() => this.handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="link"
                size="small"
                style={{ color: '#ff4d4f' }}
              >
                删除
              </Button>
            </Popconfirm>
          </div>
        ),
      },
    ];

    const rowSelection = {
      selectedRowKeys,
      onChange: (newSelectedRowKeys) => {
        this.setState({ selectedRowKeys: newSelectedRowKeys });
      },
    };

    return (
      <Card
        title="资金计划列表"
        extra={
          <Button type="primary" onClick={this.handleCreate}>
            新建资金计划
          </Button>
        }
      >
        {/* 筛选区域 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Select
              placeholder="选择年份"
              allowClear
              style={{ width: '100%' }}
              value={filters.year}
              onChange={(value) => this.handleFilterChange('year', value)}
            >
              <Option value={2024}>2024年</Option>
              <Option value={2025}>2025年</Option>
            </Select>
          </Col>
          <Col span={6}>
            <Select
              placeholder="选择月份"
              allowClear
              style={{ width: '100%' }}
              value={filters.month}
              onChange={(value) => this.handleFilterChange('month', value)}
            >
              {Array.from({ length: 12 }, (_, i) => (
                <Option key={i + 1} value={i + 1}>
                  {i + 1}月
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={6}>
            <Select
              placeholder="计划状态"
              allowClear
              style={{ width: '100%' }}
              value={filters.status}
              onChange={(value) => this.handleFilterChange('status', value)}
            >
              <Option value="draft">草稿</Option>
              <Option value="submitted">已提交</Option>
              <Option value="approved">已审批</Option>
              <Option value="executing">执行中</Option>
              <Option value="completed">已完成</Option>
              <Option value="cancelled">已取消</Option>
            </Select>
          </Col>
          <Col span={6}>
            {selectedRowKeys.length > 0 && (
              <Button
                type="danger"
                onClick={this.handleBatchDelete}
              >
                批量删除 ({selectedRowKeys.length})
              </Button>
            )}
          </Col>
        </Row>

        {/* 表格 */}
        <Table
          columns={columns}
          dataSource={fundingPlans}
          rowKey="id"
          loading={loading}
          pagination={pagination}
          rowSelection={rowSelection}
          onChange={this.handleTableChange}
          scroll={{ x: 1000 }}
        />
      </Card>
    );
  }
}

export default FundingPlanTable;
