import React, { Component } from 'react';
import {
  Modal,
  Timeline,
  Card,
  Tag,
  Avatar,
  Row,
  Col,
  Spin,
  Empty,
  Button,
  Select,
  DatePicker,
  Input,
  Tooltip,
  // Divider,
  Badge,
  Icon,
  message,
} from 'antd';
import moment from 'moment';
import { projectAPI, userApi } from '../../services/api';
import {
  // CONTRACT_SIGNING_STATUS,
  // CONTRACT_TYPES,
  // PROJECT_STATUS,
  getContractSigningStatusConfig,
  getContractTypeLabel,
  getProjectStatusConfig,
} from '../../utils/projectUtils';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Search } = Input;

// 变更类型配置
const CHANGE_TYPE_CONFIG = {
  CREATE: { label: '创建', color: '#52c41a', icon: 'plus-circle' },
  UPDATE: { label: '更新', color: '#1890ff', icon: 'edit' },
  DELETE: { label: '删除', color: '#f5222d', icon: 'delete' },
  STATUS_CHANGE: { label: '状态变更', color: '#722ed1', icon: 'swap' },
  BUDGET_CHANGE: { label: '预算调整', color: '#fa8c16', icon: 'dollar' },
  TEAM_CHANGE: { label: '团队变更', color: '#13c2c2', icon: 'team' },
  CONTRACT_CHANGE: { label: '合同变更', color: '#eb2f96', icon: 'file-text' },
};

class ProjectChangeLog extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      changeLogs: [],
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0,
      },
      filters: {
        changeType: undefined,
        dateRange: undefined,
        operator: undefined,
        searchText: '',
      },
      userCache: {}, // 用户ID到用户名的映射缓存
      brandCache: {}, // 品牌ID到品牌名的映射缓存
    };
  }

  componentDidMount() {
    if (this.props.visible && this.props.projectId) {
      this.loadChangeLogs();
    }
  }

  componentDidUpdate(prevProps) {
    if (this.props.visible && !prevProps.visible && this.props.projectId) {
      this.loadChangeLogs();
    }
  }

  // 加载变更记录
  loadChangeLogs = async (params = {}) => {
    const { projectId } = this.props;
    if (!projectId) return;

    this.setState({ loading: true });

    try {
      const { pagination, filters } = this.state;
      const requestParams = {
        page: params.page || pagination.current,
        pageSize: params.pageSize || pagination.pageSize,
        sortOrder: 'desc',
        ...filters,
        ...params,
      };

      // 处理日期范围
      if (requestParams.dateRange && requestParams.dateRange.length === 2) {
        requestParams.startDate = requestParams.dateRange[0].format('YYYY-MM-DD');
        requestParams.endDate = requestParams.dateRange[1].format('YYYY-MM-DD');
        delete requestParams.dateRange;
      }

      const response = await projectAPI.getProjectChangeLogs(projectId, requestParams);

      if (response.success) {
        this.setState({
          changeLogs: response.data.changeLogs || [],
          pagination: {
            current: response.data.page || 1,
            pageSize: response.data.pageSize || 20,
            total: response.data.total || 0,
          },
        });
      } else {
        message.error(response.message || '获取变更记录失败');
      }
    } catch (error) {
      console.error('Load change logs failed:', error);
      message.error('获取变更记录失败');
    } finally {
      this.setState({ loading: false });
    }
  };

  // 处理筛选变更
  handleFilterChange = (key, value) => {
    this.setState(
      (prevState) => ({
        filters: {
          ...prevState.filters,
          [key]: value,
        },
        pagination: {
          ...prevState.pagination,
          current: 1,
        },
      }),
      () => {
        this.loadChangeLogs();
      },
    );
  };

  // 处理分页变更
  handlePaginationChange = (page, pageSize) => {
    this.loadChangeLogs({ page, pageSize });
  };

  // 格式化变更详情
  formatChangeDetails = (changeLog) => {
    const { changeDetails, beforeData, afterData, changedFields } = changeLog;

    if (!changedFields || changedFields.length === 0) {
      return changeDetails?.description || '无详细信息';
    }

    return changedFields.map((field) => {
      const before = beforeData?.[field];
      const after = afterData?.[field];
      console.log(after);
      return (
        <div key={field} style={{ marginBottom: 4 }}>
          <span style={{ fontWeight: 500 }}>{this.getFieldLabel(field)}:</span>
          <span style={{ color: '#722ed1', textDecoration: 'line-through', marginLeft: 8 }}>
            {this.formatFieldValue(before, field)}
          </span>
          <Icon type="arrow-right" style={{ margin: '0 8px', color: '#8c8c8c' }} />
          <span style={{ color: '#52c41a', fontWeight: 500 }}>
            {this.formatFieldValue(after, field)}
          </span>
        </div>
      );
    });
  };

  // 获取字段标签
  getFieldLabel = (field) => {
    const fieldLabels = {
      // 基本信息
      projectName: '项目名称',
      status: '项目状态',
      brand: '所属品牌',
      brandId: '所属品牌',
      documentType: '单据类型',

      // 执行信息
      executivePM: '执行PM',
      executorPM: '执行PM',
      contentMedia: '内容媒介',
      contentMediaIds: '内容媒介',
      executionPeriod: '执行周期',
      period: '执行周期',

      // 合同信息
      contractType: '合同类型',
      contractSigningStatus: '合同签署状态',
      settlementRules: '结算规则',
      kpi: 'KPI指标',

      // 预算信息
      planningBudget: '规划预算',
      talentBudget: '达人预算',
      influencerBudget: '达人预算',
      adBudget: '投流预算',
      otherBudget: '其他预算',

      // 成本信息
      talentCost: '达人成本',
      influencerCost: '达人成本',
      adCost: '投流成本',
      otherCost: '其他成本',
      estimatedTalentRebate: '预估达人返点',
      estimatedInfluencerRebate: '预估达人返点',

      // 收入信息
      talentRebateIncome: '达人返点收入',

      // 付款信息
      expectedPaymentMonth: '预期付款月份',
      paymentTermDays: '付款期限天数',

      // 系统字段
      createdAt: '创建时间',
      updatedAt: '更新时间',
      createTime: '创建时间',
      updateTime: '更新时间',
      cost: '项目成本',
    };
    return fieldLabels[field] || field;
  };

  // 格式化字段值
  formatFieldValue = (value, field) => {
    if (value === null || value === undefined || value === '') return '空';
    // 布尔值处理
    if (typeof value === 'boolean') return value ? '是' : '否';

    // 数组处理
    if (Array.isArray(value) && !field.includes('contentMedia')) {
      if (value.length === 0) return '空';
      return value.join(', ');
    }

    // 根据字段类型进行特殊处理
    switch (field) {
      // 合同签署状态
      case 'contractSigningStatus':
        return this.formatContractSigningStatus(value);

      // 合同类型
      case 'contractType':
        return this.formatContractType(value);

      // 项目状态
      case 'status':
        return this.formatProjectStatus(value);

      // 预算和成本字段（数字格式化）
      case 'planningBudget':
      case 'talentBudget':
      case 'influencerBudget':
      case 'adBudget':
      case 'otherBudget':
      case 'talentCost':
      case 'influencerCost':
      case 'adCost':
      case 'otherCost':
      case 'estimatedTalentRebate':
      case 'estimatedInfluencerRebate':
      case 'talentRebateIncome':
        return this.formatCurrency(value);

      // 日期字段
      case 'createdAt':
      case 'updatedAt':
      case 'createTime':
      case 'updateTime':
        return this.formatDateTime(value);

      // 执行周期
      case 'executionPeriod':
      case 'period':
        return this.formatExecutionPeriod(value);

      // 用户ID字段
      case 'executivePM':
      case 'executorPM':
        return this.formatUserId(value);

      // 品牌ID字段
      case 'brand':
      case 'brandId':
        return this.formatBrandId(value);

      // 内容媒介
      case 'contentMedia':
      case 'contentMediaIds':
        return this.formatContentMedia(value);

      case 'cost':
        return this.formatCost(value);

      default:
        console.log('default');
        return String(value);
    }
  };

  // 格式化合同签署状态
  formatContractSigningStatus = (status) => {
    const config = getContractSigningStatusConfig(status);
    return config.label;
  };

  // 格式化合同类型
  formatContractType = (type) => {
    return getContractTypeLabel(type);
  };

  // 格式化项目状态
  formatProjectStatus = (status) => {
    const config = getProjectStatusConfig(status);
    return config.label;
  };

  // 格式化货币
  formatCurrency = (amount) => {
    if (!amount || isNaN(amount)) return '¥0';
    return `¥${Number(amount).toLocaleString()}`;
  };

  // 格式化日期时间
  formatDateTime = (dateTime) => {
    if (!dateTime) return '空';
    const momentDate = moment(dateTime);
    if (!momentDate.isValid()) return String(dateTime);
    return momentDate.format('YYYY-MM-DD HH:mm:ss');
  };

  // 格式化执行周期
  formatExecutionPeriod = (period) => {
    if (Array.isArray(period) && period.length === 2) {
      return `${period[0]} ~ ${period[1]}`;
    }
    if (typeof period === 'object' && period.startDate && period.endDate) {
      return `${period.startDate} ~ ${period.endDate}`;
    }
    return String(period);
  };

  // 格式化用户ID
  formatUserId = (userId) => {
    console.log('[ userId ] >', userId);
    if (!userId) return '空';
    const { userCache } = this.state;

    // 如果缓存中有用户名，直接返回
    console.log('[ userCache ] >', userCache);
    if (userCache[userId]) {
      return userCache[userId];
    }

    // 如果是用户ID格式，尝试异步获取用户名
    if (typeof userId === 'string' && userId.length > 0) {
      this.loadUserName(userId);
      return userId; // 暂时返回ID，异步加载完成后会更新
    }

    return String(userId);
  };

  // 格式化品牌ID
  formatBrandId = (brandId) => {
    if (!brandId) return '空';
    const { brandCache } = this.state;

    // 如果缓存中有品牌名，直接返回
    if (brandCache[brandId]) {
      return brandCache[brandId];
    }

    // 如果是品牌ID格式，尝试异步获取品牌名
    if (typeof brandId === 'string' || typeof brandId === 'number') {
      this.loadBrandName(brandId);
      return String(brandId); // 暂时返回ID，异步加载完成后会更新
    }

    return String(brandId);
  };

  // 格式化内容媒介
  formatContentMedia = (media) => {
    if (!media) return '空';
    console.log('格式化媒介');
    if (Array.isArray(media)) {
      if (media.length === 0) return '空';
      return media.map((item) => {
        if (typeof item === 'object' && item.name) {
          return item.name;
        }
        return this.formatUserId(item);
      }).join(', ');
    }

    if (typeof media === 'string') {
      // 如果是逗号分隔的字符串
      if (media.includes(',')) {
        return media.split(',').map((item) => this.formatUserId(item.trim())).join(', ');
      }
      return this.formatUserId(media);
    }

    return String(media);
  };

  formatCost = (cost) => {
    if (!cost) return '空';
    // cost 包含多个字段influencerCost,adCost,otherCost
    if (typeof cost === 'object') {
      return `达人成本: ${this.formatCurrency(cost.influencerCost)}，投流成本: ${this.formatCurrency(cost.adCost)}，其他成本: ${this.formatCurrency(cost.otherCost)}`;
    }
    return this.formatCurrency(cost);
  };

  // 异步加载用户名
  loadUserName = async (userId) => {
    try {
      // 避免重复请求
      const { userCache } = this.state;
      if (userCache[userId] || userCache[userId] === 'loading') {
        return;
      }

      // 标记为加载中
      this.setState({
        userCache: {
          ...userCache,
          [userId]: 'loading',
        },
      });

      // 这里可以调用用户信息API
      // 暂时使用模拟数据
      const userName = await this.getUserName(userId);

      this.setState((prevState) => ({
        userCache: {
          ...prevState.userCache,
          [userId]: userName,
        },
      }));
    } catch (error) {
      console.error('Load user name failed:', error);
      // 加载失败时保留原ID
      this.setState((prevState) => ({
        userCache: {
          ...prevState.userCache,
          [userId]: userId,
        },
      }));
    }
  };

  // 异步加载品牌名
  loadBrandName = async (brandId) => {
    try {
      // 避免重复请求
      const { brandCache } = this.state;
      if (brandCache[brandId] || brandCache[brandId] === 'loading') {
        return;
      }

      // 标记为加载中
      this.setState({
        brandCache: {
          ...brandCache,
          [brandId]: 'loading',
        },
      });

      // 这里可以调用品牌信息API
      // 暂时使用模拟数据
      const brandName = await this.mockGetBrandName(brandId);

      this.setState((prevState) => ({
        brandCache: {
          ...prevState.brandCache,
          [brandId]: brandName,
        },
      }));
    } catch (error) {
      console.error('Load brand name failed:', error);
      // 加载失败时保留原ID
      this.setState((prevState) => ({
        brandCache: {
          ...prevState.brandCache,
          [brandId]: String(brandId),
        },
      }));
    }
  };

  // 获取用户名
  getUserName = async (userId) => {
    if (!userId) return '';
    try {
      const response = await userApi.fetchUserName(userId);
      if (response.success) {
        console.log('[ response.data ] >', response.data);
        return response.data.name;
      }
      throw new Error(response.message);
    } catch (error) {
      console.error('Fetch user name failed:', error);
      return userId;
    }
  };

  // 模拟获取品牌名（实际项目中应该调用真实API）
  mockGetBrandName = async (brandId) => {
    // 模拟API延迟
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 模拟品牌数据
    const mockBrands = {
      1: '品牌A',
      2: '品牌B',
      3: '品牌C',
      brand_a: '品牌A',
      brand_b: '品牌B',
      brand_c: '品牌C',
    };

    return mockBrands[brandId] || `品牌${brandId}`;
  };

  // 获取变更类型配置
  getChangeTypeConfig = (changeType) => {
    return CHANGE_TYPE_CONFIG[changeType] || {
      label: changeType,
      color: '#8c8c8c',
      icon: 'question-circle',
    };
  };

  // 渲染时间轴项目
  renderTimelineItem = (changeLog) => {
    const config = this.getChangeTypeConfig(changeLog.changeType);
    const time = moment(changeLog.createdAt);

    return (
      <Timeline.Item
        key={changeLog.id}
        dot={
          <Avatar
            size="small"
            style={{ backgroundColor: config.color }}
            icon={config.icon}
          />
        }
      >
        <Card
          size="small"
          style={{ marginBottom: 16 }}
          bodyStyle={{ padding: '12px 16px' }}
        >
          <Row gutter={16} align="middle">
            <Col flex="auto">
              <div style={{ marginBottom: 8 }}>
                <Tag color={config.color} style={{ marginRight: 8 }}>
                  {config.label}
                </Tag>
                <span style={{ fontWeight: 500, fontSize: '14px' }}>
                  {changeLog.changeTitle}
                </span>
              </div>

              <div style={{ color: '#666', fontSize: '12px', marginBottom: 8 }}>
                {this.formatChangeDetails(changeLog)}
              </div>

              <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                {/* 用黄色 */}
                <span style={{ color: '#8c8c8c', fontSize: '12px' }}>
                  <Icon type="user" style={{ marginRight: 4 }} />
                  {this.formatUserId(changeLog.operatorId)}
                </span>
                <span style={{ color: '#8c8c8c', fontSize: '12px' }}>
                  <Icon type="clock-circle" style={{ marginRight: 4 }} />
                  {time.format('YYYY-MM-DD HH:mm:ss')}
                </span>
                {changeLog.operatorIP && (
                  <Tooltip title={`IP: ${changeLog.operatorIP}`}>
                    <span style={{ color: '#8c8c8c', fontSize: '12px' }}>
                      <Icon type="environment" style={{ marginRight: 4 }} />
                      {changeLog.operatorIP}
                    </span>
                  </Tooltip>
                )}
              </div>

              {changeLog.reason && (
                <div style={{ marginTop: 8, padding: '6px 12px', backgroundColor: '#f5f5f5', borderRadius: 4 }}>
                  <span style={{ fontSize: '12px', color: '#666' }}>
                    <Icon type="message" style={{ marginRight: 4 }} />
                    变更原因: {changeLog.reason}
                  </span>
                </div>
              )}
            </Col>
          </Row>
        </Card>
      </Timeline.Item>
    );
  };

  render() {
    const { visible, onCancel, project } = this.props;
    const { loading, changeLogs, pagination, filters } = this.state;

    return (
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Icon type="history" style={{ marginRight: 8, color: '#1890ff' }} />
            <span>项目变更记录</span>
            {project && (
              <Tag color="blue" style={{ marginLeft: 12 }}>
                {project.projectName}
              </Tag>
            )}
          </div>
        }
        visible={visible}
        onCancel={onCancel}
        footer={null}
        width={1000}
        bodyStyle={{ padding: '16px 24px' }}
        destroyOnClose
      >
        {/* 筛选区域 */}
        <Card size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16} align="middle">
            <Col span={6}>
              <Select
                placeholder="变更类型"
                value={filters.changeType}
                onChange={(value) => this.handleFilterChange('changeType', value)}
                style={{ width: '100%' }}
                allowClear
              >
                {Object.entries(CHANGE_TYPE_CONFIG).map(([key, config]) => (
                  <Option key={key} value={key}>
                    <Icon type={config.icon} style={{ marginRight: 8, color: config.color }} />
                    {config.label}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={8}>
              <RangePicker
                placeholder={['开始日期', '结束日期']}
                value={filters.dateRange}
                onChange={(value) => this.handleFilterChange('dateRange', value)}
                style={{ width: '100%' }}
              />
            </Col>
            <Col span={6}>
              <Search
                placeholder="搜索操作人员"
                value={filters.searchText}
                onChange={(e) => this.handleFilterChange('searchText', e.target.value)}
                allowClear
              />
            </Col>
            <Col span={4}>
              <Button onClick={() => this.loadChangeLogs()} icon="reload">
                刷新
              </Button>
            </Col>
          </Row>
        </Card>

        {/* 变更记录内容 */}
        <div style={{ maxHeight: '60vh', overflowY: 'auto' }}>
          <Spin spinning={loading}>
            {changeLogs.length > 0 ? (
              <>
                <div style={{ marginBottom: 16, color: '#8c8c8c', fontSize: '12px' }}>
                  <Badge count={pagination.total} style={{ backgroundColor: '#1890ff' }} />
                  <span style={{ marginLeft: 8 }}>共 {pagination.total} 条变更记录</span>
                </div>

                <Timeline>
                  {changeLogs.map(this.renderTimelineItem)}
                </Timeline>

                {pagination.total > pagination.pageSize && (
                  <div style={{ textAlign: 'center', marginTop: 16 }}>
                    <Button
                      onClick={() => this.handlePaginationChange(pagination.current + 1)}
                      disabled={pagination.current * pagination.pageSize >= pagination.total}
                      loading={loading}
                    >
                      加载更多
                    </Button>
                  </div>
                )}
              </>
            ) : (
              <Empty
                description="暂无变更记录"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            )}
          </Spin>
        </div>
      </Modal>
    );
  }
}

export default ProjectChangeLog;
