import React, { Component } from 'react';
import {
  Descriptions,
  Tag,
  Card,
  Row,
  Col,
  Statistic,
  // Progress,
  // Divider,
  // Badge,
} from 'antd';
import moment from 'moment';

import { userApi } from '../../services/api';
import {
  PROJECT_STATUS,
  getContractTypeLabel,
  getContractSigningStatusConfig,
} from '../../utils/projectUtils';

class ProjectDetailModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      userCache: {}, // 用户ID到用户名的映射缓存
      brandCache: {}, // 品牌ID到品牌名的映射缓存
    };
  }
  formatCurrency = (amount) => {
    try {
      if (!amount || isNaN(amount)) return '¥0';
      return `¥${Number(amount).toLocaleString()}`;
    } catch (error) {
      console.warn('Format currency error:', error);
      return '¥0';
    }
  };

  formatDate = (date) => {
    try {
      if (!date) return '-';
      const momentDate = moment(date);
      if (!momentDate.isValid()) return '-';
      return momentDate.format('YYYY-MM-DD');
    } catch (error) {
      console.warn('Format date error:', error);
      return '-';
    }
  };

  getProjectStatusText = (status) => {
    try {
      if (!status) return '未知状态';
      const statusConfig = PROJECT_STATUS.find((item) => item.value === status);
      return statusConfig ? statusConfig.label : status;
    } catch (error) {
      console.warn('Get project status text error:', error);
      return '未知状态';
    }
  };

  getProjectStatusColor = (status) => {
    try {
      const statusMap = {
        draft: 'default',
        active: 'processing',
        completed: 'success',
        cancelled: 'error',
      };
      return statusMap[status] || 'default';
    } catch (error) {
      console.warn('Get project status color error:', error);
      return 'default';
    }
  };
  // 格式化用户ID
  formatUserId = (userId) => {
    console.log('[ -----userId ] >', userId);
    if (!userId) return '空';
    console.log(this.state);
    const { userCache } = this.state;

    // 如果缓存中有用户名，直接返回
    console.log('[ userCache ] >', userCache);
    if (userCache[userId]) {
      console.log('[ userCache[userId] ] >', userCache[userId]);
      return userCache[userId];
    }

    // 如果是用户ID格式，尝试异步获取用户名
    if (typeof userId === 'string' && userId.length > 0) {
      this.loadUserName(userId);
      return userId; // 暂时返回ID，异步加载完成后会更新
    }

    return String(userId);
  };

  formatUserInfo = (userInfo) => {
    console.log('开始格式化媒介');
    console.log('[ userInfo ] >', typeof userInfo);
    try {
      if (!userInfo) return '-';
      if (typeof userInfo === 'string') return this.formatUserId(userInfo);
      if (typeof userInfo === 'object') {
        if (userInfo.name) return userInfo.name;
        if (userInfo.userid) return this.formatUserId(userInfo.userid);
      }
      return '-';
    } catch (error) {
      console.warn('Format user info error:', error);
      return '-';
    }
  };


  // 异步加载用户名
  loadUserName = async (userId) => {
    try {
      // 避免重复请求
      const { userCache } = this.state;
      if (userCache[userId] || userCache[userId] === 'loading') {
        return;
      }

      // 标记为加载中
      this.setState({
        userCache: {
          ...userCache,
          [userId]: 'loading',
        },
      });

      // 这里可以调用用户信息API
      // 暂时使用模拟数据
      const userName = await this.getUserName(userId);

      this.setState((prevState) => ({
        userCache: {
          ...prevState.userCache,
          [userId]: userName,
        },
      }));
    } catch (error) {
      console.error('Load user name failed:', error);
      // 加载失败时保留原ID
      this.setState((prevState) => ({
        userCache: {
          ...prevState.userCache,
          [userId]: userId,
        },
      }));
    }
  };

  // 获取用户名
  getUserName = async (userId) => {
    if (!userId) return '';
    try {
      const response = await userApi.fetchUserName(userId);
      if (response.success) {
        console.log('[ response.data ] >', response.data);
        return response.data.name;
      }
      throw new Error(response.message);
    } catch (error) {
      console.error('Fetch user name failed:', error);
      return userId;
    }
  };
  calculateProgress = (project) => {
    try {
      if (!project || !project.executionPeriod ||
          !Array.isArray(project.executionPeriod) ||
          project.executionPeriod.length !== 2) {
        return 0;
      }

      const startDate = moment(project.executionPeriod[0]);
      const endDate = moment(project.executionPeriod[1]);

      if (!startDate.isValid() || !endDate.isValid()) {
        return 0;
      }

      const now = moment();

      if (now.isBefore(startDate)) return 0;
      if (now.isAfter(endDate)) return 100;

      const total = endDate.diff(startDate, 'days');
      const elapsed = now.diff(startDate, 'days');

      if (total <= 0) return 0;

      return Math.round((elapsed / total) * 100);
    } catch (error) {
      console.warn('Calculate progress error:', error);
      return 0;
    }
  };

  render() {
    try {
      const { project } = this.props;
      if (!project) {
        return (
          <div style={{ padding: '20px', textAlign: 'center' }}>
            <p>项目信息不存在</p>
          </div>
        );
      }

      // const progress = this.calculateProgress(project);
      const grossMargin = project.planningBudget > 0
        ? ((project.projectProfit || 0) / project.planningBudget * 100).toFixed(1)
        : 0;

      return (
        <div style={{ padding: '20px' }}>
          {/* 基本信息 */}
          <Card title="基本信息" style={{ marginBottom: 24 }}>
            <Descriptions column={2} bordered>
              <Descriptions.Item label="项目名称" span={2}>
                {project.projectName}
              </Descriptions.Item>
              {/* <Descriptions.Item label="项目状态">
                <Badge
                  status={this.getProjectStatusColor(project.status)}
                  text={this.getProjectStatusText(project.status)}
                />
              </Descriptions.Item> */}
              <Descriptions.Item label="合同类型">
                <Tag color="blue">
                  {project.contractType ? getContractTypeLabel(project.contractType) : '-'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="合同签署状态">
                {project.contractSigningStatus ? (
                  <Tag color={getContractSigningStatusConfig(project.contractSigningStatus).color}>
                    {getContractSigningStatusConfig(project.contractSigningStatus).label}
                  </Tag>
                ) : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="所属品牌">
                {(project.brandName) || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="执行PM">
                {this.formatUserInfo(project.executivePM)}
              </Descriptions.Item>
              <Descriptions.Item label="内容媒介">
                {project.contentMedia && project.contentMedia.length > 0 && project.contentMedia.includes(', ')
                  ? project.contentMedia.split(',').map((user) => this.formatUserInfo(user.trim())).join(', ')
                  : this.formatUserInfo(project.contentMedia)
                }
              </Descriptions.Item>
              <Descriptions.Item label="执行周期" span={2}>
                {project.executionPeriod && project.executionPeriod.length === 2
                  ? `${this.formatDate(project.executionPeriod[0])} 至 ${this.formatDate(project.executionPeriod[1])}`
                  : '-'
              }
              </Descriptions.Item>
              <Descriptions.Item label="预计回款月份">
                {project.expectedPaymentMonth || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="账期">
                {project.paymentTermDays ? `T+${project.paymentTermDays}天` : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {this.formatDate(project.createTime)}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {this.formatDate(project.updateTime)}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* 财务信息 */}
          <Card title="财务信息" style={{ marginBottom: 24 }}>
            <Row gutter={[24, 24]}>
              <Col xs={24} sm={12} lg={6}>
                <Statistic
                  title="规划预算"
                  value={project.planningBudget || 0}
                  formatter={(value) => this.formatCurrency(value)}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>

              <Col xs={24} sm={12} lg={6}>
                <Statistic
                  title="预估达人返点"
                  value={project.estimatedTalentRebate}
                  formatter={(value) => this.formatCurrency(value)}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>

              <Col xs={24} sm={12} lg={6}>
                <Statistic
                  title="项目利润"
                  value={project.projectProfit || 0}
                  formatter={(value) => this.formatCurrency(value)}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col xs={24} sm={12} lg={6}>
                <Statistic
                  title="毛利率"
                  value={grossMargin}
                  suffix="%"
                  valueStyle={{ color: grossMargin >= 20 ? '#52c41a' : '#faad14' }}
                />
              </Col>
              {/* <Col xs={24} sm={12} lg={6}>
                <div>
                  <div style={{ marginBottom: 8, color: '#666' }}>项目进度</div>
                  <Progress
                    percent={progress}
                    status={progress === 100 ? 'success' : 'active'}
                    strokeColor={{
                      '0%': '#108ee9',
                      '100%': '#87d068',
                    }}
                  />
                </div>
              </Col> */}
            </Row>
          </Card>

          {/* 预算明细 */}
          {(project.talentBudget || project.adBudget || project.otherBudget) && (
          <Card title="预算明细" style={{ marginBottom: 24 }}>
            <Row gutter={[24, 16]}>
              {(
                <Col xs={24} sm={8}>
                  <Statistic
                    title="达人预算"
                    value={project.talentBudget}
                    formatter={(value) => this.formatCurrency(value)}
                    valueStyle={{ color: '#722ed1' }}
                  />
                </Col>
              )}
              {(
                <Col xs={24} sm={8}>
                  <Statistic
                    title="广告预算"
                    value={project.adBudget}
                    formatter={(value) => this.formatCurrency(value)}
                    valueStyle={{ color: '#fa8c16' }}
                  />
                </Col>
              )}
              {(
                <Col xs={24} sm={8}>
                  <Statistic
                    title="其他预算"
                    value={project.otherBudget}
                    formatter={(value) => this.formatCurrency(value)}
                    valueStyle={{ color: '#13c2c2' }}
                  />
                </Col>
              )}
            </Row>
          </Card>
          )}

          {/* 成本明细 */}
          {(project.talentCost || project.adCost || project.otherCost) && (
          <Card title="成本明细" style={{ marginBottom: 24 }}>
            <Row gutter={[24, 16]}>
              {(
                <Col xs={24} sm={8}>
                  <Statistic
                    title="达人成本"
                    value={project.talentCost}
                    formatter={(value) => this.formatCurrency(value)}
                    valueStyle={{ color: '#f5222d' }}
                  />
                </Col>
              )}
              { (
                <Col xs={24} sm={8}>
                  <Statistic
                    title="广告成本"
                    value={project.adCost}
                    formatter={(value) => this.formatCurrency(value)}
                    valueStyle={{ color: '#f5222d' }}
                  />
                </Col>
              )}
              {(
                <Col xs={24} sm={8}>
                  <Statistic
                    title="其他成本"
                    value={project.otherCost}
                    formatter={(value) => this.formatCurrency(value)}
                    valueStyle={{ color: '#f5222d' }}
                  />
                </Col>
              )}
            </Row>


          </Card>
          )}

          {/* 其他信息 */}
          {(project.settlementRules || project.kpi) && (
          <Card title="其他信息">
            <Descriptions column={1} bordered>
              {project.settlementRules && (
                <Descriptions.Item label="结算规则">
                  {project.settlementRules}
                </Descriptions.Item>
              )}
              {project.kpi && (
                <Descriptions.Item label="KPI指标">
                  {project.kpi}
                </Descriptions.Item>
              )}
            </Descriptions>
          </Card>
          )}
          {project.attachments && project.attachments.length > 0 && (
          <Card title="附件信息">
            <Row gutter={[24, 16]}>
              {project.attachments.map((file) => (
                <Col key={file.url}>
                  <a href={file.url} target="_blank" rel="noopener noreferrer">
                    {file.originalName}
                  </a>
                </Col>
              ))}
            </Row>
          </Card>
          )}
        </div>

      );
    } catch (error) {
      console.error('ProjectDetailModal render error:', error);
      return (
        <div style={{ padding: '20px', textAlign: 'center' }}>
          <p>项目详情加载失败，请稍后重试</p>
          <p style={{ color: '#999', fontSize: '12px' }}>错误信息：{error.message}</p>
        </div>
      );
    }
  }
}

export default ProjectDetailModal;
