# 项目周预算管理组件

这是一个完整的项目周预算管理系统组件集合，支持按周为单位创建和管理项目预算，包含预算表单、预算列表表格、批量创建和统计功能。已集成真实的后端API接口。

## 组件结构

```
ProjectManagement/
├── WeeklyBudgetManagement.js    # 主管理页面组件（整合所有功能）
├── WeeklyBudgetForm.js          # 周预算表单组件（带专业表单校验）
├── WeeklyBudgetTable.js         # 周预算列表表格组件
├── WeeklyBudgetBatchForm.js     # 批量创建周预算表单组件
├── WeeklyBudgetStats.js         # 周预算统计组件
└── WeeklyBudgetREADME.md       # 说明文档
```

## 支持文件

```
services/
└── api.js                      # API服务层，处理所有HTTP请求

utils/
└── weeklyBudgetUtils.js        # 周预算相关工具函数
```

## 功能特性

### 周预算表单 (WeeklyBudgetForm.js)
- ✅ 基本信息：预算标题、周期选择、服务类型
- ✅ 供应商选择：从启用的供应商中选择
- ✅ 服务内容：详细的服务内容描述
- ✅ 财务信息：合同金额、税率、已付金额
- ✅ 备注信息：额外的备注说明
- ✅ 表单验证和错误提示
- ✅ 支持新建和编辑模式
- ✅ 项目周期限制：只能在项目执行周期内创建预算

### 周预算表格 (WeeklyBudgetTable.js)
- ✅ 完整的周预算列表展示
- ✅ 筛选功能（服务类型、状态、年份）
- ✅ 新建、编辑、删除操作
- ✅ 批量删除功能
- ✅ 分页和排序
- ✅ 支付进度可视化
- ✅ 响应式设计
- ✅ 状态和服务类型标识

### 批量创建表单 (WeeklyBudgetBatchForm.js)
- ✅ 日期范围选择：批量创建指定时间范围内的周预算
- ✅ 默认参数设置：服务类型、合同金额、税率
- ✅ 预览功能：显示将要创建的周预算列表
- ✅ 项目周期限制：只能在项目执行周期内批量创建

### 周预算统计 (WeeklyBudgetStats.js)
- ✅ 预算总数统计
- ✅ 合同总金额、已付金额、剩余金额
- ✅ 整体支付进度可视化
- ✅ 按服务类型分组统计
- ✅ 按状态分组统计
- ✅ 按供应商分组统计
- ✅ 周趋势统计

## 使用方法

### 1. 在项目详情页面中使用
周预算管理已集成到 `ProjectDetail.js` 中作为一个标签页：

```jsx
<TabPane tab="周预算管理" key="weeklyBudget">
  <WeeklyBudgetManagement
    projectId={projectId}
    projectInfo={projectInfo}
  />
</TabPane>
```

### 2. 独立使用周预算管理
```jsx
import WeeklyBudgetManagement from './components/ProjectManagement/WeeklyBudgetManagement';

<WeeklyBudgetManagement
  projectId="project_id"
  projectInfo={projectInfo}
/>
```

### 3. 独立使用周预算表单
```jsx
import WeeklyBudgetForm from './components/ProjectManagement/WeeklyBudgetForm';

<WeeklyBudgetForm
  projectId="project_id"
  projectInfo={projectInfo}
  weeklyBudget={budgetData} // 编辑时传入预算数据，新建时不传
  onSubmit={(data) => {
    console.log('周预算数据:', data);
  }}
  onCancel={() => {
    console.log('取消操作');
  }}
/>
```

### 4. 独立使用周预算表格
```jsx
import WeeklyBudgetTable from './components/ProjectManagement/WeeklyBudgetTable';

<WeeklyBudgetTable
  projectId="project_id"
  onView={(budget) => {
    console.log('查看周预算:', budget);
  }}
  onEdit={(budget) => {
    console.log('编辑周预算:', budget);
  }}
/>
```

## 数据结构

### 周预算数据结构
```javascript
{
  id: "weekly_budget_id",
  title: "第1周达人投放预算",
  weekStartDate: "2024-01-01",
  weekEndDate: "2024-01-07",
  weekNumber: 1,
  year: 2024,
  serviceType: "influencer",
  serviceContent: "小红书达人投放，包含图文和视频内容",
  remarks: "重点关注美妆类达人",
  contractAmount: 50000,
  taxRate: "special_6",
  paidAmount: 25000,
  remainingAmount: 25000,
  status: "approved",
  projectId: "project_id",
  supplierId: "supplier_id",
  supplier: {
    id: "supplier_id",
    name: "供应商名称",
    shortName: "简称"
  },
  createdAt: "2024-01-01T00:00:00.000Z",
  updatedAt: "2024-01-01T00:00:00.000Z"
}
```

## 常量定义

### 服务类型
- `influencer` - 达人服务
- `advertising` - 投流服务
- `other` - 其他服务

### 税率选项
- `special_1` - 小规模纳税人1%
- `special_3` - 小规模纳税人3%
- `special_6` - 小规模纳税人6%
- `general_6` - 一般纳税人6%
- `general_9` - 一般纳税人9%
- `general_13` - 一般纳税人13%
- `ordinary` - 普票

### 周预算状态
- `draft` - 草稿
- `approved` - 已审批
- `executing` - 执行中
- `completed` - 已完成
- `cancelled` - 已取消

## 技术要求

- React 16.12.0+
- Ant Design 3.26.8
- moment.js（用于时间处理）

## 兼容性说明

本组件专门为Ant Design 3.x版本设计，已处理以下兼容性问题：
- 使用了3.x版本支持的API
- 表单验证使用Form.create()方式
- 日期选择器使用moment.js

## API集成

组件已完全集成后端API，支持以下功能：

### 周预算管理API
- `GET /weekly-budgets` - 获取周预算列表
- `GET /weekly-budgets/{id}` - 获取单个周预算
- `POST /projects/{projectId}/weekly-budgets` - 创建项目周预算
- `POST /projects/{projectId}/weekly-budgets/batch` - 批量创建项目周预算
- `PUT /weekly-budgets/{id}` - 更新周预算
- `DELETE /weekly-budgets/{id}` - 删除周预算
- `GET /weekly-budgets/stats` - 获取周预算统计
- `DELETE /weekly-budgets/batch-delete` - 批量删除周预算
- `PUT /weekly-budgets/batch-status` - 批量更新周预算状态

### API配置
API基础URL在 `src/config.js` 中配置，默认为 `${host}/api`

## 数据转换

组件会自动处理前端表单数据与API数据格式之间的转换，确保数据的正确传输和显示。

## 错误处理

- 网络请求失败时会显示错误提示
- 表单验证失败时会显示具体的错误信息
- 支持Token过期自动刷新机制

## 性能优化

- 使用分页加载减少数据传输量
- 实现了防抖搜索避免频繁请求
- 组件懒加载提升页面加载速度
- 智能的表单数据缓存

## 特色功能

### 1. 智能预算标题生成
根据选择的周数和服务类型自动生成预算标题

### 2. 项目周期约束
只能在项目执行周期内创建周预算，避免数据错误

### 3. 支付进度可视化
使用进度条直观显示每个预算的支付进度

### 4. 批量创建功能
支持一次性创建多个连续周的预算，提高工作效率

### 5. 供应商集成
与供应商管理模块无缝集成，支持选择供应商

### 6. 统计分析
提供多维度的统计分析，帮助项目管理决策
