import React, { Component } from 'react';
// import { Card, Row, Col, Icon, Statistic } from 'antd';
import { Row, Icon } from 'antd';
import ProjectTable from './ProjectTable';
// import ProjectForm from './ProjectForm';
// import ProjectDetail from './ProjectDetail';
// import BrandManagement from './BrandManagement';
// import WeeklyBudgetForm from './WeeklyBudgetForm';
// import SupplierManagement from '../SupplierManagement/SupplierManagement';
import './ProjectManagement.css';

// const { TabPane } = Tabs;

class ProjectManagement extends Component {
  getInitialTab = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const tab = urlParams.get('tab');
    return tab || 'list';
  };

  handleTabChange = (key) => {
    // this.setState({ activeTab: key });

    // 更新URL参数
    const url = new URL(window.location);
    if (key === 'list') {
      url.searchParams.delete('tab');
    } else {
      url.searchParams.set('tab', key);
    }
    window.history.pushState({}, '', url);
  };

  render() {
    return (
      <div className="modern-project-management">
        {/* 页面头部 */}
        <div className="page-header">
          <div className="header-content">
            <div className="header-left">
              <h1 className="page-title">
                <Icon type="project" className="title-icon" />
                项目管理
              </h1>
              <p className="page-description">
                管理您的项目、品牌和供应商信息
              </p>
            </div>
            <div className="header-actions">
              {/* <Button
                type="primary"
                size="large"
                icon="plus"
                className="action-button"
              >
                新建项目
              </Button> */}
            </div>
          </div>
        </div>

        {/* 快速统计 */}
        {/* <Row gutter={[24, 24]} className="stats-section">
          <Col xs={24} sm={12} lg={6}>
            <Card className="stat-card">
              <Statistic
                title="项目总数"
                value={24}
                prefix={<Icon type="project" />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className="stat-card">
              <Statistic
                title="进行中项目"
                value={8}
                prefix={<Icon type="clock-circle" />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className="stat-card">
              <Statistic
                title="品牌数量"
                value={15}
                prefix={<Icon type="tags" />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className="stat-card">
              <Statistic
                title="供应商数量"
                value={32}
                prefix={<Icon type="shop" />}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
        </Row> */}

        <Row>
          <ProjectTable />
        </Row>


      </div>
    );
  }
}

export default ProjectManagement;
