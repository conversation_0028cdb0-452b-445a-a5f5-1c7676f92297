import React, { Component } from 'react';
import { Card, Button, Select, message, Row, Col } from 'antd';
import moment from 'moment';
import { SERVICE_TYPES, getMonthWeekInfo } from '../../utils/weeklyBudgetUtils';
import CustomWeekPicker from './CustomWeekPicker';

const { Option } = Select;

class TitleGenerationTest extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedWeek: null,
      selectedServiceType: null,
      generatedTitle: '',
    };
  }

  // 模拟项目信息
  mockProjectInfo = {
    projectName: '测试项目-标题生成',
    executionPeriod: ['2024-01-01', '2024-12-31'],
  };

  // 获取服务类型标签
  getServiceTypeLabel = (serviceType) => {
    const type = SERVICE_TYPES.find((t) => t.value === serviceType);
    return type ? type.label : serviceType;
  };

  // 生成预算标题
  generateBudgetTitle = (weekRange, serviceType) => {
    console.log('=== 开始生成标题 ===');
    console.log('输入参数:', { weekRange, serviceType });

    if (!weekRange || !serviceType) {
      console.log('缺少必要参数，返回空字符串');
      return '';
    }

    const weekStartDate = weekRange.clone().startOf('week');
    console.log('周开始日期:', weekStartDate.format('YYYY-MM-DD'));

    const monthWeekInfo = getMonthWeekInfo(weekStartDate);
    console.log('月份周数信息:', monthWeekInfo);

    const { projectName } = this.mockProjectInfo;
    const serviceLabel = this.getServiceTypeLabel(serviceType);

    const title = `${projectName}-${monthWeekInfo.monthName}第${monthWeekInfo.weekOfMonth}周${serviceLabel}预算`;
    console.log('生成的标题:', title);
    console.log('=== 标题生成完成 ===');

    return title;
  };

  handleWeekChange = (week) => {
    console.log('周期选择变化:', week);
    this.setState((prevState) => {
      const updates = { selectedWeek: week };
      // 如果已经选择了服务类型，立即生成标题
      if (week && prevState.selectedServiceType) {
        const title = this.generateBudgetTitle(week, prevState.selectedServiceType);
        updates.generatedTitle = title;
      }
      return updates;
    });
  };

  handleServiceTypeChange = (serviceType) => {
    console.log('服务类型选择变化:', serviceType);
    this.setState((prevState) => {
      const updates = { selectedServiceType: serviceType };
      // 如果已经选择了周期，立即生成标题
      if (prevState.selectedWeek && serviceType) {
        const title = this.generateBudgetTitle(prevState.selectedWeek, serviceType);
        updates.generatedTitle = title;
      }
      return updates;
    });
  };

  handleManualGenerate = () => {
    const { selectedWeek, selectedServiceType } = this.state;

    if (!selectedWeek) {
      message.warning('请先选择周期');
      return;
    }

    if (!selectedServiceType) {
      message.warning('请先选择服务类型');
      return;
    }

    const title = this.generateBudgetTitle(selectedWeek, selectedServiceType);
    this.setState({ generatedTitle: title });
    message.success('标题生成成功！');
  };

  handleReset = () => {
    this.setState({
      selectedWeek: null,
      selectedServiceType: null,
      generatedTitle: '',
    });
    message.info('已重置所有选择');
  };

  render() {
    const { selectedWeek, selectedServiceType, generatedTitle } = this.state;

    return (
      <div style={{ padding: '20px' }}>
        <Card title="标题自动生成测试" style={{ marginBottom: 16 }}>
          <p>这个测试页面专门用于验证预算标题的自动生成功能。</p>

          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col span={8}>
              <h4>1. 选择周期</h4>
              <CustomWeekPicker
                style={{ width: '100%' }}
                placeholder="请选择周期"
                value={selectedWeek}
                onChange={this.handleWeekChange}
                projectStartDate={moment('2024-01-01')}
                projectEndDate={moment('2024-12-31')}
              />
            </Col>

            <Col span={8}>
              <h4>2. 选择服务类型</h4>
              <Select
                style={{ width: '100%' }}
                placeholder="请选择服务类型"
                value={selectedServiceType}
                onChange={this.handleServiceTypeChange}
              >
                {SERVICE_TYPES.map((type) => (
                  <Option key={type.value} value={type.value}>
                    {type.label}
                  </Option>
                ))}
              </Select>
            </Col>

            <Col span={8}>
              <h4>3. 操作</h4>
              <Button
                type="primary"
                onClick={this.handleManualGenerate}
                style={{ marginRight: 8 }}
              >
                手动生成标题
              </Button>
              <Button onClick={this.handleReset}>
                重置
              </Button>
            </Col>
          </Row>

          <Card title="生成结果" size="small" style={{ backgroundColor: '#f5f5f5' }}>
            <Row gutter={16}>
              <Col span={12}>
                <p><strong>选择的周期：</strong></p>
                <div style={{ padding: '8px', backgroundColor: '#fff', borderRadius: '4px', minHeight: '32px' }}>
                  {selectedWeek ? (
                    <span style={{ color: '#1890ff' }}>
                      {selectedWeek.format('YYYY-MM-DD')} (周一)
                    </span>
                  ) : (
                    <span style={{ color: '#999' }}>未选择</span>
                  )}
                </div>
              </Col>

              <Col span={12}>
                <p><strong>选择的服务类型：</strong></p>
                <div style={{ padding: '8px', backgroundColor: '#fff', borderRadius: '4px', minHeight: '32px' }}>
                  {selectedServiceType ? (
                    <span style={{ color: '#52c41a' }}>
                      {this.getServiceTypeLabel(selectedServiceType)}
                    </span>
                  ) : (
                    <span style={{ color: '#999' }}>未选择</span>
                  )}
                </div>
              </Col>
            </Row>

            <div style={{ marginTop: 16 }}>
              <p><strong>生成的标题：</strong></p>
              <div style={{
                padding: '12px',
                backgroundColor: generatedTitle ? '#e6f7ff' : '#fff',
                borderRadius: '4px',
                border: generatedTitle ? '1px solid #91d5ff' : '1px solid #d9d9d9',
                minHeight: '40px',
                fontSize: '14px',
                fontWeight: generatedTitle ? 'bold' : 'normal',
              }}
              >
                {generatedTitle || (
                  <span style={{ color: '#999' }}>
                    请先选择周期和服务类型，标题将自动生成
                  </span>
                )}
              </div>
            </div>
          </Card>

          <div style={{ marginTop: 16, padding: '12px', backgroundColor: '#fff2e8', borderRadius: '6px', border: '1px solid #ffbb96' }}>
            <p style={{ margin: 0, color: '#d4380d' }}>
              <strong>🔍 调试提示：</strong>
              打开浏览器开发者工具的控制台(Console)，可以看到详细的标题生成过程和调试信息。
            </p>
          </div>
        </Card>
      </div>
    );
  }
}

export default TitleGenerationTest;
