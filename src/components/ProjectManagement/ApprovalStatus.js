import React, { Component } from 'react';
import {
  Modal,
  Card,
  Row,
  Col,
  Tag,
  Button,
  Steps,
  Timeline,
  Spin,
  message,
  Descriptions,
  // Divider,
} from 'antd';
import { approvalAPI } from '../../services/api';
import { formatCurrency, formatDate } from '../../utils/weeklyBudgetUtils';

const { Step } = Steps;

// 审批状态配置
const APPROVAL_STATUS_CONFIG = {
  PENDING: { label: '审批中', color: 'processing', status: 'process' },
  APPROVED: { label: '已通过', color: 'success', status: 'finish' },
  REJECTED: { label: '已拒绝', color: 'error', status: 'error' },
  CANCELLED: { label: '已取消', color: 'default', status: 'error' },
};

// 审批结果配置
const APPROVAL_RESULT_CONFIG = {
  agree: { label: '同意', color: 'success' },
  refuse: { label: '拒绝', color: 'error' },
  redirect: { label: '转交', color: 'warning' },
};

class ApprovalStatus extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      approval: null,
      syncing: false,
    };
  }

  componentDidMount() {
    if (this.props.approvalId) {
      this.loadApprovalDetails();
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.approvalId !== this.props.approvalId && this.props.approvalId) {
      this.loadApprovalDetails();
    }
  }

  loadApprovalDetails = async () => {
    const { approvalId } = this.props;
    if (!approvalId) return;

    this.setState({ loading: true });
    try {
      const response = await approvalAPI.getApproval(approvalId);
      console.log('[ response ] >', response);
      if (response.success) {
        this.setState({ approval: response.data });
      } else {
        message.error(response.message || '获取审批详情失败');
      }
    } catch (error) {
      console.error('Load approval details failed:', error);
      message.error('获取审批详情失败');
    } finally {
      this.setState({ loading: false });
    }
  };

  computedTime = (startTime, endTime) => {
    const start = new Date(startTime);
    const end = new Date(endTime);
    const diff = end - start;
    const minutes = Math.floor(diff / 1000 / 60);
    return minutes;
  };

  handleSyncStatus = async () => {
    const { approval } = this.state;
    if (!approval || !approval.processInstanceId) return;

    this.setState({ syncing: true });
    try {
      const response = await approvalAPI.syncApprovalStatus(approval.processInstanceId);
      if (response.success) {
        message.success('同步审批状态成功');
        this.setState({ approval: { ...approval, ...response.data } });
      } else {
        message.error(response.message || '同步审批状态失败');
      }
    } catch (error) {
      console.error('Sync approval status failed:', error);
      message.error('同步审批状态失败');
    } finally {
      this.setState({ syncing: false });
    }
  };

  getApprovalStatusConfig = (status) => {
    return APPROVAL_STATUS_CONFIG[status] || APPROVAL_STATUS_CONFIG.PENDING;
  };

  getApprovalResultConfig = (result) => {
    return APPROVAL_RESULT_CONFIG[result] || { label: result, color: 'default' };
  };

  renderApprovalSteps = (approval) => {
    const statusConfig = this.getApprovalStatusConfig(approval.status);
    const steps = [
      {
        title: '发起审批',
        description: `${formatDate(approval.createTime, 'YYYY-MM-DD HH:mm')}`,
        status: 'finish',
      },
      {
        title: '审批中',
        description: approval.status === 'pending' ? '等待审批' : `共耗时: ${this.computedTime(approval.createTime, approval.finishTime)}分钟`,
        status: approval.status === 'pending' ? 'process' : statusConfig.status,
      },
    ];

    if (approval.finishTime) {
      steps.push({
        title: '审批完成',
        description: `${formatDate(approval.finishTime, 'YYYY-MM-DD HH:mm')}`,
        status: statusConfig.status,
      });
    }

    return (
      <Steps current={approval.status === 'pending' ? 1 : 2} status={statusConfig.status}>
        {steps.map((step) => (
          <Step key={step.id} title={step.title} description={step.description} />
        ))}
      </Steps>
    );
  };

  renderApprovalTimeline = (approval) => {
    const items = [
      {
        color: 'blue',
        children: (
          <div>
            <p><strong>发起审批</strong></p>
            <p>时间: {formatDate(approval.createTime, 'YYYY-MM-DD HH:mm')}</p>
            <p>发起人: {approval.originatorUserId}</p>
          </div>
        ),
      },
    ];

    if (approval.finishTime) {
      const resultConfig = this.getApprovalResultConfig(approval.result);
      items.push({
        color: resultConfig.color === 'success' ? 'green' : 'red',
        children: (
          <div>
            <p><strong>审批完成</strong></p>
            <p>时间: {formatDate(approval.finishTime, 'YYYY-MM-DD HH:mm')}</p>
            <p>结果: <Tag color={resultConfig.color}>{resultConfig.label}</Tag></p>
          </div>
        ),
      });
    }

    return <Timeline items={items} />;
  };

  render() {
    const { visible, onCancel } = this.props;
    const { loading, approval, syncing } = this.state;

    return (
      <Modal
        title="审批状态跟踪"
        visible={visible}
        onCancel={onCancel}
        footer={[
          <Button key="sync" loading={syncing} onClick={this.handleSyncStatus}>
            同步状态
          </Button>,
          <Button key="close" type="primary" onClick={onCancel}>
            关闭
          </Button>,
        ]}
        width={800}
        destroyOnClose
      >
        <Spin spinning={loading}>
          {approval ? (
            <div>
              {/* 基本信息 */}
              <Card size="small" style={{ marginBottom: 16 }}>
                <Descriptions title="审批基本信息" column={2} size="small">
                  <Descriptions.Item label="审批标题">
                    {approval.title}
                  </Descriptions.Item>
                  <Descriptions.Item label="审批状态">
                    <Tag color={this.getApprovalStatusConfig(approval.status).color}>
                      {this.getApprovalStatusConfig(approval.status).label}
                    </Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="审批金额">
                    {formatCurrency(approval.approvalAmount)}
                  </Descriptions.Item>
                  <Descriptions.Item label="流程实例ID">
                    {approval.processInstanceId}
                  </Descriptions.Item>
                  <Descriptions.Item label="流程代码">
                    {approval.processCode}
                  </Descriptions.Item>
                  <Descriptions.Item label="发起时间">
                    {formatDate(approval.createTime, 'YYYY-MM-DD HH:mm')}
                  </Descriptions.Item>
                  {approval.finishTime && (
                    <Descriptions.Item label="完成时间">
                      {formatDate(approval.finishTime, 'YYYY-MM-DD HH:mm')}
                    </Descriptions.Item>
                  )}
                  {approval.result && (
                    <Descriptions.Item label="审批结果">
                      <Tag color={this.getApprovalResultConfig(approval.result).color}>
                        {this.getApprovalResultConfig(approval.result).label}
                      </Tag>
                    </Descriptions.Item>
                  )}
                </Descriptions>
              </Card>

              {/* 关联周预算信息 */}
              {approval.weeklyBudget && (
                <Card size="small" style={{ marginBottom: 16 }}>
                  <h4>关联周预算信息</h4>
                  <Row gutter={16}>
                    <Col span={12}>
                      <p><strong>预算标题：</strong>{approval.weeklyBudget.title}</p>
                      <p><strong>项目名称：</strong>{approval.weeklyBudget.project?.name}</p>
                    </Col>
                    <Col span={12}>
                      <p><strong>预算ID：</strong>{approval.weeklyBudget.id}</p>
                      <p><strong>项目ID：</strong>{approval.weeklyBudget.project?.id}</p>
                    </Col>
                  </Row>
                </Card>
              )}

              {/* 审批进度 */}
              <Card size="small" style={{ marginBottom: 16 }}>
                <h4>审批进度</h4>
                {this.renderApprovalSteps(approval)}
              </Card>

              {/* 审批时间线 */}
              <Card size="small" style={{ marginBottom: 16 }}>
                <h4>审批时间线</h4>
                {this.renderApprovalTimeline(approval)}
              </Card>

              {/* 付款事由和备注 */}
              {(approval.reason || approval.remark) && (
                <Card size="small">
                  <h4>详细信息</h4>
                  {approval.reason && (
                    <div style={{ marginBottom: 12 }}>
                      <strong>付款事由：</strong>
                      <p style={{ marginTop: 4, marginBottom: 0 }}>{approval.reason}</p>
                    </div>
                  )}
                  {approval.remark && (
                    <div>
                      <strong>备注信息：</strong>
                      <p style={{ marginTop: 4, marginBottom: 0 }}>{approval.remark}</p>
                    </div>
                  )}
                </Card>
              )}
            </div>
          ) : (
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <p>暂无审批信息</p>
            </div>
          )}
        </Spin>
      </Modal>
    );
  }
}

export default ApprovalStatus;
