import React, { Component } from 'react';
import {
  Modal,
  Form,
  Select,
  InputNumber,
  DatePicker,
  message,
  Row,
  Col,
  Alert,
} from 'antd';
import moment from 'moment';
import { weeklyBudgetAPI } from '../../services/api';
import {
  SERVICE_TYPES,
  TAX_RATES,
  generateProjectWeeks,
} from '../../utils/weeklyBudgetUtils';

const { Option } = Select;
const { RangePicker } = DatePicker;

// 表单校验规则
const formRules = {
  dateRange: [
    { required: true, message: '请选择批量创建的日期范围' },
  ],
  serviceType: [
    { required: true, message: '请选择服务类型' },
  ],
  defaultContractAmount: [
    { required: true, message: '请输入默认合同金额' },
    { type: 'number', min: 0.01, message: '合同金额必须大于0' },
  ],
};

class WeeklyBudgetBatchForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      previewWeeks: [],
    };
  }

  handleDateRangeChange = (dates) => {
    if (dates && dates.length === 2) {
      const [startDate, endDate] = dates;
      const weeks = generateProjectWeeks(
        startDate.format('YYYY-MM-DD'),
        endDate.format('YYYY-MM-DD'),
      );
      this.setState({ previewWeeks: weeks });
    } else {
      this.setState({ previewWeeks: [] });
    }
  };

  handleOk = () => {
    this.props.form.validateFields(async (err, values) => {
      if (err) {
        message.error('请检查表单填写是否正确');
        return;
      }

      this.setState({ loading: true });

      try {
        const [startDate, endDate] = values.dateRange;

        const batchData = {
          startDate: startDate.format('YYYY-MM-DD'),
          endDate: endDate.format('YYYY-MM-DD'),
          serviceType: values.serviceType,
          defaultContractAmount: values.defaultContractAmount,
          defaultTaxRate: values.defaultTaxRate,
        };

        const response = await weeklyBudgetAPI.batchCreateWeeklyBudgets(
          this.props.projectId,
          batchData,
        );

        if (response.success) {
          message.success(`成功创建 ${response.data.length} 个周预算`);
          this.props.onSubmit && this.props.onSubmit(response.data);
        } else {
          message.error(response.message || '批量创建失败');
        }
      } catch (error) {
        console.error('Batch create weekly budgets failed:', error);
        message.error('批量创建失败');
      } finally {
        this.setState({ loading: false });
      }
    });
  };

  handleCancel = () => {
    this.props.onCancel && this.props.onCancel();
  };

  render() {
    const { visible, projectInfo } = this.props;
    const { loading, previewWeeks } = this.state;
    const { getFieldDecorator } = this.props.form;

    // 项目执行周期限制
    const projectStartDate = projectInfo?.period?.startDate
      ? moment(projectInfo.period.startDate)
      : null;
    const projectEndDate = projectInfo?.period?.endDate
      ? moment(projectInfo.period.endDate)
      : null;

    return (
      <Modal
        title="批量创建周预算"
        visible={visible}
        onOk={this.handleOk}
        onCancel={this.handleCancel}
        confirmLoading={loading}
        width={600}
        destroyOnClose
      >
        <Form layout="vertical">
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="批量创建日期范围">
                {getFieldDecorator('dateRange', {
                  rules: formRules.dateRange,
                })(
                  <RangePicker
                    style={{ width: '100%' }}
                    placeholder={['开始日期', '结束日期']}
                    onChange={this.handleDateRangeChange}
                    disabledDate={(current) => {
                      if (projectStartDate && current.isBefore(projectStartDate, 'day')) {
                        return true;
                      }
                      if (projectEndDate && current.isAfter(projectEndDate, 'day')) {
                        return true;
                      }
                      return false;
                    }}
                  />,
                )}
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="服务类型">
                {getFieldDecorator('serviceType', {
                  rules: formRules.serviceType,
                })(
                  <Select placeholder="请选择服务类型">
                    {SERVICE_TYPES.map((type) => (
                      <Option key={type.value} value={type.value}>
                        {type.label}
                      </Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="默认税率">
                {getFieldDecorator('defaultTaxRate')(
                  <Select placeholder="请选择默认税率">
                    {TAX_RATES.map((rate) => (
                      <Option key={rate.value} value={rate.value}>
                        {rate.label}
                      </Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="默认合同金额">
                {getFieldDecorator('defaultContractAmount', {
                  rules: formRules.defaultContractAmount,
                })(
                  <InputNumber
                    placeholder="请输入默认合同金额"
                    style={{ width: '100%' }}
                    min={0}
                    formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                  />,
                )}
              </Form.Item>
            </Col>
          </Row>

          {/* 预览信息 */}
          {previewWeeks.length > 0 && (
            <div style={{ marginTop: 16 }}>
              <Alert
                message="预览信息"
                description={
                  <div>
                    <p>将创建 <strong>{previewWeeks.length}</strong> 个周预算：</p>
                    <div style={{ maxHeight: 200, overflowY: 'auto' }}>
                      {previewWeeks.map((week) => (
                        <div key={week.id} style={{ marginBottom: 4 }}>
                          第{week.weekNumber}周: {week.weekStartDate} 至 {week.weekEndDate}
                        </div>
                      ))}
                    </div>
                  </div>
                }
                type="info"
                showIcon
              />
            </div>
          )}

          {projectInfo && (
            <div style={{ marginTop: 16 }}>
              <Alert
                message="项目信息"
                description={
                  <div>
                    <p><strong>项目名称：</strong>{projectInfo.projectName}</p>
                    {projectInfo.executionPeriod && (
                      <p>
                        <strong>项目周期：</strong>
                        {projectInfo.executionPeriod[0]} 至 {projectInfo.executionPeriod[1]}
                      </p>
                    )}
                  </div>
                }
                type="info"
                showIcon
              />
            </div>
          )}
        </Form>
      </Modal>
    );
  }
}

export default Form.create()(WeeklyBudgetBatchForm);
