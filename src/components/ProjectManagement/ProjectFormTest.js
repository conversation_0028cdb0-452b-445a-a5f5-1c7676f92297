import React, { Component } from 'react';
import { Card, Button, message } from 'antd';
import ProjectForm from './ProjectForm';

class ProjectFormTest extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showForm: true,
      initialData: null,
    };
  }

  handleSubmit = (data) => {
    console.log('Form submitted:', data);
    message.success('表单提交成功！');
  };

  handleLoadTestData = () => {
    const testData = {
      documentType: 'project_initiation',
      brand: 1,
      projectName: '测试项目 - 春季营销推广',
      executionPeriod: ['2024-03-01', '2024-05-31'],
      executivePM: 'user001',
      contractType: 'quarterly',
      contractSigningStatus: 'signed',
      contentMedia: ['user003', 'user006'],
      // planningBudget: 1000000, // 移除总预算，让它自动计算
      talentBudget: 300000,
      adBudget: 400000,
      otherBudget: 300000, // 总计：1,000,000
      talentCost: 280000,
      adCost: 380000,
      otherCost: 45000,
      talentRebateIncome: 30000,
      settlementRules: '按季度结算，每月预付70%，季度末结清余款',
      kpi: '目标曝光量：1000万\n目标转化率：3%\n目标ROI：1:4',
      expectedPaymentMonth: '2024-06',
      paymentTermDays: 180,
    };

    this.setState({ initialData: testData });
    message.info('已加载测试数据 - 总预算将自动计算为 ¥1,000,000');
  };

  handleClearData = () => {
    this.setState({ initialData: null });
    message.info('已清空测试数据');
  };

  render() {
    const { showForm, initialData } = this.state;

    return (
      <div style={{ padding: '20px' }}>
        <Card title="项目表单重构测试页面" style={{ marginBottom: 20 }}>
          <div style={{ marginBottom: 16 }}>
            <Button
              type="primary"
              onClick={this.handleLoadTestData}
              style={{ marginRight: 8 }}
            >
              加载测试数据
            </Button>
            <Button
              onClick={this.handleClearData}
              style={{ marginRight: 8 }}
            >
              清空数据
            </Button>
            <Button
              type={showForm ? 'danger' : 'default'}
              onClick={() => this.setState({ showForm: !showForm })}
            >
              {showForm ? '隐藏表单' : '显示表单'}
            </Button>
          </div>

          <div style={{ fontSize: '14px', color: '#666', marginBottom: 16 }}>
            <h4>重构改进点：</h4>
            <ul>
              <li>✅ 将达人返点从成本信息移到收入信息区域</li>
              <li>✅ 重新组织预算规划：项目总预算 = 达人预算 + 投流预算 + 其他预算</li>
              <li>✅ <strong>项目总预算自动计算</strong>：输入分项预算时自动更新总预算</li>
              <li>✅ 简化界面布局，使用更紧凑的卡片设计</li>
              <li>✅ 优化字段分组和标签，提高用户体验</li>
              <li>✅ 移除预算分配验证，避免不匹配问题</li>
              <li>✅ 利润分析区域增加计算公式说明</li>
            </ul>
            <div style={{ marginTop: 12, padding: '8px 12px', backgroundColor: '#f0f9ff', border: '1px solid #bae6fd', borderRadius: '4px' }}>
              <strong>💡 使用提示：</strong>
              <br />• 项目总预算现在是只读字段，会根据分项预算自动计算
              <br />• 修改达人预算、投流预算或其他预算时，总预算会实时更新
              <br />• 这样可以确保预算分配始终准确，避免手动输入错误
            </div>
          </div>
        </Card>

        {showForm && (
          <ProjectForm
            initialData={initialData}
            onSubmit={this.handleSubmit}
          />
        )}
      </div>
    );
  }
}

export default ProjectFormTest;
