import React, { Component } from 'react';
import { Tabs, Card, Button, message } from 'antd';
import WeeklyBudgetTable from './WeeklyBudgetTable';
import BudgetForm from './BudgetForm';
import WeeklyBudgetMultiForm from './WeeklyBudgetMultiForm';
import WeeklyBudgetStats from './WeeklyBudgetStats';
import WeeklyBudgetBatchForm from './WeeklyBudgetBatchForm';
import FundingPlanModal from './FundingPlanModal';

const { TabPane } = Tabs;

class WeeklyBudgetManagement extends Component {
  constructor(props) {
    super(props);
    this.state = {
      activeTab: 'list',
      selectedBudgetId: null,
      selectedBudget: null,
      showBatchForm: false,
      formType: 'single', // 'single' 或 'multi'
      showFundingPlan: false,
    };
  }

  handleTabChange = (key) => {
    this.setState({ activeTab: key });
  };

  handleViewBudget = (budget) => {
    this.setState({
      selectedBudgetId: budget.id,
      selectedBudget: budget,
      activeTab: 'detail',
    });
  };

  handleEditBudget = (budget) => {
    this.setState({
      selectedBudgetId: budget.id,
      selectedBudget: budget,
      activeTab: 'form',
    });
  };
  handleViewFundingPlan = (budget) => {
    this.setState({
      selectedBudgetId: budget.id,
      selectedBudget: budget,
      showFundingPlan: true,
    });
  };

  handleCreateBudget = () => {
    this.setState({
      selectedBudgetId: null,
      selectedBudget: null,
      activeTab: 'form',
      formType: 'single',
    });
  };

  handleCreateMultiBudget = () => {
    this.setState({
      selectedBudgetId: null,
      selectedBudget: null,
      activeTab: 'form',
      formType: 'multi',
    });
  };

  handleBatchCreate = () => {
    this.setState({
      showBatchForm: true,
    });
  };

  handleFormSubmit = (data) => {
    console.log('周预算数据:', data);

    // 判断是单个预算还是多个预算
    if (Array.isArray(data)) {
      message.success(`成功创建 ${data.length} 个周预算`);
    } else {
      // message.success('周预算保存成功');
    }

    // 表单提交后切换到列表页
    this.setState({
      activeTab: 'list',
      selectedBudgetId: null,
      selectedBudget: null,
      formType: 'single',
    });
    // 刷新表格数据
    if (this.tableRef) {
      this.tableRef.loadWeeklyBudgets();
    }
  };

  handleFormCancel = () => {
    this.setState({
      activeTab: 'list',
      selectedBudgetId: null,
      selectedBudget: null,
      formType: 'single',
    });
  };

  handleBatchFormSubmit = (data) => {
    console.log('批量创建周预算数据:', data);
    message.success('批量创建周预算成功');
    this.setState({ showBatchForm: false });
    // 刷新表格数据
    if (this.tableRef) {
      this.tableRef.loadWeeklyBudgets();
    }
  };

  handleBatchFormCancel = () => {
    this.setState({ showBatchForm: false });
  };

  render() {
    const {
      activeTab,
      selectedBudgetId,
      selectedBudget,
      showBatchForm,
      formType,
      showFundingPlan,
    } = this.state;
    const { projectId, projectInfo } = this.props;

    return (
      <div style={{ padding: '20px' }}>
        <Card
          title="周预算管理"
          extra={
            <div>
              <Button
                type="primary"
                onClick={this.handleCreateBudget}
                style={{ marginRight: 8 }}
              >
                新建周预算
              </Button>
              <Button
                onClick={this.handleCreateMultiBudget}
                style={{ marginRight: 8 }}
              >
                多项预算创建
              </Button>
              <Button
                onClick={this.handleBatchCreate}
              >
                批量创建
              </Button>
            </div>
          }
        >
          <Tabs activeKey={activeTab} onChange={this.handleTabChange}>
            <TabPane tab="预算列表" key="list">
              <WeeklyBudgetTable
                ref={(ref) => { this.tableRef = ref; }}
                projectId={projectId}
                onView={this.handleViewBudget}
                onEdit={this.handleEditBudget}
                onViewFundingPlan={this.handleViewFundingPlan}
              />
            </TabPane>

            <TabPane tab={formType === 'multi' ? '多项预算创建' : '新建预算'} key="form">
              {formType === 'multi' ? (
                <WeeklyBudgetMultiForm
                  projectId={projectId}
                  projectInfo={projectInfo}
                  onSubmit={this.handleFormSubmit}
                  onCancel={this.handleFormCancel}
                />
              ) : (
                <BudgetForm
                  projectId={projectId}
                  projectInfo={projectInfo}
                  budget={selectedBudget}
                  onSubmit={this.handleFormSubmit}
                  onCancel={this.handleFormCancel}
                />
              )}
            </TabPane>

            {selectedBudgetId && (
              <TabPane
                tab={`预算详情${selectedBudget ? ` - ${selectedBudget.title}` : ''}`}
                key="detail"
              >
                <Card title="预算详细信息">
                  {selectedBudget && (
                    <div>
                      <p><strong>预算标题：</strong>{selectedBudget.title}</p>
                      <p><strong>服务类型：</strong>{selectedBudget.serviceType}</p>
                      <p><strong>服务内容：</strong>{selectedBudget.serviceContent || '-'}</p>
                      <p><strong>合同金额：</strong>
                        ¥{selectedBudget.contractAmount?.toLocaleString()}
                      </p>
                      <p><strong>已付金额：</strong>
                        ¥{selectedBudget.paidAmount?.toLocaleString() || 0}
                      </p>
                      <p>
                        <strong>剩余金额：</strong>
                        ¥{((selectedBudget.contractAmount || 0)
                          - (selectedBudget.paidAmount || 0)).toLocaleString()}
                      </p>
                      <p><strong>税率：</strong>{selectedBudget.taxRate}</p>
                      <p><strong>状态：</strong>{selectedBudget.status}</p>
                      <p><strong>供应商：</strong>{selectedBudget.supplier?.name || '-'}</p>
                      <p><strong>备注：</strong>{selectedBudget.remarks || '-'}</p>
                    </div>
                  )}
                </Card>
              </TabPane>
            )}

            <TabPane tab="预算统计" key="stats">
              <WeeklyBudgetStats projectId={projectId} />
            </TabPane>
          </Tabs>
        </Card>

        {/* 批量创建表单模态框 */}
        {showBatchForm && (
          <WeeklyBudgetBatchForm
            visible={showBatchForm}
            projectId={projectId}
            projectInfo={projectInfo}
            onSubmit={this.handleBatchFormSubmit}
            onCancel={this.handleBatchFormCancel}
          />
        )}

        {/* 资金计划模态框 */}
        {showFundingPlan && (
          <FundingPlanModal
            visible={showFundingPlan}
            weeklyBudget={selectedBudget}
            projectId={projectId}
            projectInfo={projectInfo}
            selectedBudget={selectedBudget}
            onCancel={() => this.setState({ showFundingPlan: false })}
          />
        )}
      </div>
    );
  }
}

export default WeeklyBudgetManagement;
