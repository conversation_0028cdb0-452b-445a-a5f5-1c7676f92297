import React, { Component } from 'react';
import { Card, Tag, Select, Row, Col, Divider } from 'antd';
import { 
  CONTRACT_SIGNING_STATUS, 
  getContractSigningStatusConfig,
  getContractSigningStatusLabel 
} from '../../utils/projectUtils';

const { Option } = Select;

/**
 * 合同签署状态测试组件
 * 用于测试合同签署状态相关功能
 */
class ContractSigningStatusTest extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedStatus: 'signed',
    };
  }

  handleStatusChange = (status) => {
    this.setState({ selectedStatus: status });
  };

  render() {
    const { selectedStatus } = this.state;
    const selectedConfig = getContractSigningStatusConfig(selectedStatus);

    return (
      <div style={{ padding: '20px' }}>
        <Card title="合同签署状态功能测试" style={{ marginBottom: '20px' }}>
          <Row gutter={16}>
            <Col span={12}>
              <h4>状态选择器测试</h4>
              <Select
                style={{ width: '100%' }}
                value={selectedStatus}
                onChange={this.handleStatusChange}
                placeholder="请选择合同签署状态"
              >
                {CONTRACT_SIGNING_STATUS.map((status) => (
                  <Option key={status.value} value={status.value}>
                    <Tag color={status.color} style={{ marginRight: 8 }}>
                      {status.label}
                    </Tag>
                    {status.label}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={12}>
              <h4>当前选中状态</h4>
              <div style={{ padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
                <Tag color={selectedConfig.color} style={{ marginRight: 8 }}>
                  {selectedConfig.label}
                </Tag>
                <span>值: {selectedStatus}</span>
              </div>
            </Col>
          </Row>

          <Divider />

          <h4>所有状态展示</h4>
          <Row gutter={[16, 16]}>
            {CONTRACT_SIGNING_STATUS.map((status) => (
              <Col span={6} key={status.value}>
                <Card size="small">
                  <div style={{ textAlign: 'center' }}>
                    <Tag color={status.color} style={{ marginBottom: 8 }}>
                      {status.label}
                    </Tag>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      值: {status.value}
                    </div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      颜色: {status.color}
                    </div>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>

          <Divider />

          <h4>工具函数测试</h4>
          <div style={{ backgroundColor: '#f9f9f9', padding: '16px', borderRadius: '4px' }}>
            <p><strong>getContractSigningStatusLabel('{selectedStatus}'):</strong> {getContractSigningStatusLabel(selectedStatus)}</p>
            <p><strong>getContractSigningStatusConfig('{selectedStatus}'):</strong></p>
            <pre style={{ backgroundColor: '#fff', padding: '8px', borderRadius: '4px', fontSize: '12px' }}>
              {JSON.stringify(selectedConfig, null, 2)}
            </pre>
          </div>
        </Card>

        <Card title="使用说明">
          <h4>合同签署状态枚举值：</h4>
          <ul>
            <li><Tag color="#8c8c8c">no_contract</Tag> - 无合同：项目还没有合同</li>
            <li><Tag color="#faad14">pending</Tag> - 待签署：合同已准备好，等待签署</li>
            <li><Tag color="#1890ff">signing</Tag> - 签署中：合同正在签署过程中</li>
            <li><Tag color="#52c41a">signed</Tag> - 已签署：合同已完成签署</li>
          </ul>

          <h4>在项目表单中的使用：</h4>
          <p>在项目表单的基本信息区域，合同类型字段下方会显示合同签署状态选择器。</p>

          <h4>在项目列表中的显示：</h4>
          <p>项目列表表格中会显示合同签署状态列，使用带颜色的标签显示状态。</p>

          <h4>在财务报表中的显示：</h4>
          <p>品牌执行详情报表的项目列表中也会显示合同签署状态。</p>
        </Card>
      </div>
    );
  }
}

export default ContractSigningStatusTest;
