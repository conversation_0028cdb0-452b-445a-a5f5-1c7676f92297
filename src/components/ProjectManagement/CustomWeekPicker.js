import React, { Component } from 'react';
import { Input, DatePicker, Icon } from 'antd';
import { getMonthWeekInfoByAddition } from '../../utils/weeklyBudgetUtils';

class CustomWeekPicker extends Component {
  constructor(props) {
    super(props);
    this.state = {
      open: false,
    };
  }

  formatWeekDisplay = (value) => {
    if (!value) return '';
    // 使用选择的日期来计算月份中的第几周（按加法计算）
    const monthWeekInfo = getMonthWeekInfoByAddition(value);
    return `${monthWeekInfo.month}月第${monthWeekInfo.weekOfMonth}周`;
  };

  handleWeekChange = (week) => {
    this.setState({ open: false });
    if (this.props.onChange) {
      this.props.onChange(week);
    }
  };

  handleInputClick = () => {
    this.setState({ open: true });
  };

  handleOpenChange = (open) => {
    this.setState({ open });
  };

  render() {
    const {
      value,
      placeholder,
      style,
      disabledDate,
      projectStartDate,
      projectEndDate,
      ...restProps
    } = this.props;
    const { open } = this.state;

    // 项目周期限制函数
    const getDisabledDate = (current) => {
      if (!current) return false;

      // 如果有自定义的disabledDate函数，先执行
      if (disabledDate && disabledDate(current)) {
        return true;
      }

      // 项目开始日期限制
      if (projectStartDate && current.isBefore(projectStartDate, 'day')) {
        return true;
      }

      // 项目结束日期限制
      if (projectEndDate && current.isAfter(projectEndDate, 'day')) {
        return true;
      }

      return false;
    };

    return (
      <div style={{ position: 'relative' }}>
        <Input
          {...restProps}
          value={this.formatWeekDisplay(value)}
          placeholder={placeholder || '请选择周期'}
          style={style}
          onClick={this.handleInputClick}
          readOnly
          suffix={<Icon type="calendar" />}
        />
        <DatePicker.WeekPicker
          value={value}
          onChange={this.handleWeekChange}
          disabledDate={getDisabledDate}
          open={open}
          onOpenChange={this.handleOpenChange}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            opacity: 0,
            pointerEvents: open ? 'auto' : 'none',
          }}
          getCalendarContainer={() => document.body}
        />
      </div>
    );
  }
}

export default CustomWeekPicker;
