import React, { Component } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  message,
} from 'antd';
import { brandAPI, dataTransform } from '../../services/api';
import { BRAND_STATUS } from '../../utils/projectUtils';

const { Option } = Select;

// 品牌表单校验规则
const brandFormRules = {
  name: [
    { required: true, message: '请输入品牌名称' },
    { min: 2, max: 50, message: '品牌名称长度应在2-50个字符之间' },
    { pattern: /^[a-zA-Z0-9\u4e00-\u9fa5\s]+$/, message: '品牌名称只能包含中文、英文、数字和空格' },
  ],
  description: [
    { max: 200, message: '品牌描述不能超过200个字符' },
  ],
  status: [
    { required: true, message: '请选择品牌状态' },
  ],
};

class BrandFormModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
    };
  }

  componentDidUpdate(prevProps) {
    // 当模态框打开且有编辑数据时，设置表单值
    if (this.props.visible && !prevProps.visible && this.props.editingBrand) {
      setTimeout(() => {
        // 只设置有值的字段，避免空字符串覆盖placeholder
        const fieldsToSet = {
          name: this.props.editingBrand.name,
          status: this.props.editingBrand.status,
        };

        // 只有当description有实际内容时才设置，避免空字符串覆盖placeholder
        if (this.props.editingBrand.description) {
          fieldsToSet.description = this.props.editingBrand.description;
        }

        this.props.form.setFieldsValue(fieldsToSet);
      }, 100);
    }
  }

  handleSubmit = () => {
    this.props.form.validateFields(async (err, values) => {
      if (err) {
        message.error('请检查表单填写是否正确');
        return;
      }

      this.setState({ loading: true });

      try {
        const brandData = dataTransform.brandToAPI(values);
        let response;

        if (this.props.editingBrand) {
          // 编辑模式
          response = await brandAPI.updateBrand({ id: this.props.editingBrand.id, ...brandData });
        } else {
          // 新增模式
          response = await brandAPI.createBrand(brandData);
        }

        if (response.success) {
          message.success(this.props.editingBrand ? '品牌更新成功' : '品牌创建成功');
          this.handleCancel();
          if (this.props.onSuccess) {
            this.props.onSuccess();
          }
        } else {
          message.error(response.message || '操作失败');
        }
      } catch (error) {
        console.error('Submit brand failed:', error);
        message.error(`操作失败: ${error.message}`);
      } finally {
        this.setState({ loading: false });
      }
    });
  };

  handleCancel = () => {
    this.props.form.resetFields();
    this.setState({ loading: false });
    if (this.props.onCancel) {
      this.props.onCancel();
    }
  };

  render() {
    const { visible, editingBrand, form } = this.props;
    const { loading } = this.state;
    const { getFieldDecorator } = form;

    return (
      <Modal
        title={editingBrand ? '编辑品牌' : '新建品牌'}
        visible={visible}
        onCancel={this.handleCancel}
        onOk={this.handleSubmit}
        confirmLoading={loading}
        okText="保存"
        cancelText="取消"
        destroyOnClose
      >
        <Form layout="vertical">
          <Form.Item label="品牌名称">
            {getFieldDecorator('name', {
              rules: brandFormRules.name,
            })(
              <Input placeholder="请输入品牌名称" />,
            )}
          </Form.Item>

          <Form.Item label="品牌描述">
            {getFieldDecorator('description', {
              rules: brandFormRules.description,
            })(
              <Input.TextArea
                placeholder="请输入品牌描述"
                rows={3}
                showCount
                maxLength={200}
              />,
            )}
          </Form.Item>

          <Form.Item label="品牌状态">
            {getFieldDecorator('status', {
              rules: brandFormRules.status,
              initialValue: 'active',
            })(
              <Select placeholder="请选择品牌状态">
                {BRAND_STATUS.map((status) => (
                  <Option key={status.value} value={status.value}>
                    {status.label}
                  </Option>
                ))}
              </Select>,
            )}
          </Form.Item>
        </Form>
      </Modal>
    );
  }
}

export default Form.create()(BrandFormModal);
