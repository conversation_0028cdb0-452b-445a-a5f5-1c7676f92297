import React, { Component } from 'react';
import { Card, Divider, message } from 'antd';
import ProjectForm from './ProjectForm';
import ProjectTable from './ProjectTable';
import BrandManagement from './BrandManagement';

class ProjectDemo extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showForm: true,
      showTable: true,
      showBrand: true,
    };
  }

  render() {
    const { showForm, showTable, showBrand } = this.state;

    return (
      <div style={{ padding: '20px', background: '#f0f2f5', minHeight: '100vh' }}>
        <h1 style={{ textAlign: 'center', marginBottom: '30px' }}>项目管理系统演示</h1>

        {showForm && (
          <>
            <Card title="项目表单组件演示" style={{ marginBottom: '30px' }}>
              <p>这是一个完整的项目表单，包含所有必要的字段，支持自动计算利润和毛利率。</p>
              <ProjectForm
                onSubmit={(data) => {
                  console.log('表单提交数据:', data);
                  message.success('表单提交成功！请查看控制台输出。');
                }}
              />
            </Card>
            <Divider />
          </>
        )}

        {showTable && (
          <>
            <Card title="项目表格组件演示" style={{ marginBottom: '30px' }}>
              <p>这是一个功能完整的项目列表表格，支持搜索、筛选、编辑、删除等操作。</p>
              <ProjectTable />
            </Card>
            <Divider />
          </>
        )}

        {showBrand && (
          <>
            <Card title="品牌管理组件演示" style={{ marginBottom: '30px' }}>
              <p>这是品牌库管理组件，用于维护项目中使用的品牌信息。</p>
              <BrandManagement />
            </Card>
          </>
        )}

        <Card title="组件特性说明" style={{ marginTop: '30px' }}>
          <h3>项目表单特性：</h3>
          <ul>
            <li>✅ 单据类型下拉选择（项目立项表）</li>
            <li>✅ 品牌选择（从品牌库获取）</li>
            <li>✅ 项目执行周期时间段选择</li>
            <li>✅ 项目名称输入</li>
            <li>✅ 各类预算和成本金额字段</li>
            <li>✅ 自动计算项目利润和毛利率</li>
            <li>✅ 执行PM和内容媒介人员选择</li>
            <li>✅ 合同类型选择（年框、季框、单次、PO单、京任务）</li>
            <li>✅ 预计回款月份选择（YYYY-MM格式）</li>
            <li>✅ 账期天数输入（T+天数格式）</li>
            <li>✅ 项目结算规则和KPI富文本输入</li>
            <li>✅ 附件上传功能</li>
            <li>✅ 表单验证</li>
          </ul>

          <h3>项目表格特性：</h3>
          <ul>
            <li>✅ 完整的项目列表展示</li>
            <li>✅ 搜索和筛选功能</li>
            <li>✅ 新建、编辑、删除操作</li>
            <li>✅ 批量删除功能</li>
            <li>✅ 分页和排序</li>
            <li>✅ 响应式设计</li>
          </ul>

          <h3>品牌管理特性：</h3>
          <ul>
            <li>✅ 品牌信息的增删改查</li>
            <li>✅ 品牌状态管理（启用/禁用）</li>
            <li>✅ 品牌编码唯一性验证</li>
            <li>✅ 批量操作支持</li>
          </ul>

          <h3>技术特性：</h3>
          <ul>
            <li>✅ 基于React Class组件</li>
            <li>✅ 兼容Ant Design 3.x</li>
            <li>✅ 模块化设计，组件可独立使用</li>
            <li>✅ 完整的数据模拟</li>
            <li>✅ 响应式布局</li>
            <li>✅ 表单自动计算功能</li>
          </ul>
        </Card>
      </div>
    );
  }
}

export default ProjectDemo;
