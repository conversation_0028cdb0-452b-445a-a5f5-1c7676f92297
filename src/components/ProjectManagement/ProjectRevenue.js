import React, { Component } from 'react';
import {
  Card,
  Button,
  Modal,
  message,
  Tabs,
  Alert,
  Row,
  Col,
  Statistic,
  Progress,
  Tag,
} from 'antd';
import RevenueTable from '../Revenue/RevenueTable';
import RevenueForm from '../Revenue/RevenueForm';
import RevenueStats from '../Revenue/RevenueStats';
import { ProjectPermissionGuard } from '../Auth/PermissionGuard';
import { formatAmount, generateRevenueStats } from '../../utils/revenueUtils';
// import {
// formatAmount,
// calculateCompletionRate,
// generateRevenueStats
// } from '../../utils/revenueUtils';
import { revenueAPI } from '../../services/api';

const { TabPane } = Tabs;

class ProjectRevenue extends Component {
  constructor(props) {
    super(props);
    this.state = {
      activeTab: 'list',
      modalVisible: false,
      editingRevenue: null,
      revenues: [],
      loading: false,
      projectStats: null,
    };
  }

  componentDidMount() {
    if (this.props.projectId) {
      this.loadProjectRevenues();
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.projectId !== this.props.projectId && this.props.projectId) {
      this.loadProjectRevenues();
    }
  }

  loadProjectRevenues = async () => {
    if (!this.props.projectId) return;

    this.setState({ loading: true });
    try {
      const response = await revenueAPI.getRevenues({
        projectId: this.props.projectId,
        pageSize: 1000, // 获取所有收入记录用于统计
      });

      if (response.success) {
        const revenues = response.data.revenues || [];
        const stats = generateRevenueStats(revenues);

        this.setState({
          revenues,
          projectStats: stats,
        });
      } else {
        message.error(response.message || '获取项目收入失败');
      }
    } catch (error) {
      console.error('Load project revenues failed:', error);
      message.error('获取项目收入失败');
    } finally {
      this.setState({ loading: false });
    }
  };

  handleAdd = () => {
    this.setState({
      modalVisible: true,
      editingRevenue: null,
    });
  };

  handleEdit = (revenue) => {
    this.setState({
      modalVisible: true,
      editingRevenue: revenue,
    });
  };

  handleModalCancel = () => {
    this.setState({
      modalVisible: false,
      editingRevenue: null,
    });
  };

  handleFormSubmit = (data) => {
    console.log('Revenue saved:', data);
    this.setState({
      modalVisible: false,
      editingRevenue: null,
    });
    // 重新加载项目收入数据
    this.loadProjectRevenues();
  };

  renderOverview = () => {
    const { projectStats, revenues } = this.state;
    const { projectInfo } = this.props;

    if (!projectStats) {
      return <div>暂无收入数据</div>;
    }

    const completionRate = projectStats.completionRate || 0;
    const overdueCount = revenues.filter((r) =>
      r.status !== 'received' && r.status !== 'cancelled' &&
      new Date(r.plannedDate) < new Date()).length;

    return (
      <div>
        {/* 项目基本信息 */}
        {projectInfo && (
          <Card title="项目信息" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={8}>
                <div><strong>项目名称：</strong>{projectInfo.name}</div>
              </Col>
              <Col span={8}>
                <div><strong>项目预算：</strong>{formatAmount(projectInfo.budget)}</div>
              </Col>
              <Col span={8}>
                <div><strong>项目状态：</strong>{projectInfo.status}</div>
              </Col>
            </Row>
          </Card>
        )}

        {/* 收入概览统计 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="预计总收入"
                value={projectStats.totalPlanned}
                formatter={(value) => formatAmount(value)}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="已收款金额"
                value={projectStats.totalReceived}
                formatter={(value) => formatAmount(value)}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="收款完成率"
                value={completionRate}
                suffix="%"
                valueStyle={{ color: completionRate >= 80 ? '#52c41a' : '#faad14' }}
              />
              <Progress
                percent={completionRate}
                size="small"
                status={completionRate >= 100 ? 'success' : 'active'}
                style={{ marginTop: 8 }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="逾期收入"
                value={overdueCount}
                suffix="项"
                valueStyle={{ color: overdueCount > 0 ? '#f5222d' : '#52c41a' }}
              />
              {overdueCount > 0 && (
                <Tag color="red" style={{ marginTop: 8 }}>需要关注</Tag>
              )}
            </Card>
          </Col>
        </Row>

        {/* 提醒信息 */}
        {overdueCount > 0 && (
          <Alert
            message="收入提醒"
            description={`当前有 ${overdueCount} 项收入已逾期，请及时跟进处理。`}
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        {/* 快速操作 */}
        <Card title="快速操作" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={6}>
              {/* <ProjectPermissionGuard action="create"> */}
              <Button type="primary" block onClick={this.handleAdd}>
                新建收入记录
              </Button>
              {/* </ProjectPermissionGuard> */}
            </Col>
            <Col span={6}>
              <Button block onClick={() => this.setState({ activeTab: 'list' })}>
                查看收入列表
              </Button>
            </Col>
            <Col span={6}>
              <Button block onClick={() => this.setState({ activeTab: 'stats' })}>
                查看收入统计
              </Button>
            </Col>
            <Col span={6}>
              <Button block>
                导出收入报表
              </Button>
            </Col>
          </Row>
        </Card>
      </div>
    );
  };

  render() {
    const { activeTab, modalVisible, editingRevenue, loading } = this.state;
    const { projectId, projectInfo } = this.props;
    console.log('[ projectInfo ] >', projectInfo);
    console.log('[ loading ] >', loading);
    if (!projectId) {
      return (
        <Alert
          message="请先选择项目"
          description="收入管理需要关联到具体项目，请先选择一个项目。"
          type="info"
          showIcon
        />
      );
    }

    return (
      <div>
        <Tabs
          activeKey={activeTab}
          onChange={(key) => this.setState({ activeTab: key })}
          type="card"
          tabBarExtraContent={
            activeTab === 'list' && (
              <ProjectPermissionGuard action="create">
                <Button type="primary" onClick={this.handleAdd}>
                  新建收入
                </Button>
              </ProjectPermissionGuard>
            )
          }
        >
          {/* <TabPane tab="收入概览" key="overview">
            {this.renderOverview()}
          </TabPane> */}

          <TabPane tab="收入列表" key="list">
            <RevenueTable
              projectId={projectId}
              projectName={this.props.projectName}
              onEdit={this.handleEdit}
              onDataChange={this.loadProjectRevenues}
            />
          </TabPane>

          <TabPane tab="收入统计" key="stats">
            <ProjectPermissionGuard
              action="view"
              fallback={<div style={{ textAlign: 'center', padding: '50px' }}>您没有权限查看收入统计</div>}
            >
              <RevenueStats projectId={projectId} />
            </ProjectPermissionGuard>
          </TabPane>
        </Tabs>

        {/* 编辑收入模态框 */}
        <Modal
          title={editingRevenue ? '编辑收入' : '新建收入'}
          visible={modalVisible}
          onCancel={this.handleModalCancel}
          footer={null}
          width={1000}
          destroyOnClose
        >
          <RevenueForm
            projectId={projectId}
            projectName={this.props.projectName}
            initialData={editingRevenue}
            onSubmit={this.handleFormSubmit}
            hideActions={false}
          />
        </Modal>
      </div>
    );
  }
}

export default ProjectRevenue;
