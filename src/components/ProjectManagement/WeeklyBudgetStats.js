import React, { Component } from 'react';
import { Card, Row, Col, Statistic, Table, message, Spin, Progress, Button } from 'antd';
import { weeklyBudgetAPI } from '../../services/api';
import {
  getServiceTypeLabel,
  getWeeklyBudgetStatusConfig,
  formatCurrency,
} from '../../utils/weeklyBudgetUtils';

class WeeklyBudgetStats extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      stats: {
        totalBudgets: 0,
        totalContractAmount: 0,
        totalPaidAmount: 0,
        totalRemainingAmount: 0,
        budgetsByServiceType: [],
        budgetsByStatus: [],
        budgetsBySupplier: [],
        weeklyTrend: [],
      },
    };
  }

  componentDidMount() {
    this.loadStats();
  }

  loadStats = async () => {
    this.setState({ loading: true });
    try {
      const params = {};
      if (this.props.projectId) {
        params.projectId = this.props.projectId;
      }

      const response = await weeklyBudgetAPI.getWeeklyBudgetStats(params);
      if (response.success) {
        this.setState({
          stats: response.data,
        });
      } else {
        message.error(response.message || '获取统计数据失败');
      }
    } catch (error) {
      console.error('Load weekly budget stats failed:', error);
      message.error('获取统计数据失败');
    } finally {
      this.setState({ loading: false });
    }
  };

  render() {
    const { loading, stats } = this.state;

    // 服务类型统计表格列
    const serviceTypeColumns = [
      {
        title: '服务类型',
        dataIndex: 'serviceType',
        key: 'serviceType',
        render: (serviceType) => getServiceTypeLabel(serviceType),
      },
      {
        title: '预算数量',
        dataIndex: 'count',
        key: 'count',
        align: 'right',
      },
      {
        title: '总金额',
        dataIndex: 'totalAmount',
        key: 'totalAmount',
        align: 'right',
        render: (amount) => formatCurrency(amount),
      },
      {
        title: '占比',
        key: 'percentage',
        align: 'right',
        render: (text, record) => {
          const percentage = stats.totalContractAmount > 0
            ? ((record.totalAmount / stats.totalContractAmount) * 100).toFixed(1)
            : 0;
          return `${percentage}%`;
        },
      },
    ];

    // 状态统计表格列
    const statusColumns = [
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: (status) => {
          const config = getWeeklyBudgetStatusConfig(status);
          return config.label;
        },
      },
      {
        title: '预算数量',
        dataIndex: 'count',
        key: 'count',
        align: 'right',
      },
      {
        title: '总金额',
        dataIndex: 'totalAmount',
        key: 'totalAmount',
        align: 'right',
        render: (amount) => formatCurrency(amount),
      },
      {
        title: '占比',
        key: 'percentage',
        align: 'right',
        render: (text, record) => {
          const percentage = stats.totalContractAmount > 0
            ? ((record.totalAmount / stats.totalContractAmount) * 100).toFixed(1)
            : 0;
          return `${percentage}%`;
        },
      },
    ];

    // 供应商统计表格列
    const supplierColumns = [
      {
        title: '供应商',
        dataIndex: 'supplierName',
        key: 'supplierName',
      },
      {
        title: '预算数量',
        dataIndex: 'count',
        key: 'count',
        align: 'right',
      },
      {
        title: '总金额',
        dataIndex: 'totalAmount',
        key: 'totalAmount',
        align: 'right',
        render: (amount) => formatCurrency(amount),
      },
      {
        title: '占比',
        key: 'percentage',
        align: 'right',
        render: (text, record) => {
          const percentage = stats.totalContractAmount > 0
            ? ((record.totalAmount / stats.totalContractAmount) * 100).toFixed(1)
            : 0;
          return `${percentage}%`;
        },
      },
    ];

    // 周趋势表格列
    const trendColumns = [
      {
        title: '周期',
        dataIndex: 'week',
        key: 'week',
      },
      {
        title: '合同金额',
        dataIndex: 'contractAmount',
        key: 'contractAmount',
        align: 'right',
        render: (amount) => formatCurrency(amount),
      },
      {
        title: '已付金额',
        dataIndex: 'paidAmount',
        key: 'paidAmount',
        align: 'right',
        render: (amount) => formatCurrency(amount),
      },
      {
        title: '支付进度',
        key: 'paymentProgress',
        align: 'right',
        render: (text, record) => {
          const progress = record.contractAmount > 0
            ? Math.round((record.paidAmount / record.contractAmount) * 100)
            : 0;
          return <Progress percent={progress} size="small" />;
        },
      },
    ];

    if (loading) {
      return (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      );
    }

    // 计算支付进度
    const paymentProgress = stats.totalContractAmount > 0
      ? Math.round((stats.totalPaidAmount / stats.totalContractAmount) * 100)
      : 0;

    return (
      <div>
        {/* 总体统计 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="预算总数"
                value={stats.totalBudgets}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="合同总金额"
                value={stats.totalContractAmount}
                formatter={(value) => formatCurrency(value)}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="已付金额"
                value={stats.totalPaidAmount}
                formatter={(value) => formatCurrency(value)}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="剩余金额"
                value={stats.totalRemainingAmount}
                formatter={(value) => formatCurrency(value)}
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 支付进度 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={24}>
            <Card title="整体支付进度">
              <Progress
                percent={paymentProgress}
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
                format={(percent) => `${percent}% (${formatCurrency(stats.totalPaidAmount)} / ${formatCurrency(stats.totalContractAmount)})`}
              />
            </Card>
          </Col>
        </Row>

        {/* 详细统计 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={12}>
            <Card title="按服务类型统计" style={{ height: '400px' }}>
              <Table
                columns={serviceTypeColumns}
                dataSource={stats.budgetsByServiceType}
                rowKey="serviceType"
                pagination={false}
                size="small"
              />
            </Card>
          </Col>
          <Col span={12}>
            <Card title="按状态统计" style={{ height: '400px' }}>
              <Table
                columns={statusColumns}
                dataSource={stats.budgetsByStatus}
                rowKey="status"
                pagination={false}
                size="small"
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={12}>
            <Card title="按供应商统计" style={{ height: '400px' }}>
              <Table
                columns={supplierColumns}
                dataSource={stats.budgetsBySupplier}
                rowKey="supplierId"
                pagination={false}
                size="small"
                scroll={{ y: 300 }}
              />
            </Card>
          </Col>
          <Col span={12}>
            <Card title="周趋势统计" style={{ height: '400px' }}>
              <Table
                columns={trendColumns}
                dataSource={stats.weeklyTrend}
                rowKey="week"
                pagination={false}
                size="small"
                scroll={{ y: 300 }}
              />
            </Card>
          </Col>
        </Row>

        {/* 刷新按钮 */}
        <div style={{ textAlign: 'center', marginTop: 24 }}>
          <Button
            type="primary"
            onClick={this.loadStats}
            icon="reload"
          >
            刷新统计数据
          </Button>
        </div>
      </div>
    );
  }
}

export default WeeklyBudgetStats;
