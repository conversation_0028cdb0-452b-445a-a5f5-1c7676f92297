import React, { Component } from 'react';
import {
  Card,
  Table,
  Button,
  Form,
  Input,
  Select,
  InputNumber,
  DatePicker,
  Modal,
  message,
  Row,
  Col,
  Tag,
  Divider,
} from 'antd';
import moment from 'moment';

const { Option } = Select;
const { WeekPicker } = DatePicker;

// 服务类型选项
const SERVICE_TYPES = [
  { value: 'influencer', label: '达人服务' },
  { value: 'advertising', label: '投流服务' },
  { value: 'other', label: '其他服务' },
];

// 税率选项
const TAX_RATES = [
  { value: 'special_1', label: '小规模纳税人1%' },
  { value: 'special_3', label: '小规模纳税人3%' },
  { value: 'special_6', label: '小规模纳税人6%' },
  { value: 'ordinary', label: '普票' },
];

class WeeklyBudgetSimple extends Component {
  constructor(props) {
    super(props);
    this.state = {
      weeklyBudgets: [],
      loading: false,
      modalVisible: false,
      editingBudget: null,
    };
  }

  componentDidMount() {
    this.loadMockData();
  }

  // 加载模拟数据
  loadMockData = () => {
    const mockData = [
      {
        id: '1',
        title: '第1周达人投放预算',
        weekStartDate: '2024-01-01',
        weekEndDate: '2024-01-07',
        serviceType: 'influencer',
        contractAmount: 50000,
        paidAmount: 25000,
        taxRate: 'special_6',
        status: 'approved',
        supplierName: '达人供应商A',
      },
      {
        id: '2',
        title: '第2周投流预算',
        weekStartDate: '2024-01-08',
        weekEndDate: '2024-01-14',
        serviceType: 'advertising',
        contractAmount: 30000,
        paidAmount: 0,
        taxRate: 'special_3',
        status: 'draft',
        supplierName: '投流供应商B',
      },
    ];
    this.setState({ weeklyBudgets: mockData });
  };

  handleAdd = () => {
    this.setState({
      modalVisible: true,
      editingBudget: null,
    });
  };

  handleEdit = (record) => {
    this.setState({
      modalVisible: true,
      editingBudget: record,
    });
  };

  handleDelete = (id) => {
    const { weeklyBudgets } = this.state;
    this.setState({
      weeklyBudgets: weeklyBudgets.filter((item) => item.id !== id),
    });
    message.success('删除成功');
  };

  handleModalOk = () => {
    this.props.form.validateFields((err, values) => {
      if (err) {
        return;
      }

      const { weeklyBudgets, editingBudget } = this.state;
      const selectedWeek = values.weekRange;
      const weekStartDate = selectedWeek.clone().startOf('week');
      const weekEndDate = selectedWeek.clone().endOf('week');

      const budgetData = {
        ...values,
        weekStartDate: weekStartDate.format('YYYY-MM-DD'),
        weekEndDate: weekEndDate.format('YYYY-MM-DD'),
        id: editingBudget ? editingBudget.id : Date.now().toString(),
      };

      if (editingBudget) {
        // 编辑
        const newBudgets = weeklyBudgets.map((item) =>
          (item.id === editingBudget.id ? budgetData : item));
        this.setState({ weeklyBudgets: newBudgets });
        message.success('更新成功');
      } else {
        // 新增
        this.setState({
          weeklyBudgets: [...weeklyBudgets, budgetData],
        });
        message.success('创建成功');
      }

      this.setState({ modalVisible: false });
      this.props.form.resetFields();
    });
  };

  handleModalCancel = () => {
    this.setState({ modalVisible: false });
    this.props.form.resetFields();
  };

  render() {
    const { weeklyBudgets, loading, modalVisible, editingBudget } = this.state;
    const { getFieldDecorator } = this.props.form;

    const columns = [
      {
        title: '预算标题',
        dataIndex: 'title',
        key: 'title',
      },
      {
        title: '周期',
        key: 'period',
        render: (text, record) => {
          return `${record.weekStartDate} 至 ${record.weekEndDate}`;
        },
      },
      {
        title: '服务类型',
        dataIndex: 'serviceType',
        key: 'serviceType',
        render: (type) => {
          const serviceType = SERVICE_TYPES.find((item) => item.value === type);
          return <Tag>{serviceType ? serviceType.label : type}</Tag>;
        },
      },
      {
        title: '合同金额',
        dataIndex: 'contractAmount',
        key: 'contractAmount',
        render: (amount) => `¥${amount?.toLocaleString()}`,
      },
      {
        title: '已付金额',
        dataIndex: 'paidAmount',
        key: 'paidAmount',
        render: (amount) => `¥${amount?.toLocaleString() || 0}`,
      },
      {
        title: '剩余金额',
        key: 'remaining',
        render: (text, record) => {
          const remaining = (record.contractAmount || 0) - (record.paidAmount || 0);
          return `¥${remaining.toLocaleString()}`;
        },
      },
      {
        title: '供应商',
        dataIndex: 'supplierName',
        key: 'supplierName',
      },
      {
        title: '操作',
        key: 'action',
        render: (text, record) => (
          <div>
            <Button type="link" onClick={() => this.handleEdit(record)}>
              编辑
            </Button>
            <Divider type="vertical" />
            <Button
              type="link"
              style={{ color: '#ff4d4f' }}
              onClick={() => this.handleDelete(record.id)}
            >
              删除
            </Button>
          </div>
        ),
      },
    ];

    return (
      <Card
        title="项目周预算管理"
        extra={
          <Button type="primary" onClick={this.handleAdd}>
            新建周预算
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={weeklyBudgets}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
        />

        <Modal
          title={editingBudget ? '编辑周预算' : '新建周预算'}
          visible={modalVisible}
          onOk={this.handleModalOk}
          onCancel={this.handleModalCancel}
          width={800}
        >
          <Form layout="vertical">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="预算标题">
                  {getFieldDecorator('title', {
                    initialValue: editingBudget?.title,
                    rules: [{ required: true, message: '请输入预算标题' }],
                  })(<Input placeholder="请输入预算标题" />)}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="选择周">
                  {getFieldDecorator('weekRange', {
                    initialValue: editingBudget
                      ? moment(editingBudget.weekStartDate)
                      : undefined,
                    rules: [{ required: true, message: '请选择周' }],
                  })(
                    <WeekPicker
                      style={{ width: '100%' }}
                      placeholder="请选择周"
                    />,
                  )}
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="服务类型">
                  {getFieldDecorator('serviceType', {
                    initialValue: editingBudget?.serviceType,
                    rules: [{ required: true, message: '请选择服务类型' }],
                  })(
                    <Select placeholder="请选择服务类型">
                      {SERVICE_TYPES.map((type) => (
                        <Option key={type.value} value={type.value}>
                          {type.label}
                        </Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="税率">
                  {getFieldDecorator('taxRate', {
                    initialValue: editingBudget?.taxRate,
                  })(
                    <Select placeholder="请选择税率">
                      {TAX_RATES.map((rate) => (
                        <Option key={rate.value} value={rate.value}>
                          {rate.label}
                        </Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="合同金额">
                  {getFieldDecorator('contractAmount', {
                    initialValue: editingBudget?.contractAmount,
                    rules: [{ required: true, message: '请输入合同金额' }],
                  })(
                    <InputNumber
                      placeholder="请输入合同金额"
                      style={{ width: '100%' }}
                      min={0}
                      formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                    />,
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="已付金额">
                  {getFieldDecorator('paidAmount', {
                    initialValue: editingBudget?.paidAmount,
                  })(
                    <InputNumber
                      placeholder="请输入已付金额"
                      style={{ width: '100%' }}
                      min={0}
                      formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                    />,
                  )}
                </Form.Item>
              </Col>
            </Row>

            <Form.Item label="供应商名称">
              {getFieldDecorator('supplierName', {
                initialValue: editingBudget?.supplierName,
              })(<Input placeholder="请输入供应商名称" />)}
            </Form.Item>
          </Form>
        </Modal>
      </Card>
    );
  }
}

export default Form.create()(WeeklyBudgetSimple);
