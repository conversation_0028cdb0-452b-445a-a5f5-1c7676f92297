/* 现代化项目管理页面样式 */
.modern-project-management {
  padding: 0;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 0px 0;
  margin-bottom: 2px;
  color: white;
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>'); */
  opacity: 0.3;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: white;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 32px;
  color: rgba(255, 255, 255, 0.9);
}

.page-description {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.8);
}

.header-actions {
  position: relative;
  z-index: 1;
}

.action-button {
  background: rgba(255, 255, 255, 0.2) !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  color: white !important;
  font-weight: 600 !important;
  backdrop-filter: blur(10px);
  transition: all 300ms ease !important;
  height: 48px !important;
  padding: 0 24px !important;
  border-radius: 8px !important;
}

.action-button:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
}

/* 统计区域 */
.stats-section {
  margin-bottom: 32px;
}

.stat-card {
  border-radius: 12px !important;
  border: none !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23) !important;
  transition: all 300ms ease !important;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  transition: width 300ms ease;
}

.stat-card:hover {
  transform: translateY(-4px) !important;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23) !important;
}

.stat-card:hover::before {
  width: 8px;
}

.stat-card .ant-statistic-title {
  color: #595959 !important;
  font-weight: 500 !important;
  margin-bottom: 8px !important;
}

.stat-card .ant-statistic-content {
  font-weight: 700 !important;
}

.stat-card .ant-statistic-content-prefix {
  margin-right: 8px !important;
}

/* 主要内容卡片 */
.main-content-card {
  border-radius: 16px !important;
  border: none !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  overflow: hidden;
}

.main-content-card .ant-card-body {
  padding: 0 !important;
}

/* 现代化标签页 */
.modern-tabs {
  background: white;
}

.modern-tabs .ant-tabs-bar {
  margin: 0 !important;
  border-bottom: 1px solid #f0f0f0 !important;
  background: #fafafa !important;
  padding: 0 32px !important;
}

.modern-tabs .ant-tabs-nav-container {
  height: 64px !important;
  line-height: 64px !important;
}

.modern-tabs .ant-tabs-tab {
  margin: 0 32px 0 0 !important;
  padding: 0 16px !important;
  font-weight: 500 !important;
  color: #595959 !important;
  border: none !important;
  border-radius: 8px 8px 0 0 !important;
  transition: all 300ms ease !important;
  position: relative;
  height: 48px !important;
  line-height: 48px !important;
  margin-top: 8px !important;
}

.modern-tabs .ant-tabs-tab:hover {
  color: #1890ff !important;
  background: rgba(24, 144, 255, 0.05) !important;
}

.modern-tabs .ant-tabs-tab-active {
  color: #1890ff !important;
  background: white !important;
  font-weight: 600 !important;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1) !important;
}

.modern-tabs .ant-tabs-tab-active::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
}

.modern-tabs .ant-tabs-content {
  padding: 32px !important;
  background: white;
}

.modern-tabs .ant-tabs-tabpane {
  background: white;
}

/* 标签页图标 */
.modern-tabs .ant-tabs-tab span {
  display: flex;
  align-items: center;
  gap: 8px;
}

.modern-tabs .ant-tabs-tab .anticon {
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 24px 20px;
  }
  
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }
  
  .page-title {
    font-size: 24px;
    justify-content: center;
  }
  
  .page-description {
    font-size: 14px;
  }
  
  .action-button {
    width: 100% !important;
  }
  
  .modern-tabs .ant-tabs-bar {
    padding: 0 16px !important;
  }
  
  .modern-tabs .ant-tabs-tab {
    margin: 0 16px 0 0 !important;
    padding: 0 12px !important;
    font-size: 14px !important;
  }
  
  .modern-tabs .ant-tabs-content {
    padding: 16px !important;
  }
}

@media (max-width: 576px) {
  .stats-section .ant-col {
    margin-bottom: 16px;
  }
  
  .modern-tabs .ant-tabs-nav-scroll {
    overflow-x: auto;
  }
  
  .modern-tabs .ant-tabs-tab {
    white-space: nowrap;
    flex-shrink: 0;
  }
  
  .modern-tabs .ant-tabs-tab span {
    font-size: 12px;
  }
  
  .modern-tabs .ant-tabs-tab .anticon {
    font-size: 14px;
  }
}

/* 动画效果 */
.modern-project-management {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片悬停效果 */
.stat-card,
.main-content-card {
  transition: all 300ms ease;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  border-radius: 16px;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 64px 32px;
  color: #8c8c8c;
}

.empty-state .anticon {
  font-size: 64px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

.empty-state h3 {
  color: #595959;
  font-weight: 500;
  margin-bottom: 8px;
}

.empty-state p {
  color: #8c8c8c;
  margin-bottom: 24px;
}
