import React, { Component } from 'react';
import {
  Input,
  Button,
  Row,
  Col,
  InputNumber,
  message,
  Icon,
  Table,
  DatePicker,
  Card,
  Divider,
  Popconfirm,
  Switch,
} from 'antd';
import moment from 'moment';
import { fundingPlanAPI } from '../../services/api';


const { RangePicker } = DatePicker;

class FundingPlanBatchForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      dateRange: null,
      batchAmount: null,
      titleTemplate: '',
      useTemplate: true,
      planList: [],
      selectedRowKeys: [],
    };
  }

  // 生成资金计划列表
  generatePlanList = (dateRange) => {
    if (!dateRange || dateRange.length !== 2) return [];

    const [startDate, endDate] = dateRange;
    const planList = [];

    // 按月遍历，但根据选定日期范围重新划分周期
    const current = startDate.clone().startOf('month');
    while (current.isSameOrBefore(endDate, 'month')) {
      const year = current.year();
      const month = current.month() + 1;

      // 计算该月在选定范围内的实际开始和结束日期
      const monthStart = current.clone().startOf('month');
      const monthEnd = current.clone().endOf('month');

      // 该月的实际开始日期（不早于选定开始日期）
      const actualStart = moment.max(monthStart, startDate);
      // 该月的实际结束日期（不晚于选定结束日期）
      const actualEnd = moment.min(monthEnd, endDate);

      // 如果该月在选定范围内有有效日期
      if (actualStart.isSameOrBefore(actualEnd)) {
        // 根据实际日期范围生成周期
        const monthWeeks = this.generateWeeksForDateRange(actualStart, actualEnd, year, month);
        planList.push(...monthWeeks);
      }

      current.add(1, 'month');
    }

    return planList;
  };

  // 根据日期范围生成周期列表
  generateWeeksForDateRange = (startDate, endDate, year, month) => {
    const weeks = [];
    let weekNumber = 1;
    const current = startDate.clone();

    while (current.isSameOrBefore(endDate)) {
      const weekStart = current.clone();
      const weekEnd = current.clone().add(6, 'days');

      // 确保周结束日期不超过范围结束日期
      if (weekEnd.isAfter(endDate)) {
        weekEnd.set({
          year: endDate.year(),
          month: endDate.month(),
          date: endDate.date(),
        });
      }

      weeks.push({
        key: `${year}-${month}-${weekNumber}`,
        year,
        month,
        weekOfMonth: weekNumber,
        title: this.generateTitle(year, month, weekNumber),
        plannedAmount: this.state.batchAmount || 0,
        paidAmount: 0,
        remarks: '',
        weekStart: weekStart.format('YYYY-MM-DD'),
        weekEnd: weekEnd.format('YYYY-MM-DD'),
        weekLabel: `第${weekNumber}周 (${weekStart.format('DD')}-${weekEnd.format('DD')}日)`,
      });

      // 移动到下一周的开始
      current.add(7, 'days');
      weekNumber++;

      // 如果下一周的开始日期已经超过了结束日期，则停止
      if (current.isAfter(endDate)) {
        break;
      }
    }

    return weeks;
  };

  // 生成标题
  generateTitle = (year, month, weekOfMonth) => {
    const { budget } = this.props;
    const budgetTitle = budget?.title || '项目';

    if (this.state.useTemplate && this.state.titleTemplate) {
      return this.state.titleTemplate
        .replace('{计划名称}', budgetTitle)
        .replace('{年}', year)
        .replace('{月}', month)
        .replace('{周}', weekOfMonth);
    }

    return `${budgetTitle}-${month}月第${weekOfMonth}周资金计划`;
  };

  // 处理日期范围变化
  handleDateRangeChange = (dates) => {
    if (dates && dates.length === 2) {
      const planList = this.generatePlanList(dates);
      this.setState({
        dateRange: dates,
        planList,
        selectedRowKeys: planList.map((item) => item.key),
      });
    } else {
      this.setState({
        dateRange: dates,
        planList: [],
        selectedRowKeys: [],
      });
    }
  };

  // 处理批量金额变化
  handleBatchAmountChange = (value) => {
    this.setState((prevState) => {
      const updatedPlanList = prevState.planList.map((plan) => ({
        ...plan,
        plannedAmount: value || 0,
      }));
      return {
        batchAmount: value,
        planList: updatedPlanList,
      };
    });
  };

  // 处理标题模板变化
  handleTitleTemplateChange = (e) => {
    const template = e.target.value;
    this.setState((prevState) => {
      const updatedPlanList = prevState.planList.map((plan) => ({
        ...plan,
        title: this.generateTitleWithTemplate(template, plan.year, plan.month, plan.weekOfMonth),
      }));
      return {
        titleTemplate: template,
        planList: updatedPlanList,
      };
    });
  };

  // 使用模板生成标题
  generateTitleWithTemplate = (template, year, month, weekOfMonth) => {
    const { projectInfo } = this.props;
    const projectName = projectInfo?.projectName || '项目';

    if (this.state.useTemplate && template) {
      return template
        .replace('{项目名}', projectName)
        .replace('{年}', year)
        .replace('{月}', month)
        .replace('{周}', weekOfMonth);
    }

    return `${projectName}-${month}月第${weekOfMonth}周资金计划`;
  };

  // 处理模板开关变化
  handleTemplateToggle = (checked) => {
    this.setState((prevState) => {
      const updatedPlanList = prevState.planList.map((plan) => ({
        ...plan,
        title: this.generateTitle(plan.year, plan.month, plan.weekOfMonth),
      }));
      return {
        useTemplate: checked,
        planList: updatedPlanList,
      };
    });
  };

  // 处理表格数据变化
  handleTableDataChange = (key, field, value) => {
    this.setState((prevState) => {
      const updatedPlanList = prevState.planList.map((plan) => {
        if (plan.key === key) {
          return { ...plan, [field]: value };
        }
        return plan;
      });
      return { planList: updatedPlanList };
    });
  };

  // 处理行选择变化
  handleRowSelectionChange = (selectedRowKeys) => {
    this.setState({ selectedRowKeys });
  };

  // 删除选中的计划
  handleDeleteSelected = () => {
    this.setState((prevState) => {
      const updatedPlanList = prevState.planList.filter(
        (plan) => !prevState.selectedRowKeys.includes(plan.key),
      );
      return {
        planList: updatedPlanList,
        selectedRowKeys: [],
      };
    });
  };

  // 批量应用金额到选中行
  handleApplyAmountToSelected = () => {
    const { selectedRowKeys, batchAmount } = this.state;
    if (!batchAmount || selectedRowKeys.length === 0) {
      message.warning('请先设置批量金额并选择要应用的行');
      return;
    }

    this.setState((prevState) => {
      const updatedPlanList = prevState.planList.map((plan) => {
        if (prevState.selectedRowKeys.includes(plan.key)) {
          return { ...plan, plannedAmount: batchAmount };
        }
        return plan;
      });
      return { planList: updatedPlanList };
    });

    message.success(`已将金额 ¥${batchAmount.toLocaleString()} 应用到 ${selectedRowKeys.length} 个计划`);
  };

  // 提交批量创建
  handleSubmit = async () => {
    const { planList, selectedRowKeys } = this.state;

    if (selectedRowKeys.length === 0) {
      message.warning('请至少选择一个资金计划');
      return;
    }

    // 获取选中的计划
    const selectedPlans = planList.filter((plan) => selectedRowKeys.includes(plan.key));

    // 验证数据
    const invalidPlans = selectedPlans.filter(
      (plan) => !plan.title.trim() || !plan.plannedAmount || plan.plannedAmount <= 0,
    );

    if (invalidPlans.length > 0) {
      message.error('存在标题为空或金额无效的计划，请检查后重试');
      return;
    }

    this.setState({ loading: true });

    try {
      // 转换数据格式
      const plansData = selectedPlans.map((plan) => ({
        title: plan.title,
        year: plan.year,
        month: plan.month,
        weekOfMonth: plan.weekOfMonth,
        plannedAmount: plan.plannedAmount,
        paidAmount: plan.paidAmount || 0,
        budgetId: this.props.budget.id,
        remarks: plan.remarks,
      }));

      const response = await fundingPlanAPI.batchCreateFundingPlans(this.props.projectId, plansData);

      if (response.success) {
        message.success(`成功创建 ${response.data.length} 个资金计划`);
        if (this.props.onSubmit) {
          this.props.onSubmit(response.data);
        }
      } else {
        message.error(response.message || '批量创建失败');
      }
    } catch (error) {
      console.error('Batch create funding plans failed:', error);
      message.error('批量创建失败');
    } finally {
      this.setState({ loading: false });
    }
  };

  // 重置表单
  handleReset = () => {
    this.setState({
      dateRange: null,
      batchAmount: null,
      titleTemplate: '',
      useTemplate: true,
      planList: [],
      selectedRowKeys: [],
    });
  };

  // 取消操作
  handleCancel = () => {
    if (this.props.onCancel) {
      this.props.onCancel();
    }
  };

  render() {
    const { loading, dateRange, batchAmount, titleTemplate, useTemplate, planList, selectedRowKeys } =
      this.state;

    // 表格列定义
    const columns = [
      {
        title: '时间',
        dataIndex: 'weekLabel',
        key: 'weekLabel',
        width: 180,
        render: (text, record) => (
          <div>
            <div>
              {record.year}年{record.month}月第{record.weekOfMonth}周
            </div>
            <div style={{ fontSize: '12px', color: '#999' }}>{text}</div>
          </div>
        ),
      },
      {
        title: '计划标题',
        dataIndex: 'title',
        key: 'title',
        render: (text, record) => (
          <Input
            value={text}
            onChange={(e) => this.handleTableDataChange(record.key, 'title', e.target.value)}
            placeholder="请输入计划标题"
          />
        ),
      },
      {
        title: '计划金额',
        dataIndex: 'plannedAmount',
        key: 'plannedAmount',
        width: 150,
        render: (value, record) => (
          <InputNumber
            value={value}
            onChange={(val) => this.handleTableDataChange(record.key, 'plannedAmount', val)}
            placeholder="金额"
            style={{ width: '100%' }}
            min={0}
            step={1000}
            formatter={(val) => `¥ ${val}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            parser={(val) => val.replace(/¥\s?|(,*)/g, '')}
          />
        ),
      },
      // {
      //   title: '已付金额',
      //   dataIndex: 'paidAmount',
      //   key: 'paidAmount',
      //   width: 150,
      //   render: (value, record) => (
      //     <InputNumber
      //       value={value}
      //       onChange={(val) => this.handleTableDataChange(record.key, 'paidAmount', val)}
      //       placeholder="已付金额"
      //       style={{ width: '100%' }}
      //       min={0}
      //       step={1000}
      //       formatter={(val) => `¥ ${val}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
      //       parser={(val) => val.replace(/¥\s?|(,*)/g, '')}
      //     />
      //   ),
      // },
      {
        title: '备注',
        dataIndex: 'remarks',
        key: 'remarks',
        render: (text, record) => (
          <Input
            value={text}
            onChange={(e) => this.handleTableDataChange(record.key, 'remarks', e.target.value)}
            placeholder="备注信息"
          />
        ),
      },
    ];

    // 行选择配置
    const rowSelection = {
      selectedRowKeys,
      onChange: this.handleRowSelectionChange,
      onSelectAll: (selected) => {
        if (selected) {
          this.setState({ selectedRowKeys: planList.map((item) => item.key) });
        } else {
          this.setState({ selectedRowKeys: [] });
        }
      },
    };
    return (
      <div style={{ padding: '16px' }}>
        <Card title="批量创建资金计划" size="small">
          {/* 基础设置 */}
          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col span={8}>
              <div style={{ marginBottom: 8 }}>
                <label>选择时间范围：</label>
              </div>
              <RangePicker
                value={dateRange}
                onChange={this.handleDateRangeChange}
                placeholder={['开始日期', '结束日期']}
                style={{ width: '100%' }}
                format="YYYY-MM-DD"
              />
            </Col>
            <Col span={8}>
              <div style={{ marginBottom: 8 }}>
                <label>批量金额设置：</label>
              </div>
              <InputNumber
                value={batchAmount}
                onChange={this.handleBatchAmountChange}
                placeholder="统一金额"
                style={{ width: '100%' }}
                min={0}
                step={1000}
                formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
              />
            </Col>
            <Col span={8}>
              <div style={{ marginBottom: 8 }}>
                <label>
                  <Switch
                    checked={useTemplate}
                    onChange={this.handleTemplateToggle}
                    size="small"
                  />
                  <span style={{ marginLeft: 8 }}>使用标题模板：</span>
                </label>
              </div>
              <Input
                value={titleTemplate}
                onChange={this.handleTitleTemplateChange}
                placeholder="如：{项目名}-{月}月第{周}周资金计划"
                disabled={!useTemplate}
              />
            </Col>
          </Row>

          {/* 操作按钮 */}
          {planList.length > 0 && (
            <Row style={{ marginBottom: 16 }}>
              <Col span={24}>
                <Button
                  type="primary"
                  size="small"
                  onClick={this.handleApplyAmountToSelected}
                  disabled={selectedRowKeys.length === 0 || !batchAmount}
                  style={{ marginRight: 8 }}
                >
                  <Icon type="dollar" />
                  应用金额到选中项
                </Button>
                <Popconfirm
                  title="确定要删除选中的计划吗？"
                  onConfirm={this.handleDeleteSelected}
                  disabled={selectedRowKeys.length === 0}
                >
                  <Button type="danger" size="small" disabled={selectedRowKeys.length === 0}>
                    <Icon type="delete" />
                    删除选中项
                  </Button>
                </Popconfirm>
                <span style={{ marginLeft: 16, color: '#666' }}>
                  已选择 {selectedRowKeys.length} / {planList.length} 项
                </span>
              </Col>
            </Row>
          )}

          {/* 计划列表表格 */}
          {planList.length > 0 && (
            <>
              <Divider orientation="left">资金计划列表</Divider>
              <Table
                columns={columns}
                dataSource={planList}
                rowSelection={rowSelection}
                pagination={false}
                size="small"
                scroll={{ y: 400 }}
                style={{ marginBottom: 16 }}
              />
            </>
          )}

          {/* 提交按钮 */}
          <Row>
            <Col span={24} style={{ textAlign: 'center', marginTop: 24 }}>
              <Button
                type="primary"
                size="large"
                loading={loading}
                onClick={this.handleSubmit}
                disabled={selectedRowKeys.length === 0}
                style={{ marginRight: 16 }}
              >
                <Icon type="plus" />
                创建 {selectedRowKeys.length} 个资金计划
              </Button>
              <Button onClick={this.handleReset} size="large" style={{ marginRight: 16 }}>
                <Icon type="reload" />
                重置
              </Button>
              <Button onClick={this.handleCancel} size="large">
                <Icon type="close" />
                取消
              </Button>
            </Col>
          </Row>
        </Card>
      </div>
    );
  }
}

export default FundingPlanBatchForm;
