import React, { Component } from 'react';
import { Card, InputNumber, Row, Col, Typography } from 'antd';

const { Title, Text } = Typography;

class CurrencyTest extends Component {
  constructor(props) {
    super(props);
    this.state = {
      testAmount: 0,
    };
  }

  // 人民币转大写
  convertCurrencyToChinese = (amount) => {
    if (!amount || amount === 0) {
      return '零元整';
    }

    const num = Math.floor(Math.abs(amount));
    const digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    const units = ['', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿'];

    if (num === 0) {
      return '零元整';
    }

    const numStr = num.toString();
    let result = '';
    let zeroFlag = false; // 标记是否需要添加零

    for (let i = 0; i < numStr.length; i++) {
      const digit = parseInt(numStr[i], 10);
      const unitIndex = numStr.length - i - 1;

      if (digit === 0) {
        // 当前位是0
        if (unitIndex === 4 || unitIndex === 8) {
          // 万位或亿位，即使是0也要加单位（如果前面有数字）
          if (result && !result.endsWith('万') && !result.endsWith('亿')) {
            result += units[unitIndex];
          }
        }
        zeroFlag = true;
      } else {
        // 当前位不是0
        if (zeroFlag && result) {
          result += '零';
        }
        result += digits[digit];
        if (unitIndex > 0) {
          result += units[unitIndex];
        }
        zeroFlag = false;
      }
    }

    // 清理结果
    result = result
      .replace(/零+/g, '零') // 多个零合并为一个
      .replace(/零万/g, '万') // 零万 -> 万
      .replace(/零亿/g, '亿') // 零亿 -> 亿
      .replace(/零元/g, '元') // 零元 -> 元
      .replace(/零$/, ''); // 去掉末尾的零

    return `${result }元整`;
  };

  handleAmountChange = (value) => {
    this.setState({ testAmount: value || 0 });
  };

  render() {
    const { testAmount } = this.state;

    const testCases = [
      { amount: 0, expected: '零元整' },
      { amount: 1, expected: '壹元整' },
      { amount: 10, expected: '壹拾元整' },
      { amount: 100, expected: '壹佰元整' },
      { amount: 1000, expected: '壹仟元整' },
      { amount: 10000, expected: '壹万元整' },
      { amount: 12345, expected: '壹万贰仟叁佰肆拾伍元整' },
      { amount: 100000, expected: '壹拾万元整' },
      { amount: 1000000, expected: '壹佰万元整' },
      { amount: 10000000, expected: '壹仟万元整' },
      { amount: 100000000, expected: '壹亿元整' },
    ];

    return (
      <div style={{ padding: '20px' }}>
        <Title level={2}>人民币大写转换测试</Title>

        <Card title="实时测试" style={{ marginBottom: 20 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Text strong>输入金额：</Text>
              <InputNumber
                value={testAmount}
                onChange={this.handleAmountChange}
                style={{ width: '100%', marginTop: 8 }}
                formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                placeholder="请输入金额"
              />
            </Col>
            <Col span={12}>
              <Text strong>人民币大写：</Text>
              <div style={{
                marginTop: 8,
                padding: '8px 12px',
                backgroundColor: '#f5f5f5',
                border: '1px solid #d9d9d9',
                borderRadius: '4px',
                minHeight: '32px',
                lineHeight: '16px',
                fontSize: '14px',
                color: '#1890ff',
                fontWeight: 'bold',
              }}
              >
                {this.convertCurrencyToChinese(testAmount)}
              </div>
            </Col>
          </Row>
        </Card>

        <Card title="测试用例验证">
          <Row gutter={[16, 16]}>
            {testCases.map((testCase) => {
              const actual = this.convertCurrencyToChinese(testCase.amount);
              const isCorrect = actual === testCase.expected;

              return (
                <Col span={12}>
                  <div style={{
                    padding: '12px',
                    border: `1px solid ${isCorrect ? '#52c41a' : '#f5222d'}`,
                    borderRadius: '4px',
                    backgroundColor: isCorrect ? '#f6ffed' : '#fff2f0',
                  }}
                  >
                    <div><Text strong>输入：</Text>¥{testCase.amount.toLocaleString()}</div>
                    <div><Text strong>期望：</Text>{testCase.expected}</div>
                    <div><Text strong>实际：</Text>{actual}</div>
                    <div style={{ color: isCorrect ? '#52c41a' : '#f5222d' }}>
                      <Text strong>{isCorrect ? '✅ 正确' : '❌ 错误'}</Text>
                    </div>
                  </div>
                </Col>
              );
            })}
          </Row>
        </Card>
      </div>
    );
  }
}

export default CurrencyTest;
