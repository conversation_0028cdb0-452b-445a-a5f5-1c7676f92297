import React, { Component } from 'react';
import {
  Form,
  Input,
  Select,
  Button,
  Row,
  Col,
  InputNumber,
  message,
  Icon,
} from 'antd';
import { weeklyBudgetAPI, supplierAPI } from '../../services/api';
import {
  SERVICE_TYPES,
  TAX_RATES,
} from '../../utils/weeklyBudgetUtils';

const { Option } = Select;

// 表单校验规则
const formRules = {
  title: [
    { required: true, message: '请输入预算标题' },
    { min: 2, max: 100, message: '预算标题长度应在2-100个字符之间' },
  ],
  serviceType: [
    { required: true, message: '请选择服务类型' },
  ],
  contractAmount: [
    { required: true, message: '请输入合同金额' },
    { type: 'number', min: 0.01, message: '合同金额必须大于0' },
  ],
  paidAmount: [
    { type: 'number', min: 0, message: '已付金额不能为负数' },
  ],
  serviceContent: [
    { required: true, message: '请输入服务内容' },
  ],
  supplierId: [
    { required: true, message: '请选择供应商' },
  ],
  taxRate: [
    { required: true, message: '请选择税率' },
  ],
};

class BudgetForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      supplierOptions: [],
    };
  }

  componentDidMount() {
    this.loadSuppliers();

    // 如果是编辑模式，填充表单数据
    if (this.props.budget) {
      this.fillFormData();
    }
  }

  componentDidUpdate(prevProps) {
    // 当budget属性变化时，重新填充表单
    if (this.props.budget !== prevProps.budget) {
      if (this.props.budget) {
        this.fillFormData();
      } else {
        this.props.form.resetFields();
      }
    }
  }

  loadSuppliers = async () => {
    try {
      const response = await supplierAPI.getSuppliers({ status: 'active', pageSize: 1000 });
      if (response.success) {
        const supplierOptions = response.data.suppliers.map((supplier) => ({
          value: supplier.id,
          label: supplier.name,
          shortName: supplier.shortName,
          preferredTaxRate: supplier.preferredTaxRate, // 保存税率信息
        }));
        this.setState({ supplierOptions });
      }
    } catch (error) {
      console.error('Load suppliers failed:', error);
    }
  };

  fillFormData = () => {
    const { budget } = this.props;
    if (budget) {
      this.props.form.setFieldsValue({
        title: budget.title,
        serviceType: budget.serviceType,
        serviceContent: budget.serviceContent,
        contractAmount: budget.contractAmount,
        taxRate: budget.taxRate,
        paidAmount: budget.paidAmount,
        supplierId: budget.supplierId,
        remarks: budget.remarks,
      });
    }
  };

  // 获取服务类型标签
  getServiceTypeLabel = (serviceType) => {
    const type = SERVICE_TYPES.find((t) => t.value === serviceType);
    return type ? type.label : serviceType;
  };

  handleServiceTypeChange = (serviceType) => {
    console.log('[ serviceType ] >', serviceType);
    // 根据项目名称和服务类型生成标题 + 供应商名称
    // if (this.props.projectInfo && this.props.projectInfo.projectName && this.props.projectInfo.supplierName) {
    //   const title = `${this.props.projectInfo.projectName}-${this.getServiceTypeLabel(serviceType)}预算`;
    //   this.props.form.setFieldsValue({ title });
    // }
  };

  // 处理供应商选择，自动带入税率
  handleSupplierChange = (supplierId) => {
    const { supplierOptions } = this.state;
    const selectedSupplier = supplierOptions.find((supplier) => supplier.value === supplierId);
    const serviceType = this.props.form.getFieldValue('serviceType');

    if (this.props.projectInfo && this.props.projectInfo.projectName && selectedSupplier) {
      const title = `${this.props.projectInfo.projectName}-${this.getServiceTypeLabel(serviceType)}-${selectedSupplier.label}预算`;
      this.props.form.setFieldsValue({ title });
    }
    if (selectedSupplier && selectedSupplier.preferredTaxRate) {
      // 自动填入供应商的首选税率
      this.props.form.setFieldsValue({
        taxRate: selectedSupplier.preferredTaxRate,
      });
      message.info(`已自动填入供应商首选税率：${TAX_RATES.find((rate) =>
        rate.value === selectedSupplier.preferredTaxRate).label}`);
    }
  };

  handleSubmit = (e) => {
    e.preventDefault();
    this.props.form.validateFields(async (err, values) => {
      if (err) {
        message.error('请检查表单填写是否正确');
        return;
      }

      this.setState({ loading: true });

      try {
        const budgetData = {
          title: values.title,
          serviceType: values.serviceType,
          serviceContent: values.serviceContent,
          contractAmount: values.contractAmount,
          taxRate: values.taxRate,
          paidAmount: values.paidAmount || 0,
          supplierId: values.supplierId,
          remarks: values.remarks,
          status: 'created', // 默认状态为已创建
        };

        let response;
        if (this.props.budget) {
          // 编辑模式
          delete budgetData.status;
          response = await weeklyBudgetAPI.updateWeeklyBudget(
            this.props.budget.id,
            budgetData,
          );
        } else {
          // 新建模式
          response = await weeklyBudgetAPI.createWeeklyBudget(
            this.props.projectId,
            budgetData,
          );
        }

        if (response.success) {
          message.success(this.props.budget ? '更新预算成功' : '创建预算成功');
          this.props.onSubmit && this.props.onSubmit(response.data);
        } else {
          message.error(response.message || '操作失败');
        }
      } catch (error) {
        console.error('Submit budget failed:', error);
        message.error('操作失败');
      } finally {
        this.setState({ loading: false });
      }
    });
  };

  handleReset = () => {
    this.props.form.resetFields();
  };

  handleCancel = () => {
    this.props.onCancel && this.props.onCancel();
  };


  render() {
    const { loading, supplierOptions } = this.state;
    const { budget } = this.props;
    const { getFieldDecorator } = this.props.form;
    const isEdit = !!budget;

    return (
      <div style={{ padding: '16px' }}>
        <Form layout="vertical" onSubmit={this.handleSubmit}>
          {/* 基本信息 */}
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="费用类型">
                {getFieldDecorator('serviceType', {
                  rules: formRules.serviceType,
                })(
                  <Select
                    placeholder="请选择费用类型"
                    onChange={this.handleServiceTypeChange}
                  >
                    {SERVICE_TYPES.map((type) => {
                      let iconType = 'setting';
                      if (type.value === 'influencer') {
                        iconType = 'user';
                      } else if (type.value === 'advertising') {
                        iconType = 'rocket';
                      }

                      return (
                        <Option key={type.value} value={type.value}>
                          <Icon type={iconType} />
                          {' '}{type.label}
                        </Option>
                      );
                    })}
                  </Select>,
                )}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="供应商">
                {getFieldDecorator('supplierId', {
                  rules: formRules.supplierId,
                })(
                  <Select
                    placeholder="请选择供应商"
                    allowClear
                    showSearch
                    onChange={this.handleSupplierChange}
                    filterOption={(input, option) =>
                      option.props.children[0].toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {supplierOptions.map((supplier) => (
                      <Option key={supplier.value} value={supplier.value}>
                        {supplier.label}
                        {supplier.preferredTaxRate && (
                          <span style={{ color: '#999', marginLeft: 8 }}>
                            (税率: {TAX_RATES.find((rate) =>
                            rate.value === supplier.preferredTaxRate).label})
                          </span>
                        )}
                      </Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="预算标题">
                {getFieldDecorator('title', {
                  rules: formRules.title,
                })(
                  <Input
                    placeholder="请输入预算标题"
                    prefix={<Icon type="edit" />}
                  />,
                )}
              </Form.Item>
            </Col>

          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="服务内容">
                {getFieldDecorator('serviceContent', {
                  rules: formRules.serviceContent,
                })(
                  <Input.TextArea
                    placeholder="请输入服务内容描述"
                    rows={3}
                  />,
                )}
              </Form.Item>
            </Col>
          </Row>

          {/* 财务信息 */}
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="费用预算">
                {getFieldDecorator('contractAmount', {
                  rules: formRules.contractAmount,
                })(
                  <InputNumber
                    placeholder="请输入费用预算"
                    style={{ width: '100%' }}
                    min={0}
                    step={100}
                    formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                  />,
                )}
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="税率">
                {getFieldDecorator('taxRate', {
                  rules: formRules.taxRate,
                })(
                  <Select placeholder="请选择税率">
                    {TAX_RATES.map((rate) => (
                      <Option key={rate.value} value={rate.value}>
                        {rate.label}
                      </Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="已付金额">
                {getFieldDecorator('paidAmount', {
                  rules: formRules.paidAmount,
                })(
                  <InputNumber
                    placeholder="请输入已付金额"
                    style={{ width: '100%' }}
                    disabled
                    min={0}
                    step={100}
                    formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                  />,
                )}
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="备注">
                {getFieldDecorator('remarks')(
                  <Input.TextArea
                    placeholder="请输入备注信息"
                    rows={3}
                  />,
                )}
              </Form.Item>
            </Col>
          </Row>

          {/* 操作按钮 */}
          <Row>
            <Col span={24} style={{ textAlign: 'center', marginTop: 24 }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                size="large"
                style={{ marginRight: 16 }}
              >
                <Icon type={isEdit ? 'save' : 'plus'} />
                {isEdit ? '更新预算' : '创建预算'}
              </Button>
              <Button
                onClick={this.handleReset}
                size="large"
                style={{ marginRight: 16 }}
              >
                <Icon type="reload" />
                重置表单
              </Button>
              <Button
                onClick={this.handleCancel}
                size="large"
              >
                <Icon type="close" />
                取消
              </Button>
            </Col>
          </Row>
        </Form>
      </div>
    );
  }
}

export default Form.create()(BudgetForm);
