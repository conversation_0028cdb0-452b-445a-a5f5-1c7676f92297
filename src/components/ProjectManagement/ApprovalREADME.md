# 周预算审批功能

这是一个完整的周预算审批流程管理系统，支持发起对公付款审批、跟踪审批状态和进度管理。

## 功能特性

### 🚀 核心功能
- ✅ **发起对公付款审批** - 基于周预算创建付款审批申请
- ✅ **审批状态跟踪** - 实时查看审批进度和状态
- ✅ **钉钉审批集成** - 与钉钉审批流程无缝对接
- ✅ **文件上传支持** - 支持发票和附件上传
- ✅ **智能表单验证** - 专业的表单校验和错误提示

### 📋 审批表单功能
- **基本信息**：付款金额、付款事由、期望付款时间
- **合同信息**：合同签署主体、付款方式
- **收款账号**：账户名称、账号、开户银行、银行代码
- **文件管理**：发票文件上传、附件上传
- **关联信息**：关联审批单、备注信息

### 📊 审批状态管理
- **状态跟踪**：审批中、已通过、已拒绝、已取消
- **进度可视化**：步骤条和时间线展示
- **状态同步**：支持手动同步钉钉审批状态
- **详细信息**：完整的审批记录和历史

## 组件结构

```
ProjectManagement/
├── ApprovalForm.js          # 审批表单组件
├── ApprovalStatus.js        # 审批状态跟踪组件
├── WeeklyBudgetTable.js     # 周预算表格（已集成审批功能）
├── ApprovalDemo.js          # 审批功能演示页面
└── ApprovalREADME.md       # 说明文档
```

## API接口

### 审批管理API
- `POST /weekly-budgets/approval` - 发起对公付款审批
- `GET /approvals/{id}` - 获取单个审批实例
- `POST /approvals/sync/{processInstanceId}` - 同步审批状态
- `GET /approvals` - 获取审批列表

### 接口详情

#### 发起对公付款审批
```javascript
POST /weekly-budgets/approval
{
  "weeklyBudgetId": "string",           // 周预算ID (必填)
  "totalAmount": 10000,                 // 付款总额 (必填)
  "paymentReason": "string",            // 付款事由 (必填)
  "contractEntity": "company_a",        // 合同签署主体 (可选)
  "expectedPaymentDate": "2024-01-15",  // 期望付款时间 (必填)
  "paymentMethod": "bank_transfer",     // 付款方式 (可选)
  "receivingAccount": {                 // 收款账号信息 (必填)
    "accountName": "string",            // 账户名称 (必填)
    "accountNumber": "string",          // 账号 (必填)
    "bankName": "string",               // 开户银行 (必填)
    "bankCode": "string"                // 银行代码 (可选)
  },
  "relatedApprovalId": "string",        // 关联审批单 (可选)
  "invoiceFiles": ["string"],           // 发票文件URL列表 (可选)
  "attachments": ["string"],            // 附件URL列表 (可选)
  "remark": "string"                    // 备注 (可选)
}
```

## 使用方法

### 1. 在周预算表格中使用
审批功能已集成到 `WeeklyBudgetTable` 组件中：

```jsx
import WeeklyBudgetTable from './components/ProjectManagement/WeeklyBudgetTable';

<WeeklyBudgetTable
  projectId={projectId}
  onView={handleView}
  onEdit={handleEdit}
/>
```

### 2. 独立使用审批表单
```jsx
import ApprovalForm from './components/ProjectManagement/ApprovalForm';

<ApprovalForm
  visible={showForm}
  weeklyBudget={selectedBudget}
  onSubmit={handleSubmit}
  onCancel={handleCancel}
/>
```

### 3. 独立使用审批状态组件
```jsx
import ApprovalStatus from './components/ProjectManagement/ApprovalStatus';

<ApprovalStatus
  visible={showStatus}
  approvalId={approvalId}
  onCancel={handleCancel}
/>
```

## 操作流程

### 发起审批流程
1. 在周预算列表中找到需要付款的预算项
2. 点击"发起审批"按钮（仅在有剩余金额时可用）
3. 填写审批表单：
   - 输入付款金额（不能超过剩余金额）
   - 填写付款事由
   - 选择期望付款时间
   - 填写收款账号信息
   - 上传发票和附件（可选）
4. 提交审批申请

### 查看审批状态
1. 在周预算列表中点击"审批状态"按钮
2. 查看审批详细信息：
   - 审批基本信息和状态
   - 审批进度步骤
   - 审批时间线
   - 关联的周预算信息
3. 可手动同步最新的审批状态

## 数据验证

### 表单验证规则
- **付款金额**：必填，必须大于0，不能超过剩余金额
- **付款事由**：必填，不能为空
- **期望付款时间**：必填，不能早于当前日期
- **收款账号**：账户名称、账号、开户银行为必填项

### 业务逻辑验证
- 只有剩余金额大于0的周预算才能发起审批
- 审批金额不能超过周预算的剩余金额
- 支持多次审批，直到全部金额支付完成

## 状态管理

### 审批状态
- `PENDING` - 审批中（蓝色）
- `APPROVED` - 已通过（绿色）
- `REJECTED` - 已拒绝（红色）
- `CANCELLED` - 已取消（灰色）

### 审批结果
- `agree` - 同意（绿色）
- `refuse` - 拒绝（红色）
- `redirect` - 转交（橙色）

## 文件上传

### 支持的文件类型
- **发票文件**：PDF、JPG、JPEG、PNG
- **附件**：所有常见文件类型

### 上传功能
- 支持多文件上传
- 实时上传进度显示
- 上传成功后可预览和删除
- 自动生成文件URL用于审批提交

## 错误处理

- 网络请求失败时显示友好的错误提示
- 表单验证失败时显示具体的错误信息
- 文件上传失败时提供重试机制
- 审批状态同步失败时支持手动重试

## 性能优化

- 使用防抖机制避免重复提交
- 智能缓存审批状态信息
- 按需加载审批详情数据
- 优化文件上传体验

## 兼容性

- 支持 Ant Design 3.x
- 兼容现有的周预算管理系统
- 与钉钉审批流程完全集成
- 支持移动端响应式设计

## 演示页面

可以通过 `ApprovalDemo.js` 查看完整的功能演示：

```jsx
import ApprovalDemo from './components/ProjectManagement/ApprovalDemo';

// 在路由中使用
<Route path="/approval-demo" component={ApprovalDemo} />
```

## 技术要求

- React 16.12.0+
- Ant Design 3.26.8
- moment.js（用于时间处理）
- 支持文件上传的后端API

## 注意事项

1. 确保后端API接口已正确实现
2. 钉钉审批流程需要正确配置
3. 文件上传服务需要支持多文件上传
4. 建议在生产环境中启用HTTPS以确保文件上传安全
