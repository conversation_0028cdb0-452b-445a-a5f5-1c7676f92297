import React, { Component } from 'react';
import {
  Card,
  Tabs,
  Descriptions,
  Tag,
  Button,
  // Row,
  // Col,
  Spin,
  message,
  Modal,
} from 'antd';
import moment from 'moment';
import ProjectRevenue from './ProjectRevenue';
import ProjectForm from './ProjectForm';
import BudgetManagement from './BudgetManagement';
import FundingPlanManagement from './FundingPlanManagement';
import { projectAPI } from '../../services/api';
// import { ProjectPermissionGuard } from '../Auth/PermissionGuard';
import {
  CONTRACT_TYPES,
  // PROJECT_STATUS,
  formatCurrency,
  formatPercentage,
} from '../../utils/projectUtils';

const { TabPane } = Tabs;

class ProjectDetail extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      projectInfo: null,
      editModalVisible: false,
      activeTab: 'info',
    };
  }

  componentDidMount() {
    if (this.props.projectId) {
      this.loadProjectDetail();
    }
  }

  componentDidUpdate(prevProps) {
    console.log(this.props);
    if (prevProps.projectId !== this.props.projectId && this.props.projectId) {
      this.loadProjectDetail();
    }
  }

  loadProjectDetail = async () => {
    if (!this.props.projectId) return;

    this.setState({ loading: true });
    try {
      const response = await projectAPI.getProject(this.props.projectId);
      if (response.success) {
        this.setState({ projectInfo: response.data });
      } else {
        message.error(response.message || '获取项目详情失败');
      }
    } catch (error) {
      console.error('Load project detail failed:', error);
      message.error('获取项目详情失败');
    } finally {
      this.setState({ loading: false });
    }
  };

  handleEdit = () => {
    this.setState({ editModalVisible: true });
  };

  handleEditCancel = () => {
    this.setState({ editModalVisible: false });
  };

  handleEditSubmit = (data) => {
    console.log('Project updated:', data);
    this.setState({ editModalVisible: false });
    this.loadProjectDetail(); // 重新加载项目详情
  };

  getStatusConfig = (status) => {
    const statusMap = {
      planning: { color: 'blue', text: '规划中' },
      executing: { color: 'orange', text: '执行中' },
      completed: { color: 'green', text: '已完成' },
      suspended: { color: 'red', text: '已暂停' },
      cancelled: { color: 'gray', text: '已取消' },
    };
    return statusMap[status] || { color: 'default', text: status };
  };

  getContractTypeLabel = (type) => {
    const contractType = CONTRACT_TYPES.find((item) => item.value === type);
    return contractType ? contractType.label : type;
  };

  renderProjectInfo = () => {
    const { projectInfo } = this.state;
    if (!projectInfo) return null;

    const statusConfig = this.getStatusConfig(projectInfo.status);

    return (
      <Card
        title="项目基本信息"
        extra={
          // <ProjectPermissionGuard action="edit">
          // </ProjectPermissionGuard>

          <Button type="primary" onClick={this.handleEdit}>编辑项目</Button>
        }
      >
        <Descriptions column={2} bordered>
          <Descriptions.Item label="项目名称">
            {projectInfo.projectName}
          </Descriptions.Item>
          <Descriptions.Item label="项目状态">
            <Tag color={statusConfig.color}>{statusConfig.text}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="所属品牌">
            {projectInfo.brandName || projectInfo.brand.name}
          </Descriptions.Item>
          <Descriptions.Item label="合同类型">
            {this.getContractTypeLabel(projectInfo.contractType)}
          </Descriptions.Item>
          <Descriptions.Item label="执行PM">
            {projectInfo.executivePMName || projectInfo.executivePM}
          </Descriptions.Item>
          <Descriptions.Item label="项目规划预算">
            {formatCurrency(projectInfo.planningBudget)}
          </Descriptions.Item>
          <Descriptions.Item label="项目执行周期" span={2}>
            {(() => {
              if (projectInfo.executionPeriod && projectInfo.executionPeriod.length === 2) {
                const startDate = moment(projectInfo.executionPeriod[0]).format('YYYY-MM-DD');
                const endDate = moment(projectInfo.executionPeriod[1]).format('YYYY-MM-DD');
                return `${startDate} 至 ${endDate}`;
              }
              if (projectInfo.period && projectInfo.period.startDate &&
                  projectInfo.period.endDate) {
                return `${projectInfo.period.startDate} 至 ${projectInfo.period.endDate}`;
              }
              return '-';
            })()}
          </Descriptions.Item>
          <Descriptions.Item label="达人预算">
            {formatCurrency(projectInfo.talentBudget)}
          </Descriptions.Item>
          <Descriptions.Item label="投流预算">
            {formatCurrency(projectInfo.adBudget)}
          </Descriptions.Item>
          <Descriptions.Item label="其他预算">
            {formatCurrency(projectInfo.otherBudget)}
          </Descriptions.Item>
          <Descriptions.Item label="项目利润">
            <span style={{
              color: projectInfo.projectProfit >= 0 ? '#52c41a' : '#f5222d',
              fontWeight: 'bold',
            }}
            >
              {formatCurrency(projectInfo.projectProfit)}
            </span>
          </Descriptions.Item>
          <Descriptions.Item label="项目毛利">
            <span style={{
              color: projectInfo.grossMargin >= 0 ? '#52c41a' : '#f5222d',
              fontWeight: 'bold',
            }}
            >
              {formatPercentage(projectInfo.grossMargin)}
            </span>
          </Descriptions.Item>
          <Descriptions.Item label="内容媒介" span={2}>
            {projectInfo.contentMedia && projectInfo.contentMedia.length > 0
              ? projectInfo.contentMedia.join(', ')
              : '-'
            }
          </Descriptions.Item>
          <Descriptions.Item label="项目结算规则" span={2}>
            {projectInfo.settlementRules || '-'}
          </Descriptions.Item>
          <Descriptions.Item label="KPI指标" span={2}>
            {projectInfo.kpi || '-'}
          </Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {projectInfo.createdAt ? moment(projectInfo.createdAt).format('YYYY-MM-DD HH:mm') : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="更新时间">
            {projectInfo.updatedAt ? moment(projectInfo.updatedAt).format('YYYY-MM-DD HH:mm') : '-'}
          </Descriptions.Item>
        </Descriptions>
      </Card>
    );
  };

  render() {
    const { loading, projectInfo, editModalVisible, activeTab } = this.state;
    const { projectId } = this.props;

    if (!projectId) {
      return (
        <Card>
          <div style={{ textAlign: 'center', padding: '50px' }}>
            请选择一个项目查看详情
          </div>
        </Card>
      );
    }

    return (
      <Spin spinning={loading}>
        <div style={{ padding: '20px' }}>
          <Tabs
            activeKey={activeTab}
            onChange={(key) => this.setState({ activeTab: key })}
            type="card"
          >
            <TabPane tab="项目信息" key="info">
              {this.renderProjectInfo()}
            </TabPane>

            <TabPane tab="收入管理" key="revenue">
              {/* <ProjectPermissionGuard
                action="view"
                fallback={<div style={{ textAlign: 'center', padding: '50px' }}>您没有权限查看项目收入</div>}
              >

              </ProjectPermissionGuard> */}
              <ProjectRevenue
                projectId={projectId}
                projectInfo={projectInfo}
              />
            </TabPane>

            <TabPane tab="预算管理" key="budget">
              <BudgetManagement
                projectId={projectId}
                projectInfo={projectInfo}
              />
            </TabPane>

            <TabPane tab="资金计划" key="fundingPlan">
              <FundingPlanManagement
                projectId={projectId}
                projectInfo={projectInfo}
              />
            </TabPane>

            <TabPane tab="项目文档" key="documents">
              <Card>
                <div style={{ textAlign: 'center', padding: '50px' }}>
                  项目文档管理功能开发中...
                </div>
              </Card>
            </TabPane>

            <TabPane tab="项目日志" key="logs">
              <Card>
                <div style={{ textAlign: 'center', padding: '50px' }}>
                  项目日志功能开发中...
                </div>
              </Card>
            </TabPane>
          </Tabs>

          {/* 编辑项目模态框 */}
          <Modal
            title="编辑项目"
            visible={editModalVisible}
            onCancel={this.handleEditCancel}
            footer={null}
            width={1200}
            destroyOnClose
          >
            <ProjectForm
              initialData={projectInfo}
              onSubmit={this.handleEditSubmit}
              hideActions={false}
            />
          </Modal>
        </div>
      </Spin>
    );
  }
}

export default ProjectDetail;
