import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Tag,
  Popconfirm,
  message,
  Input,
  Select,
  Row,
  Col,
  Card,
  Icon,
  // ,
  Alert,
} from 'antd';
import moment from 'moment';
import { brandAPI, dataTransform } from '../../services/api';
import BrandFormModal from './BrandFormModal';
import { PageGuard, FeatureGuard, ButtonGuard } from '../Permission/PermissionGuard';
import { useFeaturePermission } from '../../hooks/usePermission';
import { PAGE_PERMISSIONS } from '../../config/permissions';
// import ErrorBoundary from '../Common/ErrorBoundary';

const BrandManagement = () => {
  // 权限控制
  const {
    canRead,
    canDelete,
  } = useFeaturePermission('brand');

  // 状态管理
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingBrand, setEditingBrand] = useState(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [searchFilters, setSearchFilters] = useState({
    name: undefined,
    status: undefined,
  });

  // 加载数据
  const loadData = async (params = {}) => {
    setLoading(true);
    try {
      const requestParams = {
        page: params.page || pagination.current,
        pageSize: params.pageSize || pagination.pageSize,
        keyword: searchFilters.name,
        status: searchFilters.status,
        ...params,
      };

      try {
        const response = await brandAPI.getBrands(requestParams);
        console.log('Brand API response:', response);

        if (response.success) {
          const brands = response.data.brands.map((brand) =>
            dataTransform.brandFromAPI(brand));
          setDataSource(brands);
          setPagination((prev) => ({
            ...prev,
            current: response.data.page,
            total: response.data.total,
            pageSize: response.data.pageSize,
          }));
        } else {
          throw new Error(response.message || '获取品牌列表失败');
        }
      } catch (apiError) {
        console.warn('API调用失败', apiError);
      }
    } catch (error) {
      console.error('Load brands failed:', error);
      message.error('获取品牌列表失败，请检查网络连接或联系管理员');
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    if (canRead) {
      // 直接调用函数，避免依赖问题
      loadData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [canRead]); // 只依赖权限状态

  // 事件处理方法
  const handleAdd = () => {
    setEditModalVisible(true);
    setEditingBrand(null);
  };

  const handleEdit = (record) => {
    setEditModalVisible(true);
    setEditingBrand(record);
  };

  const handleDelete = async (record) => {
    try {
      const response = await brandAPI.deleteBrand(record.id);
      if (response.success) {
        message.success('删除成功');
        loadData(); // 重新加载数据
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      console.error('Delete brand failed:', error);
      message.error(`删除失败:${ error.message}`);
    }
  };

  const handleBatchDelete = () => {
    const newDataSource = dataSource.filter((item) => !selectedRowKeys.includes(item.id));
    setDataSource(newDataSource);
    setSelectedRowKeys([]);
    message.success(`批量删除成功，共删除${selectedRowKeys.length}条记录`);
  };

  const handleModalCancel = () => {
    setEditModalVisible(false);
    setEditingBrand(null);
  };

  const handleFormSuccess = () => {
    loadData(); // 重新加载数据
  };

  const handleSearch = () => {
    loadData({ page: 1 });
  };

  const handleReset = () => {
    setSearchFilters({
      name: undefined,
      status: undefined,
    });
    setTimeout(() => {
      loadData({ page: 1 });
    }, 0);
  };

  // 实时搜索（防抖）
  const handleInstantSearch = () => {
    if (window.searchTimer) {
      clearTimeout(window.searchTimer);
    }
    window.searchTimer = setTimeout(() => {
      handleSearch();
    }, 500); // 500ms防抖
  };

  const handleSearchChange = (field, value) => {
    setSearchFilters((prev) => ({
      ...prev,
      [field]: value,
    }));

    // 对于下拉选择器，立即搜索
    if (field !== 'name') {
      setTimeout(() => handleSearch(), 0);
    } else {
      // 对于文本输入，使用防抖搜索
      handleInstantSearch();
    }
  };

  const toggleStatus = async (record) => {
    const newStatus = record.status === 'active' ? 'inactive' : 'active';
    const response = await brandAPI.updateBrand({ id: record.id, status: newStatus });
    if (response.success) {
      message.success(`品牌已${newStatus === 'active' ? '启用' : '禁用'}`);
      loadData(); // 重新加载数据
    } else {
      message.error(response.message || '操作失败');
    }
  };

  // 权限检查 - 如果没有读取权限，显示无权限提示
  if (!canRead) {
    return (
      <div style={{ padding: '20px' }}>
        <Alert
          message="无权限访问"
          description="您没有权限查看品牌管理页面"
          type="warning"
          showIcon
        />
      </div>
    );
  }

  // 表格列定义（包含权限控制）
  const columns = [
    {
      title: '品牌名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      fixed: 'left',
      render: (text) => (
        <div>
          <div style={{ fontWeight: 'bold', color: '#1890ff' }}>{text}</div>
        </div>
      ),
    },
    {
      title: '品牌描述',
      dataIndex: 'description',
      key: 'description',
      width: 300,
      render: (text) => (
        <div style={{
          maxWidth: '280px',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          white: 'nowrap',
        }}
        >
          {text || '-'}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={status === 'active' ? '#52c41a' : '#f5222d'}>
          <Icon type={status === 'active' ? 'check-circle' : 'close-circle'} />
          &nbsp;
          {status === 'active' ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160,
      render: (time) => (
        <div style={{ fontSize: '12px' }}>
          {time ? moment(time).format('YYYY-MM-DD HH:mm:ss') : '-'}
        </div>
      ),
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 160,
      render: (time) => (
        <div style={{ fontSize: '12px' }}>
          {time ? moment(time).format('YYYY-MM-DD HH:mm:ss') : '-'}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 250,
      fixed: 'right',
      render: (_, record) => (
        <>
          <ButtonGuard permissions={['brand.update']}>
            <Button
              type="link"
              size="small"
              onClick={() => handleEdit(record)}
              style={{ padding: '0 4px' }}
            >
              <Icon type="edit" />
              编辑
            </Button>
          </ButtonGuard>

          <ButtonGuard permissions={['brand.update']}>
            <Button
              type="link"
              size="small"
              onClick={() => toggleStatus(record)}
              style={{
                padding: '0 4px',
                color: record.status === 'active' ? '#fa8c16' : '#52c41a',
              }}
            >
              <Icon type={record.status === 'active' ? 'stop' : 'play-circle'} />
              {record.status === 'active' ? '禁用' : '启用'}
            </Button>
          </ButtonGuard>

          <ButtonGuard permissions={['brand.delete']}>
            <Popconfirm
              title="确定要删除这个品牌吗？"
              onConfirm={() => handleDelete(record)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="link"
                size="small"
                style={{ color: '#f5222d', padding: '0 4px' }}
              >
                <Icon type="delete" />
                删除
              </Button>
            </Popconfirm>
          </ButtonGuard>
        </>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (keys) => {
      setSelectedRowKeys(keys);
    },
    getCheckboxProps: () => ({
      disabled: !canDelete, // 没有删除权限时禁用选择
    }),
  };

  return (
    <PageGuard permissions={PAGE_PERMISSIONS.BRAND_MANAGEMENT}>
      <div style={{ padding: '0px' }}>
        <Card
          title="品牌管理"
          extra={
            <ButtonGuard permissions={PAGE_PERMISSIONS.BRAND_CREATE}>
              <Button
                type="primary"
                onClick={handleAdd}
                icon="plus"
              >
                新建品牌
              </Button>
            </ButtonGuard>
          }
        >
          {/* 搜索筛选区域 */}
          <div style={{
            marginBottom: 16,
            padding: 16,
            background: '#fafafa',
            borderRadius: 6,
            border: '1px solid #e8e8e8',
          }}
          >
            <Row gutter={16} align="middle">
              <Col span={6}>
                <Input
                  placeholder="搜索品牌名称（支持实时搜索）"
                  value={searchFilters.name}
                  onChange={(e) => handleSearchChange('name', e.target.value)}
                  onPressEnter={handleSearch}
                  allowClear
                />
              </Col>
              <Col span={4}>
                <Select
                  placeholder="选择状态"
                  value={searchFilters.status}
                  onChange={(value) => handleSearchChange('status', value)}
                  style={{ width: '100%' }}
                  allowClear
                >
                  <Select.Option value="active">启用</Select.Option>
                  <Select.Option value="inactive">禁用</Select.Option>
                </Select>
              </Col>
              <Col span={6}>
                <div>
                  <Button
                    type="primary"
                    onClick={handleSearch}
                    style={{ marginRight: 8 }}
                    loading={loading}
                  >
                    搜索
                  </Button>
                  <Button onClick={handleReset} style={{ marginRight: 8 }}>
                    重置
                  </Button>
                  <Button onClick={() => loadData()}>
                    刷新
                  </Button>
                </div>
              </Col>
            </Row>
          </div>

          {/* 批量操作区域 */}
          <FeatureGuard permissions={['brand.delete']}>
            {selectedRowKeys.length > 0 && (
              <div style={{ marginBottom: 16 }}>
                <Popconfirm
                  title={`确定要删除选中的${selectedRowKeys.length}个品牌吗？`}
                  onConfirm={handleBatchDelete}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button type="danger" style={{ marginRight: 8 }}>
                    <Icon type="delete" />
                    批量删除 ({selectedRowKeys.length})
                  </Button>
                </Popconfirm>
              </div>
            )}
          </FeatureGuard>

          {/* 表格 */}
          <Table
            columns={columns}
            dataSource={dataSource}
            rowKey="id"
            loading={loading}
            rowSelection={canDelete ? rowSelection : null}
            scroll={{ x: 1000 }}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: (page, pageSize) => {
                loadData({ page, pageSize });
              },
            }}
          />
        </Card>

        {/* 编辑/新建弹窗 */}
        <FeatureGuard permissions={['brand.create', 'brand.update']}>
          <BrandFormModal
            visible={editModalVisible}
            editingBrand={editingBrand}
            onCancel={handleModalCancel}
            onSuccess={handleFormSuccess}
          />
        </FeatureGuard>
      </div>
    </PageGuard>
  );
};

export default BrandManagement;
