import React, { Component } from 'react';
import { Card, Button, message } from 'antd';
import WeeklyBudgetMultiForm from './WeeklyBudgetMultiForm';

class WeeklyBudgetMultiFormTest extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showForm: false,
    };
  }

  // 模拟项目信息
  mockProjectInfo = {
    projectName: '测试项目-标题自动生成',
    executionPeriod: ['2024-01-01', '2024-12-31'],
  };

  handleShowForm = () => {
    this.setState({ showForm: true });
  };

  handleFormSubmit = (data) => {
    console.log('提交的多预算数据:', data);
    message.success(`成功创建 ${data.length} 个周预算`);
    this.setState({ showForm: false });
  };

  handleFormCancel = () => {
    this.setState({ showForm: false });
  };

  render() {
    const { showForm } = this.state;

    if (showForm) {
      return (
        <WeeklyBudgetMultiForm
          projectId="test-project-id"
          projectInfo={this.mockProjectInfo}
          onSubmit={this.handleFormSubmit}
          onCancel={this.handleFormCancel}
        />
      );
    }

    return (
      <div style={{ padding: '20px' }}>
        <Card title="多预算表单测试 - 紧凑表格式布局">
          <p>这是一个测试页面，用于验证多预算表单的功能。</p>
          <p><strong>新版本特点（表格式布局）：</strong></p>
          <ul>
            <li>✅ <strong>紧凑布局</strong>：使用表格式布局，便于多笔预算对比</li>
            <li>✅ <strong>一目了然</strong>：所有预算项目在同一视图中，方便横向对比</li>
            <li>✅ <strong>实时汇总</strong>：显示预算总数、总金额、完成状态统计</li>
            <li>✅ <strong>自动化流程</strong>：选择费用类型→自动生成标题，选择供应商→自动填入税率</li>
            <li>✅ <strong>灵活操作</strong>：可以添加和删除预算项目，支持批量提交</li>
            <li>✅ <strong>状态跟踪</strong>：实时显示已填写和待完善的项目数量</li>
          </ul>

          <div style={{ marginTop: 16 }}>
            <Button type="primary" size="large" onClick={this.handleShowForm}>
              🚀 体验新版多预算表单
            </Button>
          </div>

          <div style={{ marginTop: 16, padding: '12px', backgroundColor: '#f0f9ff', borderRadius: '6px', border: '1px solid #bae7ff' }}>
            <p style={{ margin: 0, color: '#1890ff' }}>
              <strong>💡 使用提示：</strong>
              新版本采用表格式布局，所有预算项目在同一个表格中显示，便于快速填写和对比。
              支持横向滚动查看所有字段，底部有实时汇总信息。
            </p>
          </div>

          <div style={{ marginTop: 12, padding: '12px', backgroundColor: '#fff7e6', borderRadius: '6px', border: '1px solid #ffd591' }}>
            <p style={{ margin: 0, color: '#d46b08' }}>
              <strong>🔧 测试标题自动生成：</strong>
              1. 先选择周期 → 2. 再选择费用类型 → 3. 查看标题是否自动生成
              <br />
              如果标题没有自动生成，请打开浏览器控制台查看调试信息。
            </p>
          </div>
        </Card>
      </div>
    );
  }
}

export default WeeklyBudgetMultiFormTest;
