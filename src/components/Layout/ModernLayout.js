import React, { useState, useEffect } from 'react';
import { Layout, Menu, Icon, Avatar, Dropdown, Badge, Breadcrumb, Button, message } from 'antd';
import { useStore } from '../../store';
import { withRouter } from 'react-router-dom';
import authService from '../../services/auth';
import './ModernLayout.css';

const { Header, Sider, Content } = Layout;
const { SubMenu } = Menu;

const ModernLayout = ({ children, onLogout, history, location }) => {
  const { state } = useStore();
  const { currentUser } = state;


  const getSelectedKey = () => {
    const { pathname } = location;
    if (pathname === '/') return 'dashboard';
    if (pathname === '/projects' || pathname.startsWith('/projects/')) return 'project-list';
    if (pathname.startsWith('/brands')) return 'brand-management';
    if (pathname.startsWith('/suppliers')) return 'suppliers';
    if (pathname.startsWith('/system')) return 'system-management';
    if (pathname.startsWith('/demo')) return 'demo';
    if (pathname.startsWith('/weekly-budget')) return 'weekly-budget';
    if (pathname.startsWith('/revenue')) return 'revenue-management';
    if (pathname.startsWith('/reports')) return 'financial-reports';
    return 'dashboard';
  };

  const getOpenKey = () => {
    const { pathname } = location;
    if (pathname === '/projects' || pathname.startsWith('/projects/')) return 'projects';
    if (pathname.startsWith('/weekly-budget') || pathname.startsWith('/revenue')) return 'finance';
    if (pathname.startsWith('/reports')) return 'reports';
    if (pathname.startsWith('/system')) return 'system';
    return '';
  };

  // 获取屏幕分辨率来判断是否为移动端
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const [collapsed, setCollapsed] = useState(isMobile);
  const [selectedKeys, setSelectedKeys] = useState([getSelectedKey()]);
  const [openKeys, setOpenKeys] = useState([getOpenKey()]);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  // 当路由变化时更新选中的菜单项
  useEffect(() => {
    setSelectedKeys([getSelectedKey()]);
    setOpenKeys([getOpenKey()]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.pathname]);

  const toggle = () => {
    setCollapsed((prev) => !prev);
  };

  const handleMenuClick = ({ key }) => {
    setSelectedKeys([key]);

    // 路由导航
    switch (key) {
      case 'dashboard':
        history.push('/');
        break;
      case 'project-list':
        history.push('/projects');
        break;
      case 'project-create':
        history.push('/projects?tab=form');
        break;
      case 'brand-management':
        history.push('/brands');
        break;
      case 'suppliers':
        history.push('/suppliers');
        break;
      case 'system-management':
        history.push('/system');
        break;
      case 'weekly-budget':
        history.push('/projects?tab=weekly-budget');
        break;
      case 'revenue-management':
        history.push('/projects?tab=revenue');
        break;
      case 'financial-reports':
        history.push('/reports');
        break;
      case 'project-analysis':
        history.push('/reports?tab=analysis');
        break;
      case 'demo':
        history.push('/demo');
        break;
      default:
        break;
    }
  };


  const handleOpenChange = (keys) => {
    setOpenKeys(keys);
  };

  const handleRefreshAuth = async () => {
    try {
      message.loading('正在刷新认证状态...', 0);
      const result = await authService.forceReauth();
      message.destroy();

      if (result.success) {
        message.success('认证状态刷新成功');
        // 刷新页面以更新用户信息
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        message.error('认证状态刷新失败');
      }
    } catch (error) {
      message.destroy();
      message.error(`认证刷新失败: ${error.message}`);
      console.error('刷新认证失败:', error);
    }
  };

  const handleDebugClearAuth = async () => {
    try {
      message.loading('正在清除认证信息并重新登录...', 0);
      const result = await authService.debugClearAndReauth();
      message.destroy();

      if (result.success) {
        message.success('重新认证成功');
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        message.error('重新认证失败');
      }
    } catch (error) {
      message.destroy();
      message.error(`重新认证失败: ${error.message}`);
      console.error('重新认证失败:', error);
    }
  };

  const getBreadcrumbItems = () => {
    const { pathname } = location;
    const items = [{ title: '首页', href: '/', id: 'home' }];

    if (pathname === '/') {
      items.push({ title: '工作台', id: 'dashboard' });
    } else if (pathname.startsWith('/projects')) {
      items.push({ title: '项目管理', id: 'projects' });
    } else if (pathname.startsWith('/brands')) {
      items.push({ title: '品牌管理', id: 'brands' });
    } else if (pathname.startsWith('/suppliers')) {
      items.push({ title: '供应商管理', id: 'suppliers' });
    } else if (pathname.startsWith('/system')) {
      items.push({ title: '系统管理', id: 'system' });
    } else if (pathname.startsWith('/demo')) {
      items.push({ title: '组件演示', id: 'demo' });
    } else if (pathname.startsWith('/reports')) {
      items.push({ title: '报表分析', id: 'reports' });
    }

    return items;
  };
  const userMenu = (
    <Menu>
      <Menu.Item key="profile">
        <Icon type="user" />
        个人资料
      </Menu.Item>
      <Menu.Item key="settings">
        <Icon type="setting" />
        系统设置
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="refresh-auth" onClick={handleRefreshAuth}>
        <Icon type="reload" />
        刷新认证
      </Menu.Item>
      <Menu.Item key="debug-clear-auth" onClick={handleDebugClearAuth}>
        <Icon type="bug" />
        调试重新认证
      </Menu.Item>
      <Menu.Item key="logout" onClick={onLogout}>
        <Icon type="logout" />
        退出登录
      </Menu.Item>
    </Menu>
  );

  return (
    <Layout className="modern-layout">
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        className="modern-sider"
        width={280}
        collapsedWidth={80}
      >
        <div className="modern-logo">
          <div className="logo-icon">
            <Icon type="fund" />
          </div>
          {!collapsed && (
            <div className="logo-text">
              <div className="logo-title">财务管理</div>
              <div className="logo-subtitle">Project Finance</div>
            </div>
          )}
        </div>

        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={selectedKeys}
          openKeys={openKeys}
          onSelect={handleMenuClick}
          onOpenChange={handleOpenChange}
          className="modern-menu"
        >
          <Menu.Item key="dashboard">
            <Icon type="dashboard" />
            <span>工作台</span>
          </Menu.Item>
          <Menu.Item key="project-list">
            <Icon type="unordered-list" />
            <span>项目列表</span>
          </Menu.Item>
          <Menu.Item key="brand-management">
            <Icon type="tags" />
            <span>品牌管理</span>
          </Menu.Item>

          <Menu.Item key="suppliers">
            <Icon type="shop" />
            <span>供应商管理</span>
          </Menu.Item>

          <Menu.Item key="system-management">
            <Icon type="setting" />
            <span>系统管理</span>
          </Menu.Item>

          <SubMenu
            key="reports"
            title={
              <span>
                <Icon type="bar-chart" />
                <span>报表分析</span>
              </span>
            }
          >
            <Menu.Item key="financial-reports">
              <Icon type="line-chart" />
              财务报表
            </Menu.Item>
            <Menu.Item key="project-analysis">
              <Icon type="pie-chart" />
              项目分析
            </Menu.Item>
          </SubMenu>

          <Menu.Divider />

          {/* <Menu.Item key="demo">
            <Icon type="experiment" />
            <span>组件演示</span>
          </Menu.Item> */}
        </Menu>

        {/* expanded */}
        {!collapsed && (
          <div className="expand-button" onClick={toggle}>
            <Icon type="menu-unfold" />
          </div>
        )}
        {/* collapsed */}
        {collapsed && (
          <div className="expand-button" onClick={toggle}>
            <Icon type="menu-fold" />
          </div>
        )}
      </Sider>

      <Layout className="modern-content-layout">
        <Header className="modern-header">
          <div className="header-left">
            <Button
              type="link"
              icon={collapsed ? 'menu-unfold' : 'menu-fold'}
              onClick={toggle}
              className="trigger"
            />

            <Breadcrumb className="breadcrumb">
              {getBreadcrumbItems().map((item) => (
                <Breadcrumb.Item key={item.id} href={item.href}>
                  {item.title}
                </Breadcrumb.Item>
              ))}
            </Breadcrumb>
          </div>

          <div className="header-right">
            <div className="header-actions">
              <Badge count={5} className="notification-badge">
                <Button type="link" icon="bell" className="action-button" />
              </Badge>

              {/* 跳转路由 */}
              <Button type="link" icon="question-circle" className="action-button" onClick={() => history.push('/permission-demo')} />
              <Dropdown overlay={userMenu} placement="bottomRight">
                <div className="user-info">
                  <Avatar
                    size="small"
                    icon="user"
                    src={currentUser?.avatar}
                    className="user-avatar"
                  />
                  <span className="user-name">{currentUser?.name || '用户'}</span>
                  <Icon type="down" className="user-dropdown-icon" />
                </div>
              </Dropdown>
            </div>
          </div>
        </Header>

        <Content className="modern-content">
          <div className="content-wrapper">
            {children}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default withRouter(ModernLayout);
