import React from 'react';
import { Dropdown, Avatar, Space } from 'antd';
import { UserOutlined, LogoutOutlined, SettingOutlined } from '@ant-design/icons';
import authService from '../../services/auth';
import PermissionRefreshButton from '../Auth/PermissionRefreshButton';

/**
 * 用户菜单组件
 * 包含用户信息、权限刷新、登出等功能
 */
const UserMenu = () => {
  const userInfo = authService.getUserInfo();

  const handleLogout = async () => {
    try {
      await authService.logout();
    } catch (error) {
      console.error('登出失败:', error);
    }
  };

  const menuItems = [
    {
      key: 'userInfo',
      label: (
        <div style={{ padding: '8px 0' }}>
          <div style={{ fontWeight: 'bold' }}>{userInfo?.name || '未知用户'}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {userInfo?.department || '未知部门'}
          </div>
        </div>
      ),
      disabled: true,
    },
    {
      type: 'divider',
    },
    {
      key: 'refreshPermissions',
      label: (
        <Space>
          <PermissionRefreshButton showText tooltip="刷新我的权限信息" />
        </Space>
      ),
    },
    {
      key: 'settings',
      label: '个人设置',
      icon: <SettingOutlined />,
      disabled: true, // 暂时禁用
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      label: '退出登录',
      icon: <LogoutOutlined />,
      onClick: handleLogout,
    },
  ];

  return (
    <Dropdown
      menu={{ items: menuItems }}
      placement="bottomRight"
      trigger={['click']}
    >
      <Space style={{ cursor: 'pointer', padding: '0 16px' }}>
        <Avatar size="small" icon={<UserOutlined />} />
        <span>{userInfo?.name || '用户'}</span>
      </Space>
    </Dropdown>
  );
};

export default UserMenu;
