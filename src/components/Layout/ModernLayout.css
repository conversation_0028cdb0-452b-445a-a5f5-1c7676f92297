/* 现代化布局样式 */
.modern-layout {
  height: 100vh;
  background: var(--color-bg-secondary);
  overflow: hidden;
}

/* 侧边栏样式 */
.modern-sider {
  background: var(--color-bg-dark) !important;
  box-shadow: var(--shadow-lg);
  position: relative;
  z-index: 100;
  transition: all var(--animation-duration-base) ease;
  height: 100vh !important;
  overflow-y: auto;
}

.modern-sider .ant-layout-sider-trigger {
  display: none;
}

/* Logo 区域 */
.modern-logo {
  height: 64px;
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-lg);
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: var(--spacing-base);
}

.logo-icon {
  width: 32px;
  height: 32px;
  background: var(--gradient-primary);
  border-radius: var(--border-radius-base);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  flex-shrink: 0;
}

.logo-text {
  margin-left: var(--spacing-base);
  color: white;
  overflow: hidden;
}

.logo-title {
  font-size: 16px;
  font-weight: var(--font-weight-semibold);
  line-height: 1.2;
  margin-bottom: 2px;
}

.logo-subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1;
}

/* 菜单样式 */
.modern-menu {
  border-right: none !important;
  background: transparent !important;
  padding: 0 var(--spacing-base);
  height: calc(100vh - 94px);
}

.modern-menu .ant-menu-item,
.modern-menu .ant-menu-submenu-title {
  margin: 4px 0 !important;
  border-radius: var(--border-radius-base) !important;
  height: 44px !important;
  line-height: 44px !important;
  padding-left: var(--spacing-base) !important;
  transition: all var(--animation-duration-base) ease !important;
}

.modern-menu .ant-menu-item:hover,
.modern-menu .ant-menu-submenu-title:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: white !important;
}

.modern-menu .ant-menu-item-selected {
  background: var(--gradient-primary) !important;
  color: white !important;
  box-shadow: var(--shadow-sm);
}

.modern-menu .ant-menu-submenu-open > .ant-menu-submenu-title {
  background: rgba(255, 255, 255, 0.05) !important;
}

.modern-menu .ant-menu-sub {
  background: rgba(0, 0, 0, 0.2) !important;
  border-radius: var(--border-radius-base);
  margin: 4px 0;
  padding: var(--spacing-sm) 0;
}

.modern-menu .ant-menu-sub .ant-menu-item {
  padding-left: var(--spacing-xl) !important;
  margin: 2px var(--spacing-sm) !important;
  width: calc(100% - var(--spacing-base));
}

.modern-menu .ant-menu-item-divider {
  background: rgba(255, 255, 255, 0.1);
  margin: var(--spacing-base) var(--spacing-base);
}

/* 内容布局 */
.modern-content-layout {
  background: var(--color-bg-secondary);
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.modern-header {
  background: var(--color-bg-primary) !important;
  padding: 0 var(--spacing-lg) !important;
  box-shadow: var(--shadow-sm);
  border-bottom: 1px solid var(--color-border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px !important;
  min-height: 64px !important;
  max-height: 64px !important;
  flex-shrink: 0;
  z-index: 99;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.trigger {
  font-size: 18px !important;
  color: var(--color-text-secondary) !important;
  padding: var(--spacing-sm) !important;
  border-radius: var(--border-radius-base) !important;
  transition: all var(--animation-duration-base) ease !important;
}

.trigger:hover {
  background: var(--color-bg-tertiary) !important;
  color: var(--color-primary) !important;
}

.breadcrumb {
  margin: 0;
}

.breadcrumb .ant-breadcrumb-link {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.breadcrumb .ant-breadcrumb-link:hover {
  color: var(--color-primary);
}

.header-right {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-base);
}

.action-button {
  width: 40px !important;
  height: 40px !important;
  border-radius: var(--border-radius-base) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: var(--color-text-secondary) !important;
  font-size: 16px !important;
  transition: all var(--animation-duration-base) ease !important;
}

.action-button:hover {
  background: var(--color-bg-tertiary) !important;
  color: var(--color-primary) !important;
}

.notification-badge .ant-badge-count {
  background: var(--gradient-error);
  border: none;
  box-shadow: var(--shadow-sm);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-base);
  border-radius: var(--border-radius-base);
  cursor: pointer;
  transition: all var(--animation-duration-base) ease;
}

.user-info:hover {
  background: var(--color-bg-tertiary);
}

.user-avatar {
  border: 2px solid var(--color-border-light);
}

.user-name {
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-dropdown-icon {
  color: var(--color-text-tertiary);
  font-size: 12px;
  transition: transform var(--animation-duration-base) ease;
}

.user-info:hover .user-dropdown-icon {
  transform: rotate(180deg);
}

/* 内容区域 */
.modern-content {
  margin: 0;
  padding: 0;
  flex: 1;
  overflow-y: auto;
  background: var(--color-bg-secondary);
  height: calc(100vh - 64px);
  min-height: calc(100vh - 64px);
}

.content-wrapper {
  padding: var(--spacing-lg);
  /* max-width: 1400px; */
  margin: 0 auto;
  width: 100%;
  min-height: 100%;
  box-sizing: border-box;
}

/* 底部样式 */
.modern-footer {
  background: var(--color-bg-primary) !important;
  border-top: 1px solid var(--color-border-light);
  padding: var(--spacing-base) var(--spacing-lg) !important;
  flex-shrink: 0;
  height: 64px;
  display: flex;
  align-items: center;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.footer-left,
.footer-right {
  color: var(--color-text-tertiary);
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-header {
    padding: 0 var(--spacing-base) !important;
  }
  
  .header-left {
    gap: var(--spacing-base);
  }
  
  .breadcrumb {
    display: none;
  }
  
  .user-name {
    display: none;
  }
  
  .content-wrapper {
    padding: var(--spacing-base);
  }
  
  .footer-content {
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
  }
}

@media (max-width: 576px) {
  .modern-sider {
    position: fixed !important;
    height: 100vh;
    z-index: 1000;
  }
  
  .modern-content-layout {
    margin-left: 0 !important;
  }
  
  .header-actions {
    gap: var(--spacing-sm);
  }
  
  .action-button {
    width: 36px !important;
    height: 36px !important;
    font-size: 14px !important;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .modern-layout {
    background: #141414;
  }
  
  .modern-header {
    background: #1f1f1f !important;
    border-bottom-color: #303030;
  }
  
  .modern-content {
    background: #141414;
  }
  
  .modern-footer {
    background: #1f1f1f !important;
    border-top-color: #303030;
  }
}

.expand-button {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}