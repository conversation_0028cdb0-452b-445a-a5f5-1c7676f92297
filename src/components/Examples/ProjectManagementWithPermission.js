import React, { useState } from 'react';
import {
  Card,
  Button,
  Table,
  // div,
  Tag,
  Popconfirm,
  message,
  Row,
  Col,
  Input,
  Select,
  Alert,
  Divider,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  CheckOutlined,
  ExportOutlined,
  DollarOutlined,
  CalendarOutlined,
  HistoryOutlined,
  // SearchOutlined,
} from '@ant-design/icons';
import { PageGuard, FeatureGuard, ButtonGuard } from '../Permission/PermissionGuard';
import { useFeaturePermission } from '../../hooks/usePermission';
import { PAGE_PERMISSIONS } from '../../config/permissions';

const { Option } = Select;
const { Search } = Input;

/**
 * 带权限控制的项目管理组件
 * 演示如何在实际业务组件中应用权限控制
 */
const ProjectManagementWithPermission = () => {
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  // 获取项目管理的功能权限
  const {
    canRead,
    canCreate,
    canUpdate,
    canDelete,
    canApprove,
    canExport,
    isSuperAdmin,
  } = useFeaturePermission('project');

  // 模拟项目数据
  const mockProjects = [
    {
      id: 1,
      projectName: '春季营销活动',
      brandName: '品牌A',
      executionPeriod: ['2024-03-01', '2024-05-31'],
      planningBudget: 500000,
      projectProfit: 120000,
      grossMargin: 0.24,
      executivePM: '张三',
      contractType: 'FIXED',
      contractSigningStatus: 'SIGNED',
      status: 'EXECUTING',
      createTime: '2024-02-15',
    },
    {
      id: 2,
      projectName: '品牌推广项目',
      brandName: '品牌B',
      executionPeriod: ['2024-04-01', '2024-06-30'],
      planningBudget: 800000,
      projectProfit: 200000,
      grossMargin: 0.25,
      executivePM: '李四',
      contractType: 'PERCENTAGE',
      contractSigningStatus: 'PENDING',
      status: 'PENDING',
      createTime: '2024-03-01',
    },
    {
      id: 3,
      projectName: '产品发布会',
      brandName: '品牌C',
      executionPeriod: ['2024-05-01', '2024-05-15'],
      planningBudget: 300000,
      projectProfit: 80000,
      grossMargin: 0.27,
      executivePM: '王五',
      contractType: 'FIXED',
      contractSigningStatus: 'SIGNED',
      status: 'COMPLETED',
      createTime: '2024-04-10',
    },
  ];

  // 过滤数据
  const filteredData = mockProjects.filter((project) => {
    const matchesSearch = !searchText ||
      project.projectName.toLowerCase().includes(searchText.toLowerCase()) ||
      project.brandName.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = !statusFilter || project.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // 表格列定义
  const columns = [
    {
      title: '项目名称',
      dataIndex: 'projectName',
      key: 'projectName',
      width: 200,
      fixed: 'left',
    },
    {
      title: '品牌',
      dataIndex: 'brandName',
      key: 'brandName',
      width: 120,
    },
    {
      title: '执行周期',
      dataIndex: 'executionPeriod',
      key: 'executionPeriod',
      width: 200,
      render: (period) => {
        if (Array.isArray(period) && period.length === 2) {
          return `${period[0]} ~ ${period[1]}`;
        }
        return '-';
      },
    },
    {
      title: '规划预算',
      dataIndex: 'planningBudget',
      key: 'planningBudget',
      width: 120,
      render: (amount) => `¥${amount.toLocaleString()}`,
    },
    {
      title: '项目利润',
      dataIndex: 'projectProfit',
      key: 'projectProfit',
      width: 120,
      render: (profit) => (
        <span style={{ color: profit >= 0 ? '#52c41a' : '#f5222d' }}>
          ¥{profit.toLocaleString()}
        </span>
      ),
    },
    {
      title: '毛利率',
      dataIndex: 'grossMargin',
      key: 'grossMargin',
      width: 100,
      render: (margin) => (
        <span style={{ color: margin >= 0 ? '#52c41a' : '#f5222d' }}>
          {(margin * 100).toFixed(1)}%
        </span>
      ),
    },
    {
      title: '执行PM',
      dataIndex: 'executivePM',
      key: 'executivePM',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => {
        const statusMap = {
          EXECUTING: { color: 'processing', text: '执行中' },
          PENDING: { color: 'warning', text: '待审批' },
          COMPLETED: { color: 'success', text: '已完成' },
          CANCELLED: { color: 'error', text: '已取消' },
        };
        const config = statusMap[status] || { color: 'default', text: status };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 120,
    },
    {
      title: '操作',
      key: 'action',
      width: 300,
      fixed: 'right',
      render: (_, record) => (
        <div size="small" wrap>
          <FeatureGuard permissions={['project.read']}>
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => message.info(`查看项目: ${record.projectName}`)}
            >
              详情
            </Button>
          </FeatureGuard>

          <ButtonGuard permissions={['project.update']}>
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => message.info(`编辑项目: ${record.projectName}`)}
            >
              编辑
            </Button>
          </ButtonGuard>

          <FeatureGuard permissions={['finance.read']}>
            <Button
              type="link"
              size="small"
              icon={<DollarOutlined />}
              style={{ color: '#52c41a' }}
              onClick={() => message.info(`收入管理: ${record.projectName}`)}
            >
              收入
            </Button>
          </FeatureGuard>

          <FeatureGuard permissions={['budget.read']}>
            <Button
              type="link"
              size="small"
              icon={<CalendarOutlined />}
              style={{ color: '#1890ff' }}
              onClick={() => message.info(`周预算: ${record.projectName}`)}
            >
              周预算
            </Button>
          </FeatureGuard>

          <FeatureGuard permissions={['project.approve']}>
            <Button
              type="link"
              size="small"
              icon={<CheckOutlined />}
              style={{ color: '#722ed1' }}
              onClick={() => message.success(`审批项目: ${record.projectName}`)}
            >
              审批
            </Button>
          </FeatureGuard>

          <FeatureGuard permissions={['project.read']}>
            <Button
              type="link"
              size="small"
              icon={<HistoryOutlined />}
              onClick={() => message.info(`变更日志: ${record.projectName}`)}
            >
              日志
            </Button>
          </FeatureGuard>

          <ButtonGuard permissions={['project.delete']}>
            <Popconfirm
              title="确定要删除这个项目吗？"
              onConfirm={() => message.success(`删除项目: ${record.projectName}`)}
            >
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
              >
                删除
              </Button>
            </Popconfirm>
          </ButtonGuard>
        </div>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: () => ({
      disabled: !canDelete, // 没有删除权限时禁用选择
    }),
  };

  return (
    <PageGuard permissions={PAGE_PERMISSIONS.PROJECT_MANAGEMENT}>
      <div style={{ padding: '24px' }}>
        <h2>项目管理（权限控制版）</h2>

        {/* 权限状态提示 */}
        <Alert
          message="权限控制演示"
          description={
            <div>
              <p><strong>当前权限状态:</strong></p>
              <div wrap>
                <Tag color={canRead ? 'green' : 'red'}>查看: {canRead ? '✓' : '✗'}</Tag>
                <Tag color={canCreate ? 'green' : 'red'}>创建: {canCreate ? '✓' : '✗'}</Tag>
                <Tag color={canUpdate ? 'green' : 'red'}>编辑: {canUpdate ? '✓' : '✗'}</Tag>
                <Tag color={canDelete ? 'green' : 'red'}>删除: {canDelete ? '✓' : '✗'}</Tag>
                <Tag color={canApprove ? 'green' : 'red'}>审批: {canApprove ? '✓' : '✗'}</Tag>
                <Tag color={canExport ? 'green' : 'red'}>导出: {canExport ? '✓' : '✗'}</Tag>
                {isSuperAdmin && <Tag color="gold">超级管理员</Tag>}
              </div>
            </div>
          }
          type="info"
          style={{ marginBottom: 24 }}
        />

        <Card>
          {/* 操作栏 */}
          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col flex="auto">
              <div>
                <ButtonGuard permissions={PAGE_PERMISSIONS.PROJECT_CREATE}>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => message.info('创建新项目')}
                  >
                    新建项目
                  </Button>
                </ButtonGuard>

                <FeatureGuard permissions={['report.export']}>
                  <Button
                    icon={<ExportOutlined />}
                    onClick={() => message.info('导出项目数据')}
                  >
                    导出数据
                  </Button>
                </FeatureGuard>

                <ButtonGuard permissions={PAGE_PERMISSIONS.PROJECT_DELETE}>
                  <Button
                    danger
                    disabled={selectedRowKeys.length === 0}
                    onClick={() => {
                      message.success(`批量删除 ${selectedRowKeys.length} 个项目`);
                      setSelectedRowKeys([]);
                    }}
                  >
                    批量删除 ({selectedRowKeys.length})
                  </Button>
                </ButtonGuard>
              </div>
            </Col>
            <Col>
              <div>
                <Search
                  placeholder="搜索项目名称或品牌"
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  style={{ width: 200 }}
                  allowClear
                />
                <Select
                  placeholder="状态筛选"
                  value={statusFilter}
                  onChange={setStatusFilter}
                  style={{ width: 120 }}
                  allowClear
                >
                  <Option value="EXECUTING">执行中</Option>
                  <Option value="PENDING">待审批</Option>
                  <Option value="COMPLETED">已完成</Option>
                  <Option value="CANCELLED">已取消</Option>
                </Select>
              </div>
            </Col>
          </Row>

          <Divider />

          {/* 数据表格 */}
          <FeatureGuard
            permissions={PAGE_PERMISSIONS.PROJECT_MANAGEMENT}
            fallback={
              <Alert
                message="无权限访问"
                description="您没有权限查看项目数据"
                type="warning"
                showIcon
              />
            }
          >
            <Table
              columns={columns}
              dataSource={filteredData}
              rowKey="id"
              rowSelection={canDelete ? rowSelection : null}
              scroll={{ x: 1500 }}
              pagination={{
                total: filteredData.length,
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
              }}
            />
          </FeatureGuard>
        </Card>

        {/* 权限说明 */}
        <Card title="权限控制说明" size="small" style={{ marginTop: 16 }}>
          <div style={{ fontSize: '12px', color: '#666' }}>
            <h4>本页面演示的权限控制功能：</h4>
            <ul>
              <li><strong>页面访问控制</strong>: 使用 PageGuard 保护整个页面</li>
              <li><strong>功能按钮控制</strong>: 使用 ButtonGuard 控制按钮的启用/禁用状态</li>
              <li><strong>功能区域控制</strong>: 使用 FeatureGuard 控制功能区域的显示/隐藏</li>
              <li><strong>表格操作控制</strong>: 根据权限动态显示操作按钮</li>
              <li><strong>批量操作控制</strong>: 根据删除权限控制行选择功能</li>
              <li><strong>降级显示</strong>: 无权限时显示友好的提示信息</li>
            </ul>

            <h4>权限检查逻辑：</h4>
            <ul>
              <li>页面级权限: project.read</li>
              <li>创建权限: project.create</li>
              <li>编辑权限: project.update</li>
              <li>删除权限: project.delete</li>
              <li>审批权限: project.approve</li>
              <li>导出权限: report.export</li>
              <li>财务权限: finance.read</li>
              <li>预算权限: budget.read</li>
            </ul>
          </div>
        </Card>
      </div>
    </PageGuard>
  );
};

export default ProjectManagementWithPermission;
