import React, { useEffect, useState } from 'react';
import { Card, Button, Table, Tag, message, Select, Input } from 'antd';
import { 
  useUsers, 
  useDepartments, 
  useRoles, 
  useNameMappings, 
  useStats 
} from '../../store/hooks';

const { Option } = Select;
const { Search } = Input;

const StoreUsageExample = () => {
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState(undefined);

  // 使用 store hooks
  const {
    users,
    loading: usersLoading,
    error: usersError,
    fetchUsers,
    searchUsers,
    getUsersByDepartment,
  } = useUsers();

  const {
    departments,
    getDepartmentName,
  } = useDepartments();

  const {
    roles,
    getRoleName,
  } = useRoles();

  const {
    getUserName,
  } = useNameMappings();

  const {
    userStats,
    roleStats,
  } = useStats();

  // 组件挂载时加载数据
  useEffect(() => {
    // 这里可以加载真实数据，现在使用模拟数据
    console.log('Store initialized');
  }, []);

  // 获取过滤后的用户列表
  const getFilteredUsers = () => {
    let filteredUsers = users;

    // 按关键词搜索
    if (searchKeyword) {
      filteredUsers = searchUsers(searchKeyword);
    }

    // 按部门筛选
    if (selectedDepartment) {
      filteredUsers = getUsersByDepartment(selectedDepartment);
    }

    return filteredUsers;
  };

  // 模拟添加用户
  const handleAddUser = () => {
    const newUser = {
      id: Date.now(),
      name: `用户${Date.now()}`,
      mobile: `138${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`,
      departmentId: departments[Math.floor(Math.random() * departments.length)]?.id,
      roles: [roles[Math.floor(Math.random() * roles.length)]],
      status: 'active',
      lastLoginAt: new Date().toISOString(),
    };

    // 这里应该调用 createUser action
    console.log('Would create user:', newUser);
    message.success('用户创建成功（模拟）');
  };

  const columns = [
    {
      title: '用户名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '手机号',
      dataIndex: 'mobile',
      key: 'mobile',
    },
    {
      title: '部门',
      dataIndex: 'departmentId',
      key: 'department',
      render: (departmentId) => getDepartmentName(departmentId),
    },
    {
      title: '角色',
      dataIndex: 'roles',
      key: 'roles',
      render: (userRoles) => (
        <div>
          {userRoles?.map((role) => (
            <Tag key={role.id} color="blue">
              {role.name}
            </Tag>
          ))}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '正常' : '禁用'}
        </Tag>
      ),
    },
  ];

  const filteredUsers = getFilteredUsers();

  return (
    <div style={{ padding: '24px' }}>
      <h2>Store 使用示例</h2>
      
      {/* 统计信息 */}
      <div style={{ marginBottom: 24 }}>
        <Card title="统计信息" size="small">
          <p>用户总数: {userStats.total}</p>
          <p>正常用户: {userStats.active}</p>
          <p>角色总数: {roleStats.total}</p>
          <p>部门总数: {departments.length}</p>
        </Card>
      </div>

      {/* 操作区域 */}
      <Card title="用户管理示例" style={{ marginBottom: 24 }}>
        <div style={{ marginBottom: 16, display: 'flex', gap: 16 }}>
          <Search
            placeholder="搜索用户名或手机号"
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
            style={{ width: 200 }}
          />
          
          <Select
            placeholder="选择部门"
            value={selectedDepartment}
            onChange={setSelectedDepartment}
            allowClear
            style={{ width: 150 }}
          >
            {departments.map((dept) => (
              <Option key={dept.id} value={dept.id}>
                {dept.name}
              </Option>
            ))}
          </Select>
          
          <Button type="primary" onClick={handleAddUser}>
            添加用户（模拟）
          </Button>
        </div>

        {/* 用户表格 */}
        <Table
          columns={columns}
          dataSource={filteredUsers}
          rowKey="id"
          loading={usersLoading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />

        {usersError && (
          <div style={{ color: 'red', marginTop: 16 }}>
            错误: {usersError}
          </div>
        )}
      </Card>

      {/* 使用说明 */}
      <Card title="Store 使用说明" size="small">
        <div style={{ fontSize: '12px', color: '#666' }}>
          <h4>主要功能：</h4>
          <ul>
            <li>✅ 全局状态管理 - 用户、部门、角色、权限等数据</li>
            <li>✅ 自动映射 - ID 到名称的快速映射</li>
            <li>✅ 选择器函数 - 方便的数据查询和过滤</li>
            <li>✅ 自定义 Hooks - 简化组件中的状态使用</li>
            <li>✅ 异步操作 - 支持 API 调用和错误处理</li>
            <li>✅ 统计数据 - 自动计算各种统计信息</li>
          </ul>
          
          <h4>使用方式：</h4>
          <pre style={{ background: '#f5f5f5', padding: '8px', fontSize: '11px' }}>
{`// 1. 在组件中使用 hooks
const { users, loading, fetchUsers } = useUsers();
const { getDepartmentName } = useDepartments();
const { userStats } = useStats();

// 2. 获取映射名称
const departmentName = getDepartmentName(departmentId);
const userName = getUserName(userId);

// 3. 搜索和过滤
const filteredUsers = searchUsers(keyword);
const deptUsers = getUsersByDepartment(deptId);

// 4. 异步操作
await fetchUsers();
await createUser(userData);`}
          </pre>
        </div>
      </Card>
    </div>
  );
};

export default StoreUsageExample;
