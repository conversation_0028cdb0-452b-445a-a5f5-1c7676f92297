import React, { Component } from 'react';
import { Card, Button, Table, Tag, message } from 'antd';
import { withStore } from '../../store/withStore';
import StoreConsumer, { BasicStoreConsumer } from '../../store/StoreConsumer';

/**
 * 示例1：使用 HOC (withStore) 的类组件
 */
class ClassComponentWithHOC extends Component {
  handleAddUser = () => {
    const { store } = this.props;
    
    // 使用 store 中的方法
    const newUser = {
      name: `用户${Date.now()}`,
      mobile: `138${Math.floor(Math.random() * 100000000)}`,
      departmentId: 1,
      status: 'active',
    };
    
    store.createUser(newUser);
    message.success('用户创建成功');
  };

  render() {
    const { store } = this.props;
    
    if (!store) {
      return <div>Loading...</div>;
    }

    const columns = [
      {
        title: '姓名',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '手机号',
        dataIndex: 'mobile',
        key: 'mobile',
      },
      {
        title: '部门',
        dataIndex: 'departmentId',
        key: 'departmentId',
        render: (departmentId) => store.getDepartmentName(departmentId),
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: (status) => (
          <Tag color={status === 'active' ? 'green' : 'red'}>
            {status === 'active' ? '正常' : '禁用'}
          </Tag>
        ),
      },
    ];

    return (
      <Card title="HOC 方式使用 Store" extra={
        <Button type="primary" onClick={this.handleAddUser}>
          添加用户
        </Button>
      }>
        <div style={{ marginBottom: 16 }}>
          <p>用户总数: {store.users?.length || 0}</p>
          <p>部门总数: {store.departments?.length || 0}</p>
          <p>角色总数: {store.roles?.length || 0}</p>
        </div>
        
        <Table
          columns={columns}
          dataSource={store.users || []}
          rowKey="id"
          pagination={{ pageSize: 5 }}
        />
      </Card>
    );
  }
}

// 使用 HOC 包装组件
const WrappedClassComponent = withStore(ClassComponentWithHOC);

/**
 * 示例2：使用 StoreConsumer 的类组件
 */
class ClassComponentWithConsumer extends Component {
  render() {
    return (
      <StoreConsumer>
        {(store) => (
          <Card title="Consumer 方式使用 Store">
            <div style={{ marginBottom: 16 }}>
              <p>用户总数: {store.users?.length || 0}</p>
              <p>部门总数: {store.departments?.length || 0}</p>
              <p>角色总数: {store.roles?.length || 0}</p>
            </div>
            
            <Table
              columns={[
                {
                  title: '姓名',
                  dataIndex: 'name',
                  key: 'name',
                },
                {
                  title: '部门',
                  dataIndex: 'departmentId',
                  key: 'departmentId',
                  render: (departmentId) => store.getDepartmentName(departmentId),
                },
              ]}
              dataSource={store.users || []}
              rowKey="id"
              pagination={{ pageSize: 3 }}
            />
          </Card>
        )}
      </StoreConsumer>
    );
  }
}

/**
 * 示例3：使用 BasicStoreConsumer 的类组件
 */
class ClassComponentWithBasicConsumer extends Component {
  render() {
    return (
      <BasicStoreConsumer>
        {(store) => (
          <Card title="Basic Consumer 方式使用 Store" size="small">
            <div>
              <p>用户总数: {store.users?.length || 0}</p>
              <p>部门总数: {store.departments?.length || 0}</p>
              <p>角色总数: {store.roles?.length || 0}</p>
              
              {store.users?.slice(0, 3).map(user => (
                <div key={user.id} style={{ marginBottom: 8 }}>
                  {user.name} - {store.getDepartmentName(user.departmentId)}
                </div>
              ))}
            </div>
          </Card>
        )}
      </BasicStoreConsumer>
    );
  }
}

/**
 * 主示例组件
 */
class ClassComponentStoreExample extends Component {
  render() {
    return (
      <div style={{ padding: '24px' }}>
        <h2>类组件使用 Store 的三种方式</h2>
        
        <div style={{ marginBottom: 24 }}>
          <WrappedClassComponent />
        </div>
        
        <div style={{ marginBottom: 24 }}>
          <ClassComponentWithConsumer />
        </div>
        
        <div style={{ marginBottom: 24 }}>
          <ClassComponentWithBasicConsumer />
        </div>
        
        <Card title="使用说明" size="small">
          <div style={{ fontSize: '12px', color: '#666' }}>
            <h4>三种方式对比：</h4>
            <ul>
              <li><strong>HOC (withStore)</strong>: 通过高阶组件注入 store，使用 this.props.store 访问</li>
              <li><strong>StoreConsumer</strong>: 使用 render props 模式，提供完整的 store 功能</li>
              <li><strong>BasicStoreConsumer</strong>: 简化版本，只提供基础的数据访问功能</li>
            </ul>
            
            <h4>推荐使用场景：</h4>
            <ul>
              <li><strong>新项目</strong>: 直接使用函数组件 + hooks</li>
              <li><strong>现有类组件</strong>: 使用 HOC 方式，改动最小</li>
              <li><strong>复杂逻辑</strong>: 使用 StoreConsumer，更灵活</li>
            </ul>
          </div>
        </Card>
      </div>
    );
  }
}

export default ClassComponentStoreExample;
