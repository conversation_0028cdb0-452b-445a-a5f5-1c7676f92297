import React from 'react';
import { Card, Alert } from 'antd';
import { useDepartments, useRoles, usePermissions } from '../../store/hooks';

/**
 * 快速 Store 测试组件
 * 简单验证同步初始化是否工作
 */
const QuickStoreTest = () => {
  const { departments, getDepartmentName } = useDepartments();
  const { roles, getRoleName } = useRoles();
  const { permissions, getPermissionName } = usePermissions();

  // 检查数据是否已经初始化
  const isDataReady = departments.length > 0 && roles.length > 0 && permissions.length > 0;

  return (
    <div style={{ padding: '24px' }}>
      <h2>Store 同步初始化测试</h2>

      {/* 状态提示 */}
      <Alert
        message={isDataReady ? '✅ 基础数据已同步初始化完成' : '❌ 基础数据未完成初始化'}
        description={
          isDataReady
            ? `部门: ${departments.length}个, 角色: ${roles.length}个, 权限: ${permissions.length}个`
            : '基础数据初始化失败或未完成'
        }
        type={isDataReady ? 'success' : 'error'}
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* 基础信息 */}
      <Card title="基础数据统计" style={{ marginBottom: 16 }}>
        <p><strong>部门总数:</strong> {departments.length}</p>
        <p><strong>角色总数:</strong> {roles.length}</p>
        <p><strong>权限总数:</strong> {permissions.length}</p>
      </Card>

      {/* 映射测试 */}
      <Card title="名称映射测试" style={{ marginBottom: 16 }}>
        {departments.length > 0 && (
          <p><strong>部门映射:</strong> ID 1 → {getDepartmentName(1)}</p>
        )}
        {roles.length > 0 && (
          <p><strong>角色映射:</strong> ID 1 → {getRoleName(1)}</p>
        )}
        {permissions.length > 0 && (
          <p><strong>权限映射:</strong> ID 1 → {getPermissionName(1)}</p>
        )}
      </Card>

      {/* 数据列表 */}
      <Card title="部门列表" style={{ marginBottom: 16 }}>
        {departments.slice(0, 5).map((dept) => (
          <div key={dept.id} style={{ marginBottom: 8 }}>
            {dept.id}. {dept.name} ({dept.code})
          </div>
        ))}
      </Card>

      <Card title="角色列表" style={{ marginBottom: 16 }}>
        {roles.slice(0, 5).map((role) => (
          <div key={role.id} style={{ marginBottom: 8 }}>
            {role.id}. {role.name} ({role.code})
          </div>
        ))}
      </Card>

      <Card title="权限列表">
        {permissions.slice(0, 5).map((permission) => (
          <div key={permission.id} style={{ marginBottom: 8 }}>
            {permission.id}. {permission.name} ({permission.code})
          </div>
        ))}
      </Card>
    </div>
  );
};

export default QuickStoreTest;
