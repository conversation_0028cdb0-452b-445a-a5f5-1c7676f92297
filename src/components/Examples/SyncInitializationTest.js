import React from 'react';
import { Card, Row, Col, Statistic, Table, Tag, Alert } from 'antd';
import { useDepartments, useRoles, usePermissions } from '../../store/hooks';

/**
 * 同步初始化测试组件
 * 验证基础数据是否在页面加载前就已经初始化完成
 */
const SyncInitializationTest = () => {
  const { departments, departmentMap, getDepartmentName } = useDepartments();
  const { roles, roleMap, getRoleName } = useRoles();
  const { permissions, permissionMap, getPermissionName } = usePermissions();

  // 检查数据是否已经初始化
  const isDataReady = departments.length > 0 && roles.length > 0 && permissions.length > 0;

  // 部门表格列定义
  const departmentColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '部门名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '部门代码',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
  ];

  // 角色表格列定义
  const roleColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '角色代码',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '正常' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
  ];

  // 权限表格列定义
  const permissionColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '权限名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '权限代码',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      render: (category) => (
        <Tag color={category === 'system' ? 'blue' : 'orange'}>
          {category === 'system' ? '系统管理' : '业务管理'}
        </Tag>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <h2>同步初始化测试</h2>

      {/* 状态提示 */}
      <Alert
        message={isDataReady ? '✅ 基础数据已同步初始化完成' : '❌ 基础数据未完成初始化'}
        description={
          isDataReady
            ? '所有基础数据（部门、角色、权限）都已在页面加载前同步初始化完成，可以立即使用。'
            : '基础数据初始化失败或未完成，请检查控制台错误信息。'
        }
        type={isDataReady ? 'success' : 'error'}
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="部门总数"
              value={departments.length}
              valueStyle={{ color: '#3f8600' }}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="角色总数"
              value={roles.length}
              valueStyle={{ color: '#1890ff' }}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="权限总数"
              value={permissions.length}
              valueStyle={{ color: '#722ed1' }}
              suffix="个"
            />
          </Card>
        </Col>
      </Row>

      {/* 映射测试 */}
      <Card title="名称映射测试" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <h4>部门映射测试</h4>
            {departments.slice(0, 3).map((dept) => (
              <p key={dept.id}>
                ID {dept.id} → {getDepartmentName(dept.id)}
              </p>
            ))}
          </Col>
          <Col span={8}>
            <h4>角色映射测试</h4>
            {roles.slice(0, 3).map((role) => (
              <p key={role.id}>
                ID {role.id} → {getRoleName(role.id)}
              </p>
            ))}
          </Col>
          <Col span={8}>
            <h4>权限映射测试</h4>
            {permissions.slice(0, 3).map((permission) => (
              <p key={permission.id}>
                ID {permission.id} → {getPermissionName(permission.id)}
              </p>
            ))}
          </Col>
        </Row>
      </Card>

      {/* 部门数据表格 */}
      <Card title="部门数据" style={{ marginBottom: 16 }}>
        <Table
          columns={departmentColumns}
          dataSource={departments}
          rowKey="id"
          pagination={{ pageSize: 5 }}
          size="small"
        />
      </Card>

      {/* 角色数据表格 */}
      <Card title="角色数据" style={{ marginBottom: 16 }}>
        <Table
          columns={roleColumns}
          dataSource={roles}
          rowKey="id"
          pagination={{ pageSize: 5 }}
          size="small"
        />
      </Card>

      {/* 权限数据表格 */}
      <Card title="权限数据" style={{ marginBottom: 16 }}>
        <Table
          columns={permissionColumns}
          dataSource={permissions}
          rowKey="id"
          pagination={{ pageSize: 5 }}
          size="small"
        />
      </Card>

      {/* 技术说明 */}
      <Card title="技术说明" size="small">
        <div style={{ fontSize: '12px', color: '#666' }}>
          <h4>同步初始化的优势：</h4>
          <ul>
            <li>✅ 基础数据在模块加载时就已经准备好，无需等待异步请求</li>
            <li>✅ 页面渲染时可以立即使用这些数据，避免加载状态</li>
            <li>✅ 减少了组件挂载后的数据获取延迟</li>
            <li>✅ 提供更好的用户体验，避免页面闪烁</li>
          </ul>

          <h4>数据来源：</h4>
          <ul>
            <li>部门数据：来自 DEPARTMENT_CONFIG 配置文件</li>
            <li>角色数据：预定义的默认角色</li>
            <li>权限数据：预定义的默认权限</li>
          </ul>

          <h4>映射表：</h4>
          <ul>
            <li>departmentMap: {Object.keys(departmentMap).length} 个部门映射</li>
            <li>roleMap: {Object.keys(roleMap).length} 个角色映射</li>
            <li>permissionMap: {Object.keys(permissionMap).length} 个权限映射</li>
          </ul>
        </div>
      </Card>
    </div>
  );
};

export default SyncInitializationTest;
