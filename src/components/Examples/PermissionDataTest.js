import React from 'react';
import { Card, Row, Col, Table, Tag, Alert, Collapse, Descriptions } from 'antd';
import { usePermissions } from '../../store/hooks';

const { Panel } = Collapse;

/**
 * 权限数据测试组件
 * 验证权限数据是否按照新的API结构正确加载和显示
 */
const PermissionDataTest = () => {
  const { permissions, permissionMap, getPermissionName } = usePermissions();

  // 按模块分组权限
  const groupedPermissions = permissions.reduce((acc, permission) => {
    const module = permission.module || '其他';
    if (!acc[module]) {
      acc[module] = [];
    }
    acc[module].push(permission);
    return acc;
  }, {});

  // 模块名称映射
  const moduleNameMap = {
    user: '用户管理',
    role: '角色管理',
    permission: '权限管理',
    department: '部门管理',
    supplier: '供应商管理',
    budget: '预算管理',
    project: '项目管理',
    brand: '品牌管理',
    finance: '财务管理',
    report: '报表管理',
    system: '系统管理',
  };

  // 权限表格列定义
  const permissionColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 200,
      render: (id) => (
        <code style={{ fontSize: '11px', background: '#f5f5f5', padding: '2px 4px' }}>
          {id}
        </code>
      ),
    },
    {
      title: '权限名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      render: (name) => (
        <code style={{ color: '#1890ff' }}>{name}</code>
      ),
    },
    {
      title: '显示名称',
      dataIndex: 'displayName',
      key: 'displayName',
      width: 120,
      render: (displayName) => (
        <strong>{displayName}</strong>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 150,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 80,
      render: (action) => {
        const colorMap = {
          read: 'blue',
          create: 'green',
          update: 'orange',
          delete: 'red',
          approve: 'purple',
          export: 'cyan',
          assign: 'magenta',
          config: 'gold',
          log: 'lime',
          backup: 'volcano',
        };
        return (
          <Tag color={colorMap[action] || 'default'}>
            {action}
          </Tag>
        );
      },
    },
    {
      title: '系统权限',
      dataIndex: 'isSystem',
      key: 'isSystem',
      width: 80,
      render: (isSystem) => (
        <Tag color={isSystem ? 'red' : 'green'}>
          {isSystem ? '系统' : '自定义'}
        </Tag>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <h2>权限数据结构测试</h2>

      {/* 状态提示 */}
      <Alert
        message={permissions.length > 0 ? '✅ 权限数据已加载' : '⏳ 权限数据加载中...'}
        description={
          permissions.length > 0
            ? `成功加载 ${permissions.length} 个权限，按 ${Object.keys(groupedPermissions).length} 个模块分组`
            : '正在从API加载权限数据...'
        }
        type={permissions.length > 0 ? 'success' : 'info'}
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* 数据概览 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Descriptions size="small" column={1}>
              <Descriptions.Item label="权限总数">
                <strong style={{ fontSize: '18px', color: '#1890ff' }}>
                  {permissions.length}
                </strong>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Descriptions size="small" column={1}>
              <Descriptions.Item label="模块数量">
                <strong style={{ fontSize: '18px', color: '#52c41a' }}>
                  {Object.keys(groupedPermissions).length}
                </strong>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Descriptions size="small" column={1}>
              <Descriptions.Item label="系统权限">
                <strong style={{ fontSize: '18px', color: '#f5222d' }}>
                  {permissions.filter((p) => p.isSystem).length}
                </strong>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Descriptions size="small" column={1}>
              <Descriptions.Item label="映射表">
                <strong style={{ fontSize: '18px', color: '#722ed1' }}>
                  {Object.keys(permissionMap).length}
                </strong>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
      </Row>

      {/* 按模块分组显示 */}
      <Card title="按模块分组的权限" style={{ marginBottom: 16 }}>
        <Collapse>
          {Object.entries(groupedPermissions).map(([module, modulePermissions]) => (
            <Panel
              header={
                <div>
                  <strong>{moduleNameMap[module] || module}</strong>
                  <Tag color="blue" style={{ marginLeft: 8 }}>
                    {modulePermissions.length} 个权限
                  </Tag>
                </div>
              }
              key={module}
            >
              <Table
                columns={permissionColumns}
                dataSource={modulePermissions}
                rowKey="id"
                pagination={false}
                size="small"
                scroll={{ x: 800 }}
              />
            </Panel>
          ))}
        </Collapse>
      </Card>

      {/* 权限映射测试 */}
      <Card title="权限映射测试" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={12}>
            <h4>ID映射测试</h4>
            {permissions.slice(0, 5).map((permission) => (
              <div key={permission.id} style={{ marginBottom: 4, fontSize: '12px' }}>
                <code>{permission.id}</code> → {getPermissionName(permission.id)}
              </div>
            ))}
          </Col>
          <Col span={12}>
            <h4>数据结构示例</h4>
            {permissions.length > 0 && (
              <pre style={{
                background: '#f5f5f5',
                padding: '8px',
                fontSize: '11px',
                borderRadius: '4px',
                overflow: 'auto',
                maxHeight: '200px',
              }}
              >
                {JSON.stringify(permissions[0], null, 2)}
              </pre>
            )}
          </Col>
        </Row>
      </Card>

      {/* API数据结构说明 */}
      <Card title="API数据结构说明" size="small">
        <div style={{ fontSize: '12px', color: '#666' }}>
          <h4>权限对象结构：</h4>
          <ul>
            <li><code>id</code>: 权限唯一标识符（字符串）</li>
            <li><code>name</code>: 权限代码名称（如 user.read）</li>
            <li><code>displayName</code>: 权限显示名称（如 查看用户）</li>
            <li><code>description</code>: 权限描述</li>
            <li><code>module</code>: 所属模块（如 user, role, project）</li>
            <li><code>action</code>: 操作类型（如 read, create, update, delete）</li>
            <li><code>resource</code>: 资源标识（可选）</li>
            <li><code>isSystem</code>: 是否为系统权限</li>
            <li><code>createdAt</code>: 创建时间</li>
            <li><code>updatedAt</code>: 更新时间</li>
          </ul>

          <h4>模块分类：</h4>
          <div>
            {Object.entries(moduleNameMap).map(([key, name]) => (
              <Tag key={key} style={{ margin: '2px' }}>
                {key}: {name}
              </Tag>
            ))}
          </div>
        </div>
      </Card>
    </div>
  );
};

export default PermissionDataTest;
