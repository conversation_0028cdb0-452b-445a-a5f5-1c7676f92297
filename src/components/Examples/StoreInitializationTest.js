import React, { useEffect } from 'react';
import { Card, Row, Col, Statistic, Table, Tag, Button, message } from 'antd';
import { useDepartments, useRoles, usePermissions, useStats } from '../../store/hooks';

/**
 * Store 初始化测试组件
 * 用于验证 store 是否正确初始化了部门、角色、权限等基础数据
 */
const StoreInitializationTest = () => {
  const { departments, departmentMap, getDepartmentName } = useDepartments();
  const { roles, roleMap, getRoleName } = useRoles();
  const { permissions, permissionMap, getPermissionName } = usePermissions();
  const { userStats, roleStats, departmentStats } = useStats();

  useEffect(() => {
    console.log('=== Store 初始化状态检查 ===');
    console.log('部门数据:', departments);
    console.log('角色数据:', roles);
    console.log('权限数据:', permissions);
    console.log('部门映射:', departmentMap);
    console.log('角色映射:', roleMap);
    console.log('权限映射:', permissionMap);
  }, [departments, roles, permissions, departmentMap, roleMap, permissionMap]);

  // 测试名称映射功能
  const testNameMapping = () => {
    const testResults = [];

    // 测试部门名称映射
    if (departments.length > 0) {
      const firstDept = departments[0];
      const mappedName = getDepartmentName(firstDept.id);
      testResults.push(`部门映射测试: ID ${firstDept.id} -> ${mappedName}`);
    }

    // 测试角色名称映射
    if (roles.length > 0) {
      const firstRole = roles[0];
      const mappedName = getRoleName(firstRole.id);
      testResults.push(`角色映射测试: ID ${firstRole.id} -> ${mappedName}`);
    }

    // 测试权限名称映射
    if (permissions.length > 0) {
      const firstPermission = permissions[0];
      const mappedName = getPermissionName(firstPermission.id);
      testResults.push(`权限映射测试: ID ${firstPermission.id} -> ${mappedName}`);
    }

    message.success(`映射测试完成！\n${testResults.join('\n')}`);
  };

  // 部门表格列定义
  const departmentColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '部门名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '部门代码',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
  ];

  // 角色表格列定义
  const roleColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '角色代码',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '正常' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
  ];

  // 权限表格列定义
  const permissionColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '权限名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '权限代码',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      render: (category) => (
        <Tag color={category === 'system' ? 'blue' : 'orange'}>
          {category === 'system' ? '系统管理' : '业务管理'}
        </Tag>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <h2>Store 初始化状态检查</h2>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="部门总数"
              value={departments.length}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="角色总数"
              value={roles.length}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="权限总数"
              value={permissions.length}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Button type="primary" onClick={testNameMapping} block>
              测试名称映射
            </Button>
          </Card>
        </Col>
      </Row>

      {/* 部门数据表格 */}
      <Card title="部门数据" style={{ marginBottom: 16 }}>
        <Table
          columns={departmentColumns}
          dataSource={departments}
          rowKey="id"
          pagination={{ pageSize: 5 }}
          size="small"
        />
      </Card>

      {/* 角色数据表格 */}
      <Card title="角色数据" style={{ marginBottom: 16 }}>
        <Table
          columns={roleColumns}
          dataSource={roles}
          rowKey="id"
          pagination={{ pageSize: 5 }}
          size="small"
        />
      </Card>

      {/* 权限数据表格 */}
      <Card title="权限数据" style={{ marginBottom: 16 }}>
        <Table
          columns={permissionColumns}
          dataSource={permissions}
          rowKey="id"
          pagination={{ pageSize: 5 }}
          size="small"
        />
      </Card>

      {/* 统计信息 */}
      <Card title="统计信息" size="small">
        <Row gutter={16}>
          <Col span={8}>
            <h4>用户统计</h4>
            <p>总数: {userStats.total}</p>
            <p>正常: {userStats.active}</p>
            <p>禁用: {userStats.inactive}</p>
          </Col>
          <Col span={8}>
            <h4>角色统计</h4>
            <p>总数: {roleStats.total}</p>
            <p>正常: {roleStats.active}</p>
            <p>禁用: {roleStats.inactive}</p>
          </Col>
          <Col span={8}>
            <h4>部门统计</h4>
            <p>总数: {departmentStats.total}</p>
            <p>有用户: {departmentStats.withUsers}</p>
            <p>无用户: {departmentStats.withoutUsers}</p>
          </Col>
        </Row>
      </Card>

      {/* 调试信息 */}
      <Card title="调试信息" size="small" style={{ marginTop: 16 }}>
        <div style={{ fontSize: '12px', color: '#666' }}>
          <p>✅ Store 已成功初始化基础数据</p>
          <p>✅ 部门数据来源: DEPARTMENT_CONFIG 配置文件</p>
          <p>✅ 角色数据来源: 默认角色配置</p>
          <p>✅ 权限数据来源: 默认权限配置</p>
          <p>✅ 所有数据都已创建对应的映射表 (Map)</p>
          <p>✅ 名称映射函数可以正常工作</p>
        </div>
      </Card>
    </div>
  );
};

export default StoreInitializationTest;
