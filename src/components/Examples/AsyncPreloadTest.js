import React from 'react';
import { Card, Row, Col, Statistic, Alert, Timeline, Tag } from 'antd';
import { useDepartments, useRoles, usePermissions } from '../../store/hooks';

/**
 * 异步预加载测试组件
 * 验证基础数据是否通过异步预加载正确初始化
 */
const AsyncPreloadTest = () => {
  const { departments, departmentMap, getDepartmentName } = useDepartments();
  const { roles, roleMap, getRoleName } = useRoles();
  const { permissions, permissionMap, getPermissionName } = usePermissions();

  // 检查数据是否已经初始化
  const isDataReady = departments.length > 0 && roles.length > 0 && permissions.length > 0;

  return (
    <div style={{ padding: '24px' }}>
      <h2>异步预加载测试</h2>

      {/* 状态提示 */}
      <Alert
        message={isDataReady ? '✅ 基础数据已通过异步预加载完成' : '❌ 基础数据未完成预加载'}
        description={
          isDataReady
            ? '所有基础数据已在应用启动时异步预加载完成，现在可以立即使用。'
            : '基础数据预加载失败或未完成，请检查网络连接和API状态。'
        }
        type={isDataReady ? 'success' : 'error'}
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* 预加载流程说明 */}
      <Card title="预加载流程" style={{ marginBottom: 24 }}>
        <Timeline>
          <Timeline.Item color="green">
            <strong>1. 应用启动</strong>
            <br />
            AppBootstrap 组件开始异步初始化数据
          </Timeline.Item>
          <Timeline.Item color="blue">
            <strong>2. 数据获取</strong>
            <br />
            尝试从API获取部门数据，失败时降级到配置文件
            <br />
            同时初始化角色和权限数据
          </Timeline.Item>
          <Timeline.Item color="orange">
            <strong>3. 数据处理</strong>
            <br />
            创建映射表，优化数据访问性能
          </Timeline.Item>
          <Timeline.Item color="purple">
            <strong>4. Store 初始化</strong>
            <br />
            将预加载的数据传递给 StoreProvider
          </Timeline.Item>
          <Timeline.Item color="green">
            <strong>5. 应用渲染</strong>
            <br />
            主应用组件渲染，数据立即可用
          </Timeline.Item>
        </Timeline>
      </Card>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="部门总数"
              value={departments.length}
              valueStyle={{ color: '#3f8600' }}
              suffix="个"
            />
            <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              映射表: {Object.keys(departmentMap).length} 条记录
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="角色总数"
              value={roles.length}
              valueStyle={{ color: '#1890ff' }}
              suffix="个"
            />
            <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              映射表: {Object.keys(roleMap).length} 条记录
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="权限总数"
              value={permissions.length}
              valueStyle={{ color: '#722ed1' }}
              suffix="个"
            />
            <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              映射表: {Object.keys(permissionMap).length} 条记录
            </div>
          </Card>
        </Col>
      </Row>

      {/* 数据来源说明 */}
      <Card title="数据来源" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <h4>部门数据</h4>
            <Tag color="blue">API 优先</Tag>
            <Tag color="orange">配置文件降级</Tag>
            <p style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              首先尝试从 departmentAPI.getDepartments() 获取，
              失败时使用 DEPARTMENT_CONFIG 配置文件数据
            </p>
          </Col>
          <Col span={8}>
            <h4>角色数据</h4>
            <Tag color="green">预定义</Tag>
            <p style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              使用预定义的5个默认角色：
              系统管理员、项目经理、执行人员、财务人员、普通用户
            </p>
          </Col>
          <Col span={8}>
            <h4>权限数据</h4>
            <Tag color="purple">预定义</Tag>
            <p style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              使用预定义的7个权限，
              分为系统管理和业务管理两大类
            </p>
          </Col>
        </Row>
      </Card>

      {/* 映射测试 */}
      <Card title="映射功能测试" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <h4>部门映射</h4>
            {departments.slice(0, 3).map((dept) => (
              <div key={dept.id} style={{ marginBottom: 4, fontSize: '12px' }}>
                ID {dept.id} → {getDepartmentName(dept.id)}
              </div>
            ))}
          </Col>
          <Col span={8}>
            <h4>角色映射</h4>
            {roles.slice(0, 3).map((role) => (
              <div key={role.id} style={{ marginBottom: 4, fontSize: '12px' }}>
                ID {role.id} → {getRoleName(role.id)}
              </div>
            ))}
          </Col>
          <Col span={8}>
            <h4>权限映射</h4>
            {permissions.slice(0, 3).map((permission) => (
              <div key={permission.id} style={{ marginBottom: 4, fontSize: '12px' }}>
                ID {permission.id} → {getPermissionName(permission.id)}
              </div>
            ))}
          </Col>
        </Row>
      </Card>

      {/* 技术优势 */}
      <Card title="技术优势" size="small">
        <div style={{ fontSize: '12px', color: '#666' }}>
          <Row gutter={16}>
            <Col span={12}>
              <h4>异步预加载的优势：</h4>
              <ul>
                <li>✅ 支持API调用，数据更加动态</li>
                <li>✅ 具备降级机制，提高可靠性</li>
                <li>✅ 在应用启动时完成，用户无感知</li>
                <li>✅ 数据准备完成后才渲染主应用</li>
                <li>✅ 避免组件中的加载状态处理</li>
              </ul>
            </Col>
            <Col span={12}>
              <h4>实现特点：</h4>
              <ul>
                <li>🔄 AppBootstrap 负责数据预加载</li>
                <li>📦 StoreProvider 接收预加载数据</li>
                <li>🎯 组件可立即使用数据</li>
                <li>⚡ 映射表提供快速查找</li>
                <li>🛡️ 错误处理和降级策略</li>
              </ul>
            </Col>
          </Row>
        </div>
      </Card>
    </div>
  );
};

export default AsyncPreloadTest;
