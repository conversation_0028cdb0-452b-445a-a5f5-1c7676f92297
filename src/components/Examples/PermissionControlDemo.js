import React, { useState } from 'react';
import {
  Card,
  Button,
  Table,
  Tag,
  Popconfirm,
  message,
  Row,
  Col,
  Alert,
  Select,
  // Switch,
  Divider,
} from 'antd';
import {
  // PlusOutlined,
  EyeOutlined,
  // CheckOutlined,
  ExportOutlined,
  UserAddOutlined,
} from '@ant-design/icons';
import { FeatureGuard, ButtonGuard } from '../Permission/PermissionGuard';
import { useFeaturePermission } from '../../hooks/usePermission';
import { useStore } from '../../store';

const { Option } = Select;

/**
 * 权限控制演示组件
 * 展示如何在实际组件中应用权限控制
 */
const PermissionControlDemo = () => {
  const [selectedModule, setSelectedModule] = useState('project');
  const [mockUserRole, setMockUserRole] = useState('admin');
  const { state, actions } = useStore();

  // 获取当前模块的功能权限
  const {
    canRead,
    canCreate,
    canUpdate,
    canDelete,
    canApprove,
    canExport,
    canAssign,
    isSuperAdmin,
  } = useFeaturePermission(selectedModule);

  // 模拟用户角色切换（用于演示）
  const handleRoleChange = (role) => {
    setMockUserRole(role);

    // 模拟不同角色的权限
    const mockUser = {
      id: 1,
      name: '演示用户',
      roles: [],
    };

    if (role === 'admin') {
      mockUser.roles = [{
        id: 1,
        code: 'admin',
        name: '系统管理员',
        permissions: state.permissions, // 所有权限
      }];
    } else if (role === 'project_manager') {
      mockUser.roles = [{
        id: 2,
        code: 'project_manager',
        name: '项目经理',
        permissions: state.permissions.filter((p) =>
          p.module === 'project' || p.module === 'brand' || p.module === 'supplier'),
      }];
    } else if (role === 'finance') {
      mockUser.roles = [{
        id: 3,
        code: 'finance',
        name: '财务人员',
        permissions: state.permissions.filter((p) =>
          p.module === 'finance' || p.module === 'budget' ||
          (p.module === 'project' && p.action === 'read')),
      }];
    } else if (role === 'viewer') {
      mockUser.roles = [{
        id: 4,
        code: 'viewer',
        name: '查看者',
        permissions: state.permissions.filter((p) => p.action === 'read'),
      }];
    }

    actions.setCurrentUser(mockUser);
  };

  // 模拟数据
  const mockData = [
    {
      id: 1,
      name: '春季营销活动',
      status: 'executing',
      budget: 100000,
      manager: '张三',
    },
    {
      id: 2,
      name: '品牌推广项目',
      status: 'pending',
      budget: 200000,
      manager: '李四',
    },
    {
      id: 3,
      name: '产品发布会',
      status: 'completed',
      budget: 150000,
      manager: '王五',
    },
  ];

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusMap = {
          executing: { color: 'processing', text: '执行中' },
          pending: { color: 'warning', text: '待审批' },
          completed: { color: 'success', text: '已完成' },
        };
        const config = statusMap[status] || { color: 'default', text: status };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '预算',
      dataIndex: 'budget',
      key: 'budget',
      render: (budget) => `¥${budget.toLocaleString()}`,
    },
    {
      title: '负责人',
      dataIndex: 'manager',
      key: 'manager',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <div size="small">
          <FeatureGuard permissions={[`${selectedModule}.read`]}>
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => message.info(`查看 ${record.name}`)}
            >
              查看
            </Button>
          </FeatureGuard>

          <ButtonGuard permissions={[`${selectedModule}.update`]}>
            <Button
              type="link"
              size="small"
              onClick={() => message.info(`编辑 ${record.name}`)}
            >
              编辑
            </Button>
          </ButtonGuard>

          <FeatureGuard permissions={[`${selectedModule}.approve`]}>
            <Button
              type="link"
              size="small"
              onClick={() => message.success(`审批 ${record.name}`)}
            >
              审批
            </Button>
          </FeatureGuard>

          <ButtonGuard permissions={[`${selectedModule}.delete`]}>
            <Popconfirm
              title="确定要删除吗？"
              onConfirm={() => message.success(`删除 ${record.name}`)}
            >
              <Button
                type="link"
                size="small"
                danger
              >
                删除
              </Button>
            </Popconfirm>
          </ButtonGuard>
        </div>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* 标题 */}
      <h2>权限控制演示</h2>
      {/* 当前用户信息 */}
      <Alert
        message="当前用户信息"
        description={
          <div>
            <p><strong>用户名:</strong> {state.currentUser?.name || '未登录'}</p>
            <p><strong>角色:</strong> {state.currentUserRoles?.map((role) => role.name).join(', ') || '无角色'}</p>
            <p><strong>权限:</strong> {state.currentUserPermissions?.map((permission) => permission.name).join(', ') || '无权限'}</p>
          </div>
        }
        type="info"
        style={{ marginBottom: 24 }}
      />

      {/* 演示控制面板 */}
      <Card title="演示控制面板" style={{ marginBottom: 24 }}>
        <Row gutter={16}>
          <Col span={12}>
            <div style={{ marginBottom: 16 }}>
              <label style={{ marginRight: 8 }}>模拟用户角色:</label>
              <Select
                value={mockUserRole}
                onChange={handleRoleChange}
                style={{ width: 200 }}
              >
                <Option value="admin">系统管理员</Option>
                <Option value="project_manager">项目经理</Option>
                <Option value="finance">财务人员</Option>
                <Option value="viewer">查看者</Option>
              </Select>
            </div>
          </Col>
          <Col span={12}>
            <div style={{ marginBottom: 16 }}>
              <label style={{ marginRight: 8 }}>当前模块:</label>
              <Select
                value={selectedModule}
                onChange={setSelectedModule}
                style={{ width: 200 }}
              >
                <Option value="project">项目管理</Option>
                <Option value="brand">品牌管理</Option>
                <Option value="supplier">供应商管理</Option>
                <Option value="budget">预算管理</Option>
                <Option value="finance">财务管理</Option>
                <Option value="user">用户管理</Option>
                <Option value="role">角色管理</Option>
              </Select>
            </div>
          </Col>
        </Row>

        {/* 权限状态显示 */}
        <Alert
          message="当前权限状态"
          description={
            <div>
              <p><strong>角色:</strong> {mockUserRole} {isSuperAdmin && '(超级管理员)'}</p>
              <p><strong>模块:</strong> {selectedModule}</p>
              <p><strong>权限:</strong></p>
              <div wrap>
                <Tag color={canRead ? 'green' : 'red'}>查看: {canRead ? '✓' : '✗'}</Tag>
                <Tag color={canCreate ? 'green' : 'red'}>创建: {canCreate ? '✓' : '✗'}</Tag>
                <Tag color={canUpdate ? 'green' : 'red'}>编辑: {canUpdate ? '✓' : '✗'}</Tag>
                <Tag color={canDelete ? 'green' : 'red'}>删除: {canDelete ? '✓' : '✗'}</Tag>
                <Tag color={canApprove ? 'green' : 'red'}>审批: {canApprove ? '✓' : '✗'}</Tag>
                <Tag color={canExport ? 'green' : 'red'}>导出: {canExport ? '✓' : '✗'}</Tag>
                <Tag color={canAssign ? 'green' : 'red'}>分配: {canAssign ? '✓' : '✗'}</Tag>
              </div>
            </div>
          }
          type="info"
          style={{ marginTop: 16 }}
        />
      </Card>

      {/* 功能按钮区域 */}
      <Card title={`${selectedModule}管理`} style={{ marginBottom: 24 }}>
        <div style={{ marginBottom: 16 }}>
          <ButtonGuard permissions={[`${selectedModule}.create`]}>
            {/* <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => message.info(`创建新的${selectedModule}`)}
            >
              新建
            </Button> */}
          </ButtonGuard>

          <FeatureGuard permissions={[`${selectedModule}.export`]}>
            <Button
              icon={<ExportOutlined />}
              onClick={() => message.info(`导出${selectedModule}数据`)}
            >
              导出
            </Button>
          </FeatureGuard>

          <FeatureGuard permissions={[`${selectedModule}.assign`]}>
            <Button
              icon={<UserAddOutlined />}
              onClick={() => message.info(`分配${selectedModule}权限`)}
            >
              权限分配
            </Button>
          </FeatureGuard>
        </div>

        <Divider />

        {/* 数据表格 */}
        <FeatureGuard
          permissions={[`${selectedModule}.read`]}
          fallback={
            <Alert
              message="无权限访问"
              description={`您没有权限查看${selectedModule}数据`}
              type="warning"
              showIcon
            />
          }
        >
          <Table
            columns={columns}
            dataSource={mockData}
            rowKey="id"
            pagination={false}
            size="small"
          />
        </FeatureGuard>
      </Card>

      {/* 权限控制说明 */}
      <Card title="权限控制说明" size="small">
        <div style={{ fontSize: '12px', color: '#666' }}>
          <h4>权限控制组件使用说明：</h4>
          <ul>
            <li><code>FeatureGuard</code>: 功能权限守卫，无权限时不显示组件</li>
            <li><code>ButtonGuard</code>: 按钮权限守卫，无权限时禁用按钮</li>
            <li><code>PageGuard</code>: 页面权限守卫，无权限时显示403页面</li>
            <li><code>ProtectedRoute</code>: 路由权限守卫，保护需要权限的路由</li>
          </ul>

          <h4>权限检查逻辑：</h4>
          <ul>
            <li>超级管理员拥有所有权限</li>
            <li>普通用户根据角色分配的权限进行检查</li>
            <li>权限格式: <code>模块.操作</code> (如: project.create)</li>
            <li>支持单个权限和多个权限的检查</li>
          </ul>

          <h4>当前演示角色权限：</h4>
          <ul>
            <li><strong>系统管理员</strong>: 拥有所有权限</li>
            <li><strong>项目经理</strong>: 项目、品牌、供应商管理权限</li>
            <li><strong>财务人员</strong>: 财务、预算管理权限，项目查看权限</li>
            <li><strong>查看者</strong>: 仅有各模块的查看权限</li>
          </ul>
        </div>
      </Card>
    </div>
  );
};

export default PermissionControlDemo;
