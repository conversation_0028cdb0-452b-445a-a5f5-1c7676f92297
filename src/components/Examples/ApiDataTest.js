import React from 'react';
import { Card, Row, Col, Statistic, Alert, Timeline, Tag, Spin } from 'antd';
import { useDepartments, useRoles, usePermissions } from '../../store/hooks';
import { useStore } from '../../store/index';

/**
 * API数据测试组件
 * 验证基础数据是否从API正确加载
 */
const ApiDataTest = () => {
  const { departments, departmentMap, getDepartmentName } = useDepartments();
  const { roles, roleMap, getRoleName } = useRoles();
  const { permissions, permissionMap, getPermissionName } = usePermissions();
  const { state } = useStore();

  // 检查数据是否已经加载
  const isDataLoaded = departments.length > 0 || roles.length > 0 || permissions.length > 0;
  const hasErrors = state.errors && Object.keys(state.errors).length > 0;

  // 状态提示信息
  let alertMessage = '';
  if (hasErrors) {
    alertMessage = '⚠️ 部分数据加载失败';
  } else if (isDataLoaded) {
    alertMessage = '✅ 基础数据已从API加载完成';
  } else {
    alertMessage = '⏳ 正在从API加载基础数据...';
  }

  let alertDescription = '';
  if (hasErrors) {
    alertDescription = '部分API调用失败，已使用默认数据。请检查网络连接和API状态。';
  } else if (isDataLoaded) {
    alertDescription = '所有基础数据已从后台API成功加载，现在可以正常使用。';
  } else {
    alertDescription = '正在异步从后台API获取部门、角色、权限等基础数据，请稍候...';
  }

  let alertType = 'info';
  if (hasErrors) {
    alertType = 'warning';
  } else if (isDataLoaded) {
    alertType = 'success';
  }

  return (
    <div style={{ padding: '24px' }}>
      <h2>API数据加载测试</h2>

      {/* 状态提示 */}
      <Alert
        message={alertMessage}
        description={alertDescription}
        type={alertType}
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* 加载状态 */}
      {!isDataLoaded && !hasErrors && (
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16, color: '#666' }}>
            正在从API加载基础数据...
          </div>
        </div>
      )}

      {/* 错误信息 */}
      {hasErrors && (
        <Card title="错误信息" style={{ marginBottom: 24 }}>
          {Object.entries(state.errors).map(([key, error]) => (
            <Alert
              key={key}
              message={`${key} 错误`}
              description={error}
              type="error"
              style={{ marginBottom: 8 }}
            />
          ))}
        </Card>
      )}

      {/* API加载流程说明 */}
      <Card title="API加载流程" style={{ marginBottom: 24 }}>
        <Timeline>
          <Timeline.Item color="green">
            <strong>1. Store初始化</strong>
            <br />
            StoreProvider 组件挂载，初始化空状态
          </Timeline.Item>
          <Timeline.Item color="blue">
            <strong>2. API调用</strong>
            <br />
            异步调用 departmentAPI.getDepartments()
            <br />
            异步调用 roleApi.getRoles()
            <br />
            异步调用 permissionApi.getPermissions()
          </Timeline.Item>
          <Timeline.Item color="orange">
            <strong>3. 数据处理</strong>
            <br />
            处理API响应，创建映射表
            <br />
            API失败时使用默认数据
          </Timeline.Item>
          <Timeline.Item color="purple">
            <strong>4. 状态更新</strong>
            <br />
            通过 dispatch 更新 store 状态
          </Timeline.Item>
          <Timeline.Item color="green">
            <strong>5. 组件更新</strong>
            <br />
            组件自动重新渲染，显示最新数据
          </Timeline.Item>
        </Timeline>
      </Card>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="部门总数"
              value={departments.length}
              valueStyle={{ color: departments.length > 0 ? '#3f8600' : '#999' }}
              suffix="个"
            />
            <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              映射表: {Object.keys(departmentMap).length} 条记录
              <br />
              <Tag color={departments.length > 0 ? 'green' : 'orange'}>
                {departments.length > 0 ? 'API数据' : '等待加载'}
              </Tag>
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="角色总数"
              value={roles.length}
              valueStyle={{ color: roles.length > 0 ? '#1890ff' : '#999' }}
              suffix="个"
            />
            <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              映射表: {Object.keys(roleMap).length} 条记录
              <br />
              <Tag color={roles.length > 0 ? 'blue' : 'orange'}>
                {roles.length > 0 ? 'API数据' : '等待加载'}
              </Tag>
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="权限总数"
              value={permissions.length}
              valueStyle={{ color: permissions.length > 0 ? '#722ed1' : '#999' }}
              suffix="个"
            />
            <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              映射表: {Object.keys(permissionMap).length} 条记录
              <br />
              <Tag color={permissions.length > 0 ? 'purple' : 'orange'}>
                {permissions.length > 0 ? 'API数据' : '等待加载'}
              </Tag>
            </div>
          </Card>
        </Col>
      </Row>

      {/* API策略说明 */}
      <Card title="API加载策略" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <h4>部门数据</h4>
            <Tag color="blue">API优先</Tag>
            <Tag color="red">失败时为空</Tag>
            <p style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              调用 departmentAPI.getDepartments()
              <br />
              失败时使用空数组，不影响应用运行
            </p>
          </Col>
          <Col span={8}>
            <h4>角色数据</h4>
            <Tag color="blue">API优先</Tag>
            <Tag color="orange">默认降级</Tag>
            <p style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              调用 roleApi.getRoles()
              <br />
              失败时使用5个预定义默认角色
            </p>
          </Col>
          <Col span={8}>
            <h4>权限数据</h4>
            <Tag color="blue">API优先</Tag>
            <Tag color="orange">默认降级</Tag>
            <p style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              调用 permissionApi.getPermissions()
              <br />
              失败时使用7个预定义默认权限
            </p>
          </Col>
        </Row>
      </Card>

      {/* 映射测试 */}
      {isDataLoaded && (
        <Card title="映射功能测试" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={8}>
              <h4>部门映射</h4>
              {departments.length > 0 ? (
                departments.slice(0, 3).map((dept) => (
                  <div key={dept.id} style={{ marginBottom: 4, fontSize: '12px' }}>
                    ID {dept.id} → {getDepartmentName(dept.id)}
                  </div>
                ))
              ) : (
                <div style={{ color: '#999', fontSize: '12px' }}>暂无部门数据</div>
              )}
            </Col>
            <Col span={8}>
              <h4>角色映射</h4>
              {roles.slice(0, 3).map((role) => (
                <div key={role.id} style={{ marginBottom: 4, fontSize: '12px' }}>
                  ID {role.id} → {getRoleName(role.id)}
                </div>
              ))}
            </Col>
            <Col span={8}>
              <h4>权限映射</h4>
              {permissions.slice(0, 3).map((permission) => (
                <div key={permission.id} style={{ marginBottom: 4, fontSize: '12px' }}>
                  ID {permission.id} → {getPermissionName(permission.id)}
                </div>
              ))}
            </Col>
          </Row>
        </Card>
      )}

      {/* 技术说明 */}
      <Card title="技术实现" size="small">
        <div style={{ fontSize: '12px', color: '#666' }}>
          <Row gutter={16}>
            <Col span={12}>
              <h4>防死循环机制：</h4>
              <ul>
                <li>✅ 使用 useRef 标记初始化状态</li>
                <li>✅ useEffect 空依赖数组，只执行一次</li>
                <li>✅ 检查预加载数据，避免重复初始化</li>
                <li>✅ API失败时使用默认数据，不重试</li>
              </ul>
            </Col>
            <Col span={12}>
              <h4>数据流特点：</h4>
              <ul>
                <li>🔄 异步加载，不阻塞页面渲染</li>
                <li>📦 自动创建映射表，优化查找性能</li>
                <li>🎯 组件自动响应数据变化</li>
                <li>⚡ 支持API和默认数据混合使用</li>
                <li>🛡️ 完整的错误处理和降级策略</li>
              </ul>
            </Col>
          </Row>
        </div>
      </Card>
    </div>
  );
};

export default ApiDataTest;
