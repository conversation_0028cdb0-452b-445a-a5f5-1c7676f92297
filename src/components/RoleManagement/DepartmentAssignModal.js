import React, { Component } from 'react';
import {
  Modal,
  Transfer,
  message,
  Card,
  Tag,
  Descriptions,
  Alert,
} from 'antd';
import { roleApi } from '../../services/api';
import { withStore } from '../../store/withStore';

class DepartmentAssignModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      targetKeys: [],
      selectedKeys: [],
      roleDepartments: [],
    };
  }

  componentDidMount() {
    this.loadRoleDepartments();
  }

  // 加载角色关联的部门
  loadRoleDepartments = async () => {
    const { role } = this.props;
    if (!role) return;

    try {
      const response = await roleApi.getRoleDepartments(role.id);
      if (response.success) {
        const roleDepartments = response.data.departments || [];
        const targetKeys = roleDepartments.map((dept) => dept.deptId.toString());

        this.setState({
          roleDepartments,
          targetKeys,
        });
      }
    } catch (error) {
      console.error('Load role departments failed:', error);
    }
  };

  // 处理部门变更
  handleChange = (targetKeys) => {
    this.setState({ targetKeys });
  };

  // 处理选择变更
  handleSelectChange = (sourceSelectedKeys, targetSelectedKeys) => {
    this.setState({
      selectedKeys: [...sourceSelectedKeys, ...targetSelectedKeys],
    });
  };

  // 提交部门分配
  handleSubmit = async () => {
    const { role, onSuccess } = this.props;
    const { targetKeys } = this.state;

    this.setState({ loading: true });

    try {
      const departmentIds = targetKeys.map((key) => parseInt(key, 10));
      const response = await roleApi.updateRoleDepartments(role.id, departmentIds);

      if (response.success) {
        message.success('部门分配成功');
        onSuccess();
      } else {
        message.error(response.message || '部门分配失败');
      }
    } catch (error) {
      console.error('Department assign failed:', error);
      message.error('部门分配失败');
    } finally {
      this.setState({ loading: false });
    }
  };

  // 渲染部门项
  renderDepartmentItem = (item) => {
    // const customLabel = (
    //   <div style={{ display: 'flex', alignItems: 'center', padding: '8px 0' }}>
    //     <div style={{ flex: 1 }}>
    //       <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
    //         {item.name}
    //       </div>
    //       <div style={{ fontSize: '12px', color: '#666' }}>
    //         {item.description || '暂无描述'}
    //       </div>
    //       <div style={{ marginTop: 4 }}>
    //         <Tag color="blue" size="small">
    //           ID: {item.id}
    //         </Tag>
    //       </div>
    //     </div>
    //   </div>
    // );
    return {
      key: item.deptId.toString(),
      title: item.name,
      description: item.description,
    };
  };

  render() {
    const { visible, role, onCancel } = this.props;
    const { loading, targetKeys, selectedKeys, roleDepartments } = this.state;

    if (!role) {
      return null;
    }

    // 获取所有部门数据
    const allDepartments = this.props.store.departments || [];
    console.log('[ allDepartments ] >', allDepartments);
    const dataSource = allDepartments.map(this.renderDepartmentItem);

    return (
      <Modal
        title="分配部门"
        visible={visible}
        onOk={this.handleSubmit}
        onCancel={onCancel}
        confirmLoading={loading}
        width={800}
        destroyOnClose
      >
        <div style={{ marginBottom: 16 }}>
          <Card size="small">
            <Descriptions column={2} size="small">
              <Descriptions.Item label="角色名称">
                {role.name}
              </Descriptions.Item>
              <Descriptions.Item label="角色编码">
                {role.code}
              </Descriptions.Item>
              <Descriptions.Item label="角色描述" span={2}>
                {role.description || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="当前关联部门" span={2}>
                {roleDepartments.length > 0 ? (
                  roleDepartments.map((dept) => (
                    <Tag key={dept.id} color="green" style={{ marginBottom: 4 }}>
                      {dept.name}
                    </Tag>
                  ))
                ) : (
                  <Tag color="default">未关联部门</Tag>
                )}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </div>

        <Alert
          message="部门关联说明"
          description={
            <div>
              <p>• 将角色与部门关联后，该部门的所有用户将自动获得此角色</p>
              <p>• 部门关联是动态的，新加入部门的用户也会自动获得角色</p>
              <p>• 用户离开部门时，会自动失去通过部门获得的角色</p>
            </div>
          }
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Transfer
          dataSource={dataSource}
          titles={['可选部门', '已关联部门']}
          targetKeys={targetKeys}
          selectedKeys={selectedKeys}
          onChange={this.handleChange}
          onSelectChange={this.handleSelectChange}
          render={(item) => item.title}
          listStyle={{
            width: 300,
            height: 400,
          }}
          operations={['关联', '移除']}
          showSearch
          searchPlaceholder="搜索部门"
          filterOption={(inputValue, option) =>
            option.title.props.children[0].props.children[0].props.children
              .toLowerCase()
              .includes(inputValue.toLowerCase())
          }
        />

        <div style={{ marginTop: 16, fontSize: '12px', color: '#666' }}>
          <p>• 左侧显示所有可用部门，右侧显示角色当前关联的部门</p>
          <p>• 部门关联后，该部门的用户将自动获得此角色的所有权限</p>
          <p>• 建议根据职能需要合理分配部门角色</p>
        </div>
      </Modal>
    );
  }
}

export default withStore(DepartmentAssignModal);
