import React, { Component } from 'react';
import {
  Modal,
  Form,
  Input,
  Switch,
  message,
  Row,
  Col,
  InputNumber,
} from 'antd';
import { roleApi } from '../../services/api';

const { TextArea } = Input;

class RoleFormModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
    };
  }

  // 表单提交
  handleSubmit = () => {
    const { form, role, onSuccess } = this.props;

    form.validateFields(async (err, values) => {
      if (err) {
        message.error('请检查表单填写是否正确');
        return;
      }

      this.setState({ loading: true });

      try {
        const formData = {
          ...values,
          status: values.status ? 'active' : 'inactive',
        };

        let response;
        if (role) {
          // 编辑角色
          response = await roleApi.updateRole(role.id, formData);
        } else {
          // 新增角色
          response = await roleApi.createRole(formData);
        }

        if (response.success) {
          onSuccess();
        } else {
          message.error(response.message || '操作失败');
        }
      } catch (error) {
        console.error('Role form submit failed:', error);
        message.error('操作失败');
      } finally {
        this.setState({ loading: false });
      }
    });
  };

  // 生成角色编码
  generateRoleCode = (name) => {
    if (!name) return '';

    // 简单的编码生成逻辑：取拼音首字母或英文首字母
    const code = name
      .replace(/[^\u4e00-\u9fa5a-zA-Z]/g, '') // 只保留中文和英文
      .substring(0, 10) // 最多10个字符
      .toUpperCase();

    return `ROLE_${code}_${Date.now().toString().slice(-4)}`;
  };

  // 角色名称变化时自动生成编码
  handleNameChange = (e) => {
    const { form, role } = this.props;
    const displayName = e.target.value;

    // 只有新增时才自动生成编码
    if (!role && displayName) {
      const name = this.generateRoleCode(displayName);
      form.setFieldsValue({ name });
    }
  };

  render() {
    const { visible, role, onCancel, form } = this.props;
    const { loading } = this.state;
    const { getFieldDecorator } = form;

    const isEdit = !!role;

    return (
      <Modal
        title={isEdit ? '编辑角色' : '新增角色'}
        visible={visible}
        onOk={this.handleSubmit}
        onCancel={onCancel}
        confirmLoading={loading}
        width={600}
        destroyOnClose
      >
        <Form layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="角色名称">
                {getFieldDecorator('displayName', {
                  initialValue: role?.displayName || '',
                  rules: [
                    { required: true, message: '请输入角色名称' },
                    { max: 50, message: '角色名称不能超过50个字符' },
                  ],
                })(
                  <Input
                    placeholder="请输入角色名称"
                    onChange={this.handleNameChange}
                  />,
                )}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="角色编码">
                {getFieldDecorator('name', {
                  initialValue: role?.name || '',
                  rules: [
                    { required: true, message: '请输入角色编码' },
                    { max: 50, message: '角色编码不能超过50个字符' },
                    {
                      pattern: /^[A-Z0-9_]+$/,
                      message: '角色编码只能包含大写字母、数字和下划线',
                    },
                  ],
                })(
                  <Input
                    placeholder="请输入角色编码"
                    disabled={isEdit} // 编辑时不允许修改编码
                  />,
                )}
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="角色描述">
            {getFieldDecorator('description', {
              initialValue: role?.description || '',
              rules: [
                { max: 200, message: '角色描述不能超过200个字符' },
              ],
            })(
              <TextArea
                placeholder="请输入角色描述"
                rows={3}
                maxLength={200}
              />,
            )}
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="排序">
                {getFieldDecorator('sort', {
                  initialValue: role?.sort || 0,
                })(
                  <InputNumber
                    placeholder="排序值"
                    min={0}
                    max={9999}
                    style={{ width: '100%' }}
                  />,
                )}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="状态">
                {getFieldDecorator('status', {
                  initialValue: role?.status === 'active' || !role,
                  valuePropName: 'checked',
                })(
                  <Switch
                    checkedChildren="启用"
                    unCheckedChildren="禁用"
                  />,
                )}
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="备注">
            {getFieldDecorator('remark', {
              initialValue: role?.remark || '',
            })(
              <TextArea
                placeholder="请输入备注信息"
                rows={2}
                maxLength={500}
              />,
            )}
          </Form.Item>
        </Form>

        <div style={{ marginTop: 16, fontSize: '12px', color: '#666' }}>
          <p>• 角色编码创建后不可修改，请谨慎填写</p>
          <p>• 角色名称用于显示，角色编码用于系统内部标识</p>
          <p>• 排序值越小，角色在列表中的位置越靠前</p>
        </div>
      </Modal>
    );
  }
}

export default Form.create()(RoleFormModal);
