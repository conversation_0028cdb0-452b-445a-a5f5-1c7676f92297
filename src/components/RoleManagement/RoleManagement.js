import React, { Component } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Row,
  Col,
  message,
  Tag,
  Tooltip,
  Popconfirm,
  Badge,
  Divider,
} from 'antd';
import { roleApi, permissionApi } from '../../services/api';
import RoleFormModal from './RoleFormModal';
import PermissionAssignModal from './PermissionAssignModal';
import DepartmentAssignModal from './DepartmentAssignModal';
import { withStore } from '../../store/withStore';

const { Search } = Input;
const { Option } = Select;

class RoleManagement extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      roles: [],
      permissions: [],
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0,
      },
      filters: {
        isActive: undefined,
        keyword: '',
      },
      roleFormVisible: false,
      permissionAssignVisible: false,
      departmentAssignVisible: false,
      selectedRole: null,
      selectedRowKeys: [],
    };
    console.log('props', this.props);
  }

  componentDidMount() {
    this.loadRoles();
    this.loadPermissions();
  }

  // 加载角色列表
  loadRoles = async (params = {}) => {
    this.setState({ loading: true });

    try {
      const { pagination, filters } = this.state;
      const requestParams = {
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...filters,
        ...params,
      };

      const response = await roleApi.getRoles(requestParams);

      if (response.success) {
        this.setState({
          roles: response.data.roles || [],
          pagination: {
            ...pagination,
            total: response.data.total || 0,
          },
        });
      } else {
        message.error(response.message || '获取角色列表失败');
      }
    } catch (error) {
      console.error('Load roles failed:', error);
      message.error('获取角色列表失败');
    } finally {
      this.setState({ loading: false });
    }
  };

  // 加载权限列表
  loadPermissions = async () => {
    try {
      const response = await permissionApi.getPermissions();
      if (response.success) {
        this.setState({ permissions: response.data.permissions || [] });
      }
    } catch (error) {
      console.error('Load permissions failed:', error);
    }
  };

  // 处理搜索
  handleSearch = (keyword) => {
    this.setState(
      (prevState) => ({
        filters: { ...prevState.filters, keyword },
        pagination: { ...prevState.pagination, current: 1 },
      }),
      () => this.loadRoles(),
    );
  };

  // 处理筛选
  handleFilterChange = (key, value) => {
    this.setState(
      (prevState) => ({
        filters: { ...prevState.filters, [key]: value },
        pagination: { ...prevState.pagination, current: 1 },
      }),
      () => this.loadRoles(),
    );
  };

  // 处理分页
  handleTableChange = (pagination) => {
    this.setState({ pagination }, () => this.loadRoles());
  };

  // 打开角色表单
  handleOpenRoleForm = (role = null) => {
    this.setState({
      roleFormVisible: true,
      selectedRole: role,
    });
  };

  // 关闭角色表单
  handleCloseRoleForm = () => {
    this.setState({
      roleFormVisible: false,
      selectedRole: null,
    });
  };

  // 角色表单提交成功
  handleRoleFormSuccess = () => {
    this.handleCloseRoleForm();
    this.loadRoles();
    message.success('操作成功');
  };

  // 打开权限分配
  handleOpenPermissionAssign = (role) => {
    this.setState({
      permissionAssignVisible: true,
      selectedRole: role,
    });
  };

  // 关闭权限分配
  handleClosePermissionAssign = () => {
    this.setState({
      permissionAssignVisible: false,
      selectedRole: null,
    });
  };

  // 权限分配成功
  handlePermissionAssignSuccess = () => {
    this.handleClosePermissionAssign();
    this.loadRoles();
    message.success('权限分配成功');
  };

  // 打开部门分配
  handleOpenDepartmentAssign = (role) => {
    this.setState({
      departmentAssignVisible: true,
      selectedRole: role,
    });
  };

  // 关闭部门分配
  handleCloseDepartmentAssign = () => {
    this.setState({
      departmentAssignVisible: false,
      selectedRole: null,
    });
  };

  // 部门分配成功
  handleDepartmentAssignSuccess = () => {
    this.handleCloseDepartmentAssign();
    this.loadRoles();
    message.success('部门分配成功');
  };

  // 删除角色
  handleDeleteRole = async (roleId) => {
    try {
      const response = await roleApi.deleteRole(roleId);
      if (response.success) {
        message.success('角色删除成功');
        this.loadRoles();
      } else {
        message.error(response.message || '角色删除失败');
      }
    } catch (error) {
      console.error('Delete role failed:', error);
      message.error(error.message || '角色删除失败');
    }
  };

  // 渲染状态标签
  renderStatusTag = (status) => {
    const statusMap = {
      true: { color: 'green', text: '正常' },
      false: { color: 'red', text: '禁用' },
    };

    const config = statusMap[status] || { color: 'default', text: '未知' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 渲染权限标签,需要转成名字
  renderPermissionTags = (rolePermissions) => {
    if (!rolePermissions || rolePermissions.length === 0) {
      return <Tag color="default">无权限</Tag>;
    }

    const displayPermissions = rolePermissions.slice(0, 3).map((item) => item.permission);
    const remainingCount = rolePermissions.length - 3;

    return (
      <div>
        {displayPermissions.map((permission) => (
          <Tag key={permission.id} color="blue" size="small">
            {permission.displayName || permission.name}
          </Tag>
        ))}
        {remainingCount > 0 && (
          <Tooltip
            title={rolePermissions
              .slice(3)
              .map((p) => p.permission.displayName || p.permission.name)
              .join(', ')}
          >
            <Tag color="blue" size="small">
              +{remainingCount}
            </Tag>
          </Tooltip>
        )}
      </div>
    );
  };

  render() {
    const {
      loading,
      roles,
      permissions,
      pagination,
      filters,
      roleFormVisible,
      permissionAssignVisible,
      departmentAssignVisible,
      selectedRole,
      selectedRowKeys,
    } = this.state;

    const columns = [
      {
        title: '角色名称',
        dataIndex: 'displayName',
        width: 150,
        render: (text, record) => (
          <div>
            <div style={{ fontWeight: 'bold' }}>{text}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {record.code}
            </div>
          </div>
        ),
      },
      {
        title: '描述',
        dataIndex: 'description',
        width: 200,
        ellipsis: true,
        render: (text) => text || '-',
      },
      {
        title: '权限',
        dataIndex: 'rolePermissions',
        width: 250,
        render: (p) => this.renderPermissionTags(p),
      },
      {
        title: '用户数',
        dataIndex: '_count',
        width: 80,
        render: (_count) => (
          <Badge count={_count?.userRoles || 0} showZero style={{ backgroundColor: '#52c41a' }} />
        ),
      },
      {
        title: '状态',
        dataIndex: 'isActive',
        width: 80,
        render: (status) => this.renderStatusTag(status),
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        width: 120,
        render: (time) => (time ? new Date(time).toLocaleDateString() : '-'),
      },
      {
        title: '操作',
        key: 'actions',
        width: 250,
        render: (_, record) => (
          <div>
            <Button
              type="link"
              size="small"
              onClick={() => this.handleOpenRoleForm(record)}
            >
              编辑
            </Button>
            <Divider type="vertical" />
            <Button
              type="link"
              size="small"
              onClick={() => this.handleOpenPermissionAssign(record)}
            >
              权限
            </Button>
            <Divider type="vertical" />
            <Button
              type="link"
              size="small"
              onClick={() => this.handleOpenDepartmentAssign(record)}
            >
              部门
            </Button>
            <Divider type="vertical" />
            <Popconfirm
              title="确定要删除这个角色吗？"
              onConfirm={() => this.handleDeleteRole(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" size="small" style={{ color: '#ff4d4f' }}>
                删除
              </Button>
            </Popconfirm>
          </div>
        ),
      },
    ];

    return (
      <div style={{ padding: '24px' }}>
        <Card
          title="角色管理"
          extra={
            <Button type="primary" onClick={() => this.handleOpenRoleForm()}>
              新增角色
            </Button>
          }
        >
          {/* 筛选区域 */}
          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col span={6}>
              <Search
                placeholder="搜索角色名称、编码"
                onSearch={this.handleSearch}
                allowClear
              />
            </Col>
            <Col span={4}>
              <Select
                placeholder="选择状态"
                value={filters.isActive}
                onChange={(value) => this.handleFilterChange('isActive', value)}
                allowClear
                style={{ width: '100%' }}
              >
                <Option value="active">正常</Option>
                <Option value="inactive">禁用</Option>
              </Select>
            </Col>
          </Row>

          {/* 角色表格 */}
          <Table
            columns={columns}
            dataSource={roles}
            rowKey="id"
            loading={loading}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`,
            }}
            onChange={this.handleTableChange}
            rowSelection={{
              selectedRowKeys,
              onChange: (keys) => this.setState({ selectedRowKeys: keys }),
            }}
          />
        </Card>

        {/* 角色表单弹窗 */}
        {roleFormVisible && (
          <RoleFormModal
            visible={roleFormVisible}
            role={selectedRole}
            onCancel={this.handleCloseRoleForm}
            onSuccess={this.handleRoleFormSuccess}
          />
        )}

        {/* 权限分配弹窗 */}
        {permissionAssignVisible && (
          <PermissionAssignModal
            visible={permissionAssignVisible}
            role={selectedRole}
            permissions={permissions}
            onCancel={this.handleClosePermissionAssign}
            onSuccess={this.handlePermissionAssignSuccess}
          />
        )}

        {/* 部门分配弹窗 */}
        {departmentAssignVisible && (
          <DepartmentAssignModal
            visible={departmentAssignVisible}
            role={selectedRole}
            onCancel={this.handleCloseDepartmentAssign}
            onSuccess={this.handleDepartmentAssignSuccess}
          />
        )}
      </div>
    );
  }
}

export default withStore(RoleManagement);
