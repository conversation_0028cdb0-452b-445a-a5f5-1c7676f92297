import React, { Component } from 'react';
import {
  Modal,
  Tree,
  message,
  Card,
  Tag,
  Descriptions,
  Spin,
  Alert,
} from 'antd';
import { roleApi } from '../../services/api';

const { TreeNode } = Tree;

class PermissionAssignModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      loadingPermissions: false,
      checkedKeys: [],
      expandedKeys: [],
      autoExpandParent: true,
      rolePermissions: [],
    };
  }

  componentDidMount() {
    this.loadRolePermissions();
    this.initializeExpandedKeys();
  }

  // 加载角色权限
  loadRolePermissions = async () => {
    const { role } = this.props;
    if (!role) return;

    this.setState({ loadingPermissions: true });

    try {
      const response = await roleApi.getRolePermissions(role.id);
      if (response.success) {
        console.log('[ permissions response ] >', response);
        const rolePermissions = response.data.permissions || [];
        const checkedKeys = rolePermissions.map((p) => p.id.toString());

        this.setState({
          rolePermissions,
          checkedKeys,
        });
      }
    } catch (error) {
      console.error('Load role permissions failed:', error);
    } finally {
      this.setState({ loadingPermissions: false });
    }
  };

  // 初始化展开的节点
  initializeExpandedKeys = () => {
    const { permissions } = this.props;
    const expandedKeys = [];

    // 获取所有模块节点
    const modules = [...new Set(permissions.map((p) => p.module))];
    expandedKeys.push(...modules);

    this.setState({ expandedKeys });
  };

  // 处理权限选择变更
  handleCheck = (checkedKeys) => {
    this.setState({ checkedKeys });
  };

  // 处理展开/收起
  handleExpand = (expandedKeys) => {
    this.setState({
      expandedKeys,
      autoExpandParent: false,
    });
  };

  // 提交权限分配
  handleSubmit = async () => {
    const { role, onSuccess } = this.props;
    const { checkedKeys } = this.state;
    this.setState({ loading: true });

    try {
      console.log('[ checkedKeys ] >', checkedKeys);
      const response = await roleApi.updateRolePermissions(role.id, checkedKeys.filter((item) => item.includes('cmc')));

      if (response.success) {
        message.success('权限分配成功');
        onSuccess();
      } else {
        message.error(response.message || '权限分配失败');
      }
    } catch (error) {
      console.error('Permission assign failed:', error);
      message.error('权限分配失败');
    } finally {
      this.setState({ loading: false });
    }
  };

  // 构建权限树数据
  buildPermissionTree = () => {
    const { permissions } = this.props;
    console.log('[ permissions ] >', permissions);

    // 按模块分组
    const groupedPermissions = (permissions || []).reduce((acc, permission) => {
      const module = permission.module || '其他';
      if (!acc[module]) {
        acc[module] = [];
      }
      acc[module].push(permission);
      return acc;
    }, {});

    // 模块名称映射（中文显示）
    const moduleNameMap = {
      user: '用户管理',
      role: '角色管理',
      permission: '权限管理',
      department: '部门管理',
      supplier: '供应商管理',
      budget: '预算管理',
      project: '项目管理',
      brand: '品牌管理',
      finance: '财务管理',
      report: '报表管理',
      system: '系统管理',
    };

    return Object.entries(groupedPermissions).map(([module, perms]) => ({
      title: moduleNameMap[module] || module,
      key: module,
      children: perms.map((permission) => ({
        title: (
          <div>
            <span style={{ fontWeight: 'bold' }}>{permission.displayName}</span>
            {/* {permission.description && (
              <div style={{ fontSize: '12px', color: '#666', marginTop: 2 }}>
                {permission.description}
              </div>
            )} */}
          </div>
        ),
        key: permission.id.toString(),
        isLeaf: true,
      })),
    }));
  };

  // 渲染树节点
  renderTreeNodes = (data) => {
    return data.map((item) => {
      if (item.children) {
        return (
          <TreeNode title={item.title} key={item.key} dataRef={item}>
            {this.renderTreeNodes(item.children)}
          </TreeNode>
        );
      }
      return <TreeNode title={item.title} key={item.key} dataRef={item} />;
    });
  };

  render() {
    const { visible, role, onCancel } = this.props;
    const {
      loading,
      loadingPermissions,
      checkedKeys,
      expandedKeys,
      autoExpandParent,
      rolePermissions,
    } = this.state;

    if (!role) {
      return null;
    }

    const treeData = this.buildPermissionTree();
    console.log('[ treeData ] >', treeData);

    return (
      <Modal
        title="分配权限"
        visible={visible}
        onOk={this.handleSubmit}
        onCancel={onCancel}
        confirmLoading={loading}
        width={800}
        destroyOnClose
      >
        <div style={{ marginBottom: 16 }}>
          <Card size="small">
            <Descriptions column={2} size="small">
              <Descriptions.Item label="角色名称">
                {role.name}
              </Descriptions.Item>
              <Descriptions.Item label="角色编码">
                {role.code}
              </Descriptions.Item>
              <Descriptions.Item label="角色描述" span={2}>
                {role.description || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="当前权限" span={2}>
                {rolePermissions.length > 0 ? (
                  <div>
                    {rolePermissions.slice(0, 5).map((permission) => (
                      <Tag key={permission.id} color="blue" style={{ marginBottom: 4 }}>
                        {permission.displayName || permission.name}
                      </Tag>
                    ))}
                    {rolePermissions.length > 5 && (
                      <Tag color="blue">+{rolePermissions.length - 5}</Tag>
                    )}
                  </div>
                ) : (
                  <Tag color="default">无权限</Tag>
                )}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </div>

        <Alert
          message="权限说明"
          description="请勾选要分配给该角色的权限。权限按功能模块分类，选择后将立即生效。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Spin spinning={loadingPermissions}>
          <div style={{ border: '1px solid #d9d9d9', borderRadius: 4, padding: 16 }}>
            <Tree
              checkable
              checkedKeys={checkedKeys}
              expandedKeys={expandedKeys}
              autoExpandParent={autoExpandParent}
              onCheck={this.handleCheck}
              onExpand={this.handleExpand}
              height={400}
            >
              {this.renderTreeNodes(treeData)}
            </Tree>
          </div>
        </Spin>

        <div style={{ marginTop: 16, fontSize: '12px', color: '#666' }}>
          <p>• 权限按功能模块分类，可以展开查看具体权限</p>
          <p>• 选择权限后会立即生效，拥有该角色的用户将获得相应权限</p>
          <p>• 建议按照最小权限原则分配权限</p>
        </div>
      </Modal>
    );
  }
}

export default PermissionAssignModal;
