import React, { useEffect } from 'react';
import { Table, Button, Tag, Avatar, message } from 'antd';
import { useUsers, useDepartments } from '../../store/hooks';
// import { useUsers, useDepartments, useNameMappings } from '../../store/hooks';

const UserList = () => {
  const {
    users,
    loading,
    error,
    fetchUsers,
    syncDingTalkUsers,
  } = useUsers();

  const { getDepartmentName } = useDepartments();
  // const { getUserName } = useNameMappings();
  // 组件挂载时加载用户数据
  useEffect(() => {
    fetchUsers().catch((err) => {
      console.error('Failed to fetch users:', err);
    });
  }, [fetchUsers]);

  // 处理同步钉钉用户
  const handleSyncUsers = async () => {
    try {
      await syncDingTalkUsers();
      message.success('用户同步成功');
    } catch (err) {
      message.error(`用户同步失败: ${ err.message}`);
    }
  };

  // 渲染状态标签
  const renderStatusTag = (status) => {
    const statusMap = {
      active: { color: 'green', text: '正常' },
      inactive: { color: 'red', text: '禁用' },
      pending: { color: 'orange', text: '待激活' },
    };

    const config = statusMap[status] || { color: 'default', text: '未知' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 渲染角色标签
  const renderRoleTags = (roles) => {
    if (!roles || roles.length === 0) {
      return <Tag color="default">无角色</Tag>;
    }

    return roles.slice(0, 2).map((role) => (
      <Tag key={role.id} color="blue">
        {role.name}
      </Tag>
    ));
  };

  const columns = [
    {
      title: '用户信息',
      key: 'userInfo',
      width: 200,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar
            size="small"
            src={record.avatar}
            icon="user"
            style={{ marginRight: 8 }}
          />
          <div>
            <div style={{ fontWeight: 'bold' }}>{record.name}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {record.mobile}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '钉钉ID',
      dataIndex: 'dingTalkUserId',
      width: 120,
      render: (text) => text || '-',
    },
    {
      title: '部门',
      dataIndex: 'departmentId',
      width: 120,
      render: (departmentId) => getDepartmentName(departmentId),
    },
    {
      title: '角色',
      dataIndex: 'roles',
      width: 150,
      render: (roles) => renderRoleTags(roles),
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 80,
      render: (status) => renderStatusTag(status),
    },
    {
      title: '最后登录',
      dataIndex: 'lastLoginAt',
      width: 120,
      render: (time) => (time ? new Date(time).toLocaleDateString() : '-'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <div>
          <Button type="link" size="small">
            编辑
          </Button>
          <Button type="link" size="small">
            分配角色
          </Button>
          <Button type="link" size="small" style={{ color: '#ff4d4f' }}>
            {record.status === 'active' ? '禁用' : '启用'}
          </Button>
        </div>
      ),
    },
  ];

  if (error) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <p>加载用户数据失败: {error}</p>
        <Button onClick={() => fetchUsers()}>重试</Button>
      </div>
    );
  }

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          onClick={handleSyncUsers}
          loading={loading}
          style={{ marginRight: 8 }}
        >
          同步钉钉用户
        </Button>
        <Button>新增用户</Button>
      </div>

      <Table
        columns={columns}
        dataSource={users}
        rowKey="id"
        loading={loading}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
        }}
      />
    </div>
  );
};

export default UserList;
