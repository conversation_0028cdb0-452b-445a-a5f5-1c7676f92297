import React, { Component } from 'react';
import {
  Card,
  Tabs,
  // Row,
  // Col,
  // Statistic,
  // Icon,
  // Button,
  // Table,
  // Tag,
  // message,
  // Modal,
  // Form,
  // Input,
  // Select,
  // Switch,
} from 'antd';
// import UserList from './UserList';
// import { useStats } from '../../store/hooks';
import { withStore } from '../../store/withStore';
import UserManagement from '../UserManagement/UserManagement';
import RoleManagement from '../RoleManagement/RoleManagement';
import PermissionManagement from '../PermissionManagement/PermissionManagement';

const { TabPane } = Tabs;
// const { Option } = Select;

// 统计卡片组件
// const StatsCards = () => {
//   const { userStats, roleStats } = useStats();

//   return (
//     <Row gutter={16} style={{ marginBottom: 24 }}>
//       <Col span={6}>
//         <Card>
//           <Statistic
//             title="用户总数"
//             value={userStats.total}
//             prefix={<Icon type="user" />}
//             valueStyle={{ color: '#3f8600' }}
//           />
//         </Card>
//       </Col>
//       <Col span={6}>
//         <Card>
//           <Statistic
//             title="正常用户"
//             value={userStats.active}
//             prefix={<Icon type="check-circle" />}
//             valueStyle={{ color: '#52c41a' }}
//           />
//         </Card>
//       </Col>
//       <Col span={6}>
//         <Card>
//           <Statistic
//             title="角色总数"
//             value={roleStats.total}
//             prefix={<Icon type="team" />}
//             valueStyle={{ color: '#1890ff' }}
//           />
//         </Card>
//       </Col>
//       <Col span={6}>
//         <Card>
//           <Statistic
//             title="活跃角色"
//             value={roleStats.active}
//             prefix={<Icon type="safety-certificate" />}
//             valueStyle={{ color: '#722ed1' }}
//           />
//         </Card>
//       </Col>
//     </Row>
//   );
// };

class SystemManagement extends Component {
  constructor(props) {
    super(props);
    this.state = {
      activeTab: 'users',
    };
  }

  // 渲染用户管理
  renderUserManagement = () => {
    return (
      <UserManagement />
    );
  };

  // 渲染角色管理
  renderRoleManagement = () => {
    return (<RoleManagement />);
  };

  // 渲染权限管理
  renderPermissionManagement = () => {
    return (<PermissionManagement />);
  };

  render() {
    const { activeTab } = this.state;
    // const { store } = this.props; // 从 HOC 获取 store 数据

    return (
      <div style={{ padding: '24px' }}>
        {/* 统计卡片 */}
        {/* <StatsCards /> */}

        {/* 管理选项卡 */}
        <Card>
          <Tabs
            activeKey={activeTab}
            onChange={(key) => this.setState({ activeTab: key })}
          >
            <TabPane tab="用户管理" key="users">
              {this.renderUserManagement()}
            </TabPane>
            <TabPane tab="角色管理" key="roles">
              {this.renderRoleManagement()}
            </TabPane>
            <TabPane tab="权限管理" key="permissions">
              {this.renderPermissionManagement()}
            </TabPane>
          </Tabs>
        </Card>
      </div>
    );
  }
}

export default withStore(SystemManagement);
