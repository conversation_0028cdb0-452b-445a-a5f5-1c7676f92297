import React, { Component } from 'react';
import { Result, Button } from 'antd';

class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    console.error('[ error ] >', error);
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // 你同样可以将错误日志上报给服务器
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: null, errorInfo: null });
  };

  render() {
    if (this.state.hasError) {
      const { title = '出现了一些问题', subTitle = '页面加载失败，请稍后重试' } = this.props;

      return (
        <div style={{ padding: '20px' }}>
          <Result
            status="error"
            title={title}
            subTitle={subTitle}
            extra={[
              <Button type="primary" key="retry" onClick={this.handleRetry}>
                重试
              </Button>,
              <Button key="close" onClick={this.props.onClose}>
                关闭
              </Button>,
            ]}
          >
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <div style={{ textAlign: 'left', marginTop: 16 }}>
                <details style={{ whiteSpace: 'pre-wrap' }}>
                  <summary>错误详情（开发模式）</summary>
                  <p><strong>错误信息：</strong>{this.state.error.toString()}</p>
                  <p><strong>错误堆栈：</strong></p>
                  <pre>{this.state.errorInfo.componentStack}</pre>
                </details>
              </div>
            )}
          </Result>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
