/* 简洁现代化品牌汇总页面 */
.modern-brand-summary {
  /* background: #fafbfc; */
  min-height: 100vh;
  padding: 0;
}

/* 精简页面头部 */
.summary-header {
  background: white;
  border-bottom: 1px solid #e8eaed;
  padding: 16px 24px;
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left .page-title {
  font-size: 20px;
  font-weight: 600;
  color: #202124;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  font-size: 20px;
  color: #1a73e8;
}

.page-description {
  font-size: 14px;
  color: #5f6368;
  margin: 4px 0 0 28px;
}

.header-filters {
  display: flex;
  gap: 16px;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  color: #5f6368;
  font-size: 14px;
  font-weight: 500;
}

.modern-date-picker {
  border: 1px solid #dadce0 !important;
  border-radius: 4px !important;
  height: 32px !important;
}

.modern-date-picker:hover {
  border-color: #1a73e8 !important;
}

/* 紧凑指标卡片区域 */
.metrics-section {
  padding: 0 24px;
  margin-bottom: 24px;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

.metric-card {
  background: white;
  border: 1px solid #e8eaed;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
  height: 88px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.metric-card:hover {
  border-color: #1a73e8;
  box-shadow: 0 1px 6px rgba(32, 33, 36, 0.1);
}

.metric-header {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.metric-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
  flex-shrink: 0;
}

.metric-primary .metric-icon {
  background: #1a73e8;
}

.metric-success .metric-icon {
  background: #137333;
}

.metric-warning .metric-icon {
  background: #f29900;
}

.metric-info .metric-icon {
  background: #9334e6;
}

.metric-content {
  flex: 1;
  min-width: 0;
}

.metric-title {
  font-size: 13px;
  color: #5f6368;
  font-weight: 500;
  margin-bottom: 4px;
  line-height: 1.2;
}

.metric-value {
  font-size: 20px;
  font-weight: 600;
  line-height: 1.2;
  color: #202124;
}

.metric-trend {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  white-space: nowrap;
  flex-shrink: 0;
}

.trend-up {
  color: #137333;
  background: #e6f4ea;
}

.trend-down {
  color: #d93025;
  background: #fce8e6;
}

/* 紧凑表格区域 */
.table-section {
  padding: 0 24px;
  margin-bottom: 24px;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

.modern-table-container {
  background: white;
  border: 1px solid #e8eaed;
  border-radius: 8px;
  overflow: hidden;
}

.table-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e8eaed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #202124;
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-icon {
  font-size: 16px;
  color: #1a73e8;
}

.data-count {
  font-size: 13px;
  color: #5f6368;
  background: #e8f0fe;
  padding: 4px 8px;
  border-radius: 4px;
}

.table-content {
  padding: 0;
}

.modern-table .ant-table-thead > tr > th {
  background: #f8f9fa;
  border-bottom: 1px solid #e8eaed;
  font-weight: 500;
  color: #5f6368;
  padding: 12px 16px;
  font-size: 13px;
}

.modern-table .ant-table-tbody > tr > td {
  padding: 12px 16px;
  border-bottom: 1px solid #f1f3f4;
  font-size: 14px;
}

.modern-table .ant-table-tbody > tr:hover > td {
  background: #f8f9fa;
}

.modern-table .ant-table-pagination {
  padding: 16px 20px;
  margin: 0;
}

/* 紧凑图表区域 */
.charts-section {
  padding: 0 24px;
  margin-bottom: 24px;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

.chart-card {
  background: white;
  border: 1px solid #e8eaed;
  border-radius: 8px;
  overflow: hidden;
  height: 240px;
  display: flex;
  flex-direction: column;
}

.chart-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e8eaed;
  background: #f8f9fa;
}

.chart-title {
  font-size: 14px;
  font-weight: 600;
  color: #202124;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-icon {
  font-size: 16px;
  color: #1a73e8;
}

.chart-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.chart-placeholder {
  text-align: center;
  color: #5f6368;
}

.placeholder-icon {
  font-size: 32px;
  color: #dadce0;
  margin-bottom: 12px;
  display: block;
}

.placeholder-text h4 {
  font-size: 14px;
  color: #5f6368;
  margin-bottom: 4px;
  font-weight: 500;
}

.placeholder-text p {
  font-size: 13px;
  color: #9aa0a6;
  margin: 0;
}

/* 简洁错误状态 */
.error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  padding: 24px;
}

.error-content {
  text-align: center;
  padding: 32px;
  background: white;
  border: 1px solid #e8eaed;
  border-radius: 8px;
  max-width: 400px;
}

.error-icon {
  font-size: 48px;
  color: #f29900;
  margin-bottom: 16px;
}

.error-content h3 {
  font-size: 18px;
  color: #202124;
  margin-bottom: 8px;
  font-weight: 600;
}

.error-content p {
  color: #5f6368;
  margin-bottom: 4px;
  font-size: 14px;
}

.error-detail {
  font-size: 12px;
  color: #9aa0a6;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .metrics-section,
  .table-section,
  .charts-section {
    max-width: 100%;
    padding-left: 20px;
    padding-right: 20px;
  }
}

@media (max-width: 768px) {
  .summary-header {
    padding: 12px 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .header-left .page-title {
    font-size: 18px;
  }

  .page-description {
    margin-left: 0;
    text-align: center;
  }

  .header-filters {
    width: 100%;
    justify-content: center;
  }

  .metric-card {
    height: auto;
    padding: 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .metric-header {
    width: 100%;
  }

  .metric-value {
    font-size: 18px;
  }

  .table-header {
    padding: 12px 16px;
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .modern-table .ant-table-thead > tr > th,
  .modern-table .ant-table-tbody > tr > td {
    padding: 8px 12px;
    font-size: 13px;
  }

  .metrics-section,
  .table-section,
  .charts-section {
    padding-left: 16px;
    padding-right: 16px;
  }

  .chart-card {
    height: 200px;
  }
}
