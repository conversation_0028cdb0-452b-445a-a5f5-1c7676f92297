import React, { Component } from 'react';
import {
  Card,
  Row,
  Col,
  Table,
  Select,
  DatePicker,
  Spin,
  message,
  Icon,
  Tag,
  Progress,
  // Divider,
  Button,
  Tooltip,
} from 'antd';
import { brandSummaryAPI, dataTransformers, mockDataGenerators } from '../../services/financialAPI';
import moment from 'moment';
import { brandAPI } from '../../services/api';
import { getContractSigningStatusConfig, getContractTypeLabel } from '../../utils/projectUtils';
import './BrandSummary.css'; // 使用与BrandSummary相同的样式

const { Option } = Select;
const { RangePicker } = DatePicker;

// 项目状态配置 - 与后端API枚举值保持一致
const PROJECT_STATUS = {
  draft: { label: '已创建' },
  active: { label: '执行中' },
  completed: { label: '已完成' },
  cancelled: { label: '已取消' },
};

class BrandExecution extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      brands: [],
      selectedBrandId: null,
      executionData: {
        brandInfo: {},
        projectStats: {},
        projects: [],
        monthlyTrend: [],
        budgetExecution: [],
      },
      filters: {
        dateRange: [moment().startOf('year'), moment().endOf('year')],
        projectStatus: [],
        includeCompleted: 'true',
        includeCancelled: 'false',
      },
    };
  }

  componentDidMount() {
    this.loadBrands();
  }

  componentDidCatch(error, errorInfo) {
    console.error('BrandExecution component error:', error, errorInfo);
  }

  // 加载品牌列表
  loadBrands = async () => {
    try {
      const response = await brandAPI.getBrands({ status: 'active' });
      if (response.success) {
        const brands = response.data.brands || [];
        this.setState({
          brands,
          selectedBrandId: brands.length > 0 ? brands[0].id : null,
        }, () => {
          if (this.state.selectedBrandId) {
            this.loadExecutionData();
          }
        });
      }
    } catch (error) {
      console.error('Load brands failed:', error);
      message.error('获取品牌列表失败');
    }
  };

  // 加载品牌执行数据
  loadExecutionData = async () => {
    const { selectedBrandId, filters } = this.state;
    if (!selectedBrandId) return;

    this.setState({ loading: true });

    try {
      const params = {};
      if (filters.dateRange && filters.dateRange.length === 2) {
        params.startDate = filters.dateRange[0].format('YYYY-MM-DD');
        params.endDate = filters.dateRange[1].format('YYYY-MM-DD');
      }
      if (filters.projectStatus && filters.projectStatus.length > 0) {
        params.projectStatus = filters.projectStatus;
      }
      if (filters.includeCompleted) {
        params.includeCompleted = filters.includeCompleted;
      }
      if (filters.includeCancelled) {
        params.includeCancelled = filters.includeCancelled;
      }

      // 调用API获取品牌财务详细报表
      const result = await brandSummaryAPI.getBrandDetail(selectedBrandId, params);

      if (result.success) {
        // 使用数据转换器转换API数据格式
        const executionData = dataTransformers.transformBrandDetailData(result.data);
        this.setState({ executionData });
      } else {
        throw new Error(result.message || '获取数据失败');
      }
    } catch (error) {
      console.warn('API调用失败，使用模拟数据:', error);

      // API调用失败时使用模拟数据
      const brandName = this.state.brands.find((b) => b.id === selectedBrandId)?.name || '未知品牌';
      const mockData = mockDataGenerators.generateBrandDetailMockData(selectedBrandId, brandName);
      this.setState({ executionData: mockData });

      message.error('获取品牌执行数据失败，已切换到模拟数据');
    } finally {
      this.setState({ loading: false });
    }
  };

  // 处理品牌选择变化
  handleBrandChange = (brandId) => {
    this.setState({ selectedBrandId: brandId }, () => {
      this.loadExecutionData();
    });
  };

  // 处理筛选条件变化
  handleFilterChange = (key, value) => {
    this.setState(
      (prevState) => ({
        filters: {
          ...prevState.filters,
          [key]: value,
        },
      }),
      () => {
        this.loadExecutionData();
      },
    );
  };

  // 格式化金额
  formatAmount = (amount) => {
    if (!amount) return '¥0';
    return `¥${amount.toLocaleString()}`;
  };

  // 渲染品牌详情卡片
  renderBrandDetailCard = () => {
    const { executionData } = this.state;
    const { brandInfo, reportMeta } = executionData;

    if (!brandInfo || !brandInfo.name) return null;

    return (
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {brandInfo.logo && (
              <img
                src={brandInfo.logo}
                alt={brandInfo.name}
                style={{ width: 32, height: 32, marginRight: 12, borderRadius: 4 }}
              />
            )}
            <div>
              <div style={{ fontSize: '18px', fontWeight: 'bold' }}>{brandInfo.name}</div>
              {brandInfo.description && (
                <div style={{ fontSize: '12px', color: '#666', fontWeight: 'normal' }}>
                  {brandInfo.description}
                </div>
              )}
            </div>
          </div>
        }
        extra={
          reportMeta?.generatedAt && (
            <div style={{ fontSize: '12px', color: '#666' }}>
              报表生成时间: {new Date(reportMeta.generatedAt).toLocaleString()}
            </div>
          )
        }
        style={{ marginBottom: 24 }}
      >
        {reportMeta?.reportPeriod && (
          <div style={{ marginBottom: 16, padding: 12, backgroundColor: '#f6f8fa', borderRadius: 4 }}>
            <Icon type="calendar" style={{ marginRight: 8, color: '#1890ff' }} />
            <span style={{ fontWeight: 'bold' }}>报表周期: </span>
            <span>
              {new Date(reportMeta.reportPeriod.startDate).toLocaleDateString()} 至{' '}
              {new Date(reportMeta.reportPeriod.endDate).toLocaleDateString()}
            </span>
          </div>
        )}
      </Card>
    );
  };

  // 渲染品牌统计信息
  renderBrandStats = () => {
    const { executionData } = this.state;
    const { projectStats } = executionData;

    const metricsData = [
      {
        title: '项目总数',
        value: projectStats.totalProjects || 0,
        icon: 'project',
        color: '#1a73e8',
        trend: `执行中: ${projectStats.activeProjects || 0}`,
        category: 'primary',
      },
      {
        title: '下单金额',
        value: projectStats.orderAmount || 0,
        formatter: this.formatAmount,
        icon: 'shopping-cart',
        color: '#1a73e8',
        trend: '+12.5%',
        category: 'primary',
      },
      {
        title: '已执行金额',
        value: projectStats.executedAmount || 0,
        formatter: this.formatAmount,
        icon: 'check-circle',
        color: '#137333',
        trend: '+8.3%',
        category: 'success',
      },
      {
        title: '执行中金额',
        value: projectStats.executingAmount || 0,
        formatter: this.formatAmount,
        icon: 'clock-circle',
        color: '#f29900',
        trend: '+5.2%',
        category: 'warning',
      },
      {
        title: '预估毛利',
        value: projectStats.estimatedProfit || 0,
        formatter: this.formatAmount,
        icon: 'rise',
        color: (projectStats.estimatedProfit || 0) >= 0 ? '#137333' : '#d93025',
        trend: '+15.2%',
        category: 'success',
      },
      {
        title: '毛利率',
        value: projectStats.estimatedProfitMargin || 0,
        suffix: '%',
        precision: 1,
        icon: 'percentage',
        color: '#9334e6',
        trend: '+2.1%',
        category: 'info',
      },
      {
        title: '已回款',
        value: projectStats.receivedAmount || 0,
        formatter: this.formatAmount,
        icon: 'bank',
        color: '#137333',
        trend: '+18.7%',
        category: 'success',
      },
      {
        title: '未回款',
        value: projectStats.unreceivedAmount || 0,
        formatter: this.formatAmount,
        icon: 'exclamation-circle',
        color: '#d93025',
        trend: '-5.4%',
        category: 'warning',
      },
    ];

    return (
      <Row gutter={[16, 16]}>
        {metricsData.map((metric) => (
          <Col xs={24} sm={12} lg={6} key={metric.title}>
            <div className={`metric-card metric-${metric.category}`}>
              <div className="metric-header">
                <div className="metric-icon">
                  <Icon type={metric.icon} />
                </div>
                <div className="metric-content">
                  <div className="metric-title">{metric.title}</div>
                  <div className="metric-value" style={{ color: metric.color }}>
                    {metric.formatter ?
                      metric.formatter(metric.value) :
                      `${metric.value.toLocaleString()}${metric.suffix || ''}`
                    }
                  </div>
                </div>
              </div>
              <div className="metric-trend">
                <span className={metric.trend.startsWith('+') ? 'trend-up' : 'trend-down'}>
                  {metric.trend}
                </span>
              </div>
            </div>
          </Col>
        ))}
      </Row>
    );
  };

  // 渲染项目列表
  renderProjectTable = () => {
    const { executionData } = this.state;

    const columns = [
      {
        title: '项目名称',
        dataIndex: 'projectName',
        key: 'projectName',
        width: 200,
        ellipsis: true,
        fixed: 'left',
      },
      {
        title: '执行周期',
        dataIndex: 'executionPeriod',
        key: 'executionPeriod',
        width: 180,
        render: (period) => {
          if (Array.isArray(period) && period.length === 2) {
            return `${period[0]} ~ ${period[1]}`;
          }
          return '-';
        },
      },
      {
        title: '项目状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        render: (status) => {
          const config = PROJECT_STATUS[status.toLowerCase()] || { label: status, color: '#d9d9d9' };
          return <Tag color={config.color}>{config.label}</Tag>;
        },
      },
      {
        title: '合同类型',
        dataIndex: 'contractType',
        key: 'contractType',
        width: 120,
        render: (type) => {
          return getContractTypeLabel(type);
        },
      },
      {
        title: '合同签署状态',
        dataIndex: 'contractSigningStatus',
        key: 'contractSigningStatus',
        width: 120,
        render: (status) => {
          if (!status) return '-';
          const config = getContractSigningStatusConfig(status);
          return (
            <Tag color={config.color}>
              {config.label}
            </Tag>
          );
        },
      },
      {
        title: '预算金额',
        dataIndex: 'planningBudget',
        key: 'planningBudget',
        width: 120,
        render: (amount) => this.formatAmount(amount),
        sorter: (a, b) => (a.planningBudget || 0) - (b.planningBudget || 0),
      },
      {
        title: '项目利润',
        dataIndex: 'projectProfit',
        key: 'projectProfit',
        width: 120,
        render: (profit) => (
          <span style={{ color: (profit || 0) >= 0 ? '#52c41a' : '#f5222d' }}>
            {this.formatAmount(profit)}
          </span>
        ),
        sorter: (a, b) => (a.projectProfit || 0) - (b.projectProfit || 0),
      },
      {
        title: '利润率',
        dataIndex: 'grossMargin',
        key: 'grossMargin',
        width: 100,
        render: (margin) => {
          const value = margin || 0;
          let color;
          if (value >= 20) {
            color = '#52c41a';
          } else if (value >= 10) {
            color = '#faad14';
          } else {
            color = '#f5222d';
          }
          return (
            <span style={{ color }}>
              {value.toFixed(1)}%
            </span>
          );
        },
        sorter: (a, b) => (a.grossMargin || 0) - (b.grossMargin || 0),
      },
      {
        title: '执行PM',
        dataIndex: 'executorPM',
        key: 'executorPM',
        width: 100,
        ellipsis: true,
      },
      {
        title: '执行进度',
        dataIndex: 'executionProgress',
        key: 'executionProgress',
        width: 120,
        render: (progress = 0) => {
          let status = 'normal';
          if (progress >= 100) {
            status = 'success';
          } else if (progress >= 80) {
            status = 'active';
          }
          return (
            <Progress
              percent={progress}
              size="small"
              status={status}
            />
          );
        },
      },
      {
        title: '操作',
        key: 'action',
        width: 100,
        fixed: 'right',
        render: (_, record) => (
          <Tooltip title="查看项目详情">
            <Button
              type="link"
              size="small"
              icon="eye"
              onClick={() => this.handleViewProject(record)}
            >
              查看
            </Button>
          </Tooltip>
        ),
      },
    ];

    return (
      <div className="modern-table-container">
        <div className="table-header">
          <div className="table-title">
            <Icon type="table" className="table-icon" />
            项目执行详细数据
          </div>
          <div className="table-actions">
            <span className="data-count">
              共 {executionData.projects ? executionData.projects.length : 0} 个项目
            </span>
          </div>
        </div>
        <div className="table-content">
          <Table
            columns={columns}
            dataSource={executionData.projects}
            rowKey="id"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条数据`,
            }}
            size="small"
            scroll={{ x: 1500 }}
            className="modern-table"
          />
        </div>
      </div>
    );
  };

  // 渲染执行趋势图
  renderTrendChart = () => {
    const { executionData } = this.state;

    return (
      <Card title="执行趋势分析" style={{ marginBottom: 24 }}>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Icon type="line-chart" style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
          <p>图表组件开发中...</p>
          <p style={{ color: '#999', fontSize: '12px' }}>
            趋势数据: {executionData.monthlyTrend ? executionData.monthlyTrend.length : 0} 个月
          </p>
        </div>
      </Card>
    );
  };

  // 查看项目详情
  handleViewProject = (project) => {
    // 这里可以打开项目详情弹窗或跳转到项目详情页
    message.info(`查看项目：${project.projectName}`);
  };

  render() {
    const { loading, brands, selectedBrandId, filters } = this.state;

    try {
      return (
        <div className="modern-brand-summary">
          <Spin spinning={loading}>
            {/* 页面头部 */}
            <div className="summary-header">
              <div className="header-content">
                <div className="header-left">
                  <h2 className="page-title">
                    <Icon type="line-chart" className="title-icon" />
                    品牌执行详情
                  </h2>
                  <p className="page-description">
                    深入了解品牌项目执行情况和财务表现
                  </p>
                </div>
                <div className="header-filters">
                  <div className="filter-item">
                    <span className="filter-label">选择品牌</span>
                    <Select
                      style={{ width: 200 }}
                      value={selectedBrandId}
                      onChange={this.handleBrandChange}
                      placeholder="请选择品牌"
                    >
                      {brands.map((brand) => (
                        <Option key={brand.id} value={brand.id}>
                          {brand.name}
                        </Option>
                      ))}
                    </Select>
                  </div>
                  {/* <div className="filter-item">
                    <span className="filter-label">项目状态</span>
                    <Select
                      mode="multiple"
                      style={{ width: 220 }}
                      value={filters.projectStatus}
                      onChange={(value) => this.handleFilterChange('projectStatus', value)}
                      placeholder="选择项目状态"
                      allowClear
                    >
                      {Object.entries(PROJECT_STATUS).map(([key, config]) => (
                        <Option key={key} value={key}>
                          <Tag size="small" style={{ marginRight: 4 }}>
                            {config.label}
                          </Tag>
                        </Option>
                      ))}
                    </Select>
                  </div>
                  <div className="filter-item">
                    <span className="filter-label">包含已完成</span>
                    <Select
                      style={{ width: 90 }}
                      value={filters.includeCompleted}
                      onChange={(value) => this.handleFilterChange('includeCompleted', value)}
                    >
                      <Option value="true">
                        是
                      </Option>
                      <Option value="false">
                        否
                      </Option>
                    </Select>
                  </div>
                  <div className="filter-item">
                    <span className="filter-label">包含已取消</span>
                    <Select
                      style={{ width: 90 }}
                      value={filters.includeCancelled}
                      onChange={(value) => this.handleFilterChange('includeCancelled', value)}
                    >
                      <Option value="true">
                        是
                      </Option>
                      <Option value="false">
                        否
                      </Option>
                    </Select>
                  </div> */}
                  <div className="filter-item">
                    <span className="filter-label">时间范围</span>
                    <RangePicker
                      value={filters.dateRange}
                      onChange={(dates) => this.handleFilterChange('dateRange', dates)}
                      format="YYYY-MM-DD"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* 高级筛选选项 */}
            {/* {selectedBrandId && (
              <Row gutter={16} align="middle">
                <Col span={6}>
                  <Select
                    mode="multiple"
                    style={{ width: 220, marginLeft: 8 }}
                    value={filters.projectStatus}
                    onChange={(value) => this.handleFilterChange('projectStatus', value)}
                    placeholder="选择项目状态"
                    allowClear
                  >
                    {Object.entries(PROJECT_STATUS).map(([key, config]) => (
                      <Option key={key} value={key}>
                        <Tag size="small" style={{ marginRight: 4 }}>
                          {config.label}
                        </Tag>
                      </Option>
                    ))}
                  </Select>
                </Col>
                <Col span={6}>
                  <Select
                    style={{ width: 90, marginLeft: 8 }}
                    value={filters.includeCompleted}
                    onChange={(value) => this.handleFilterChange('includeCompleted', value)}
                  >
                    <Option value="true">
                      是
                    </Option>
                    <Option value="false">
                      否
                    </Option>
                  </Select>
                </Col>

                <Col span={6}>
                  <Select
                    style={{ width: 90, marginLeft: 8 }}
                    value={filters.includeCancelled}
                    onChange={(value) => this.handleFilterChange('includeCancelled', value)}
                  >
                    <Option value="true">
                      是
                    </Option>
                    <Option value="false">
                      否
                    </Option>
                  </Select>
                </Col>
                <Col span={6}>
                  <Button
                    type="primary"
                    icon="search"
                    onClick={this.loadExecutionData}
                    style={{
                      background: '#1a73e8',
                      borderColor: '#1a73e8',
                      borderRadius: '4px',
                      height: '32px',
                      fontWeight: '500',
                    }}
                  >
                    查询数据
                  </Button>
                  <Button
                    icon="reload"
                    onClick={() => {
                      this.setState({
                        filters: {
                          dateRange: [moment().startOf('year'), moment().endOf('year')],
                          projectStatus: [],
                          includeCompleted: 'true',
                          includeCancelled: 'false',
                        },
                      }, () => {
                        this.loadExecutionData();
                      });
                    }}
                    style={{
                      marginLeft: '8px',
                      borderColor: '#dadce0',
                      color: '#5f6368',
                      borderRadius: '4px',
                      height: '32px',
                    }}
                  >
                    重置
                  </Button>
                </Col>
              </Row>
            )} */}

            {selectedBrandId && (
              <>

                {/* 核心指标卡片 */}
                <div className="metrics-section">
                  {this.renderBrandStats()}
                </div>

                {/* 项目详细数据表格 */}
                <div className="table-section">
                  {this.renderProjectTable()}
                </div>
              </>
            )}
          </Spin>
        </div>
      );
    } catch (error) {
      console.error('BrandExecution render error:', error);
      return (
        <div className="modern-brand-summary error-state">
          <div className="error-content">
            <Icon type="exclamation-circle" className="error-icon" />
            <h3>品牌执行数据加载失败</h3>
            <p>请刷新页面重试或联系管理员</p>
            <p className="error-detail">错误详情: {error.message}</p>
          </div>
        </div>
      );
    }
  }
}

export default BrandExecution;
