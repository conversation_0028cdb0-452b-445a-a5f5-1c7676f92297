import React, { Component } from 'react';
import { Card, Button, message, Spin, Descriptions, Table, Tag } from 'antd';
import { brandSummaryAPI, dataTransformers, mockDataGenerators } from '../../services/financialAPI';

/**
 * API测试页面
 * 用于测试财务报表API的对接情况
 */
class APITestPage extends Component {
  state = {
    loading: false,
    summaryData: null,
    brandDetailData: null,
    apiStatus: {
      summary: 'pending', // pending, success, error
      detail: 'pending',
    },
  };

  // 测试品牌汇总API
  testSummaryAPI = async () => {
    this.setState({ loading: true });
    try {
      console.log('🚀 测试品牌汇总API...');
      
      const params = {
        startDate: '2024-01-01',
        endDate: '2024-12-31',
      };
      
      const result = await brandSummaryAPI.getSummary(params);
      console.log('✅ 品牌汇总API响应:', result);
      
      if (result.success) {
        const transformedData = dataTransformers.transformSummaryData(result.data);
        this.setState({ 
          summaryData: transformedData,
          apiStatus: { ...this.state.apiStatus, summary: 'success' }
        });
        message.success('品牌汇总API测试成功');
      } else {
        throw new Error(result.message || 'API返回失败');
      }
    } catch (error) {
      console.error('❌ 品牌汇总API测试失败:', error);
      
      // 使用模拟数据
      const mockData = mockDataGenerators.generateSummaryMockData();
      this.setState({ 
        summaryData: mockData,
        apiStatus: { ...this.state.apiStatus, summary: 'error' }
      });
      message.warning('品牌汇总API测试失败，已使用模拟数据');
    } finally {
      this.setState({ loading: false });
    }
  };

  // 测试品牌详细API
  testBrandDetailAPI = async () => {
    this.setState({ loading: true });
    try {
      console.log('🚀 测试品牌详细API...');
      
      const brandId = 'brand-001';
      const params = {
        startDate: '2024-01-01',
        endDate: '2024-12-31',
      };
      
      const result = await brandSummaryAPI.getBrandDetail(brandId, params);
      console.log('✅ 品牌详细API响应:', result);
      
      if (result.success) {
        const transformedData = dataTransformers.transformBrandDetailData(result.data);
        this.setState({ 
          brandDetailData: transformedData,
          apiStatus: { ...this.state.apiStatus, detail: 'success' }
        });
        message.success('品牌详细API测试成功');
      } else {
        throw new Error(result.message || 'API返回失败');
      }
    } catch (error) {
      console.error('❌ 品牌详细API测试失败:', error);
      
      // 使用模拟数据
      const mockData = mockDataGenerators.generateBrandDetailMockData('brand-001', '测试品牌');
      this.setState({ 
        brandDetailData: mockData,
        apiStatus: { ...this.state.apiStatus, detail: 'error' }
      });
      message.warning('品牌详细API测试失败，已使用模拟数据');
    } finally {
      this.setState({ loading: false });
    }
  };

  // 渲染API状态
  renderAPIStatus = () => {
    const { apiStatus } = this.state;
    
    const getStatusTag = (status) => {
      switch (status) {
        case 'success':
          return <Tag color="green">成功</Tag>;
        case 'error':
          return <Tag color="red">失败</Tag>;
        default:
          return <Tag color="default">未测试</Tag>;
      }
    };

    return (
      <Card title="API状态" style={{ marginBottom: 24 }}>
        <Descriptions column={2}>
          <Descriptions.Item label="品牌汇总API">
            {getStatusTag(apiStatus.summary)}
          </Descriptions.Item>
          <Descriptions.Item label="品牌详细API">
            {getStatusTag(apiStatus.detail)}
          </Descriptions.Item>
        </Descriptions>
      </Card>
    );
  };

  // 渲染汇总数据
  renderSummaryData = () => {
    const { summaryData } = this.state;
    if (!summaryData) return null;

    return (
      <Card title="品牌汇总数据" style={{ marginBottom: 24 }}>
        <Descriptions column={2} bordered>
          <Descriptions.Item label="品牌总数">{summaryData.totalBrands}</Descriptions.Item>
          <Descriptions.Item label="项目总数">{summaryData.totalProjects}</Descriptions.Item>
          <Descriptions.Item label="总下单金额">¥{summaryData.totalOrderAmount?.toLocaleString()}</Descriptions.Item>
          <Descriptions.Item label="已执行金额">¥{summaryData.totalExecutedAmount?.toLocaleString()}</Descriptions.Item>
          <Descriptions.Item label="预估毛利">¥{summaryData.totalEstimatedProfit?.toLocaleString()}</Descriptions.Item>
          <Descriptions.Item label="平均毛利率">{summaryData.avgProfitMargin}%</Descriptions.Item>
        </Descriptions>
        
        <h4 style={{ marginTop: 16, marginBottom: 8 }}>品牌列表</h4>
        <Table
          dataSource={summaryData.brandStats}
          rowKey="brandId"
          size="small"
          pagination={false}
          columns={[
            { title: '排名', dataIndex: 'rank', width: 60 },
            { title: '品牌名称', dataIndex: 'brandName', width: 120 },
            { title: '项目数', dataIndex: 'projectCount', width: 80 },
            { 
              title: '下单金额', 
              dataIndex: 'orderAmount', 
              width: 120,
              render: (amount) => `¥${amount?.toLocaleString()}`
            },
            { 
              title: '毛利率', 
              dataIndex: 'profitMargin', 
              width: 80,
              render: (margin) => `${margin}%`
            },
          ]}
        />
      </Card>
    );
  };

  // 渲染详细数据
  renderDetailData = () => {
    const { brandDetailData } = this.state;
    if (!brandDetailData) return null;

    return (
      <Card title="品牌详细数据" style={{ marginBottom: 24 }}>
        <Descriptions column={2} bordered>
          <Descriptions.Item label="品牌名称">{brandDetailData.brandInfo?.name}</Descriptions.Item>
          <Descriptions.Item label="品牌描述">{brandDetailData.brandInfo?.description}</Descriptions.Item>
          <Descriptions.Item label="项目总数">{brandDetailData.projectStats?.totalProjects}</Descriptions.Item>
          <Descriptions.Item label="总预算">¥{brandDetailData.projectStats?.totalBudget?.toLocaleString()}</Descriptions.Item>
          <Descriptions.Item label="总利润">¥{brandDetailData.projectStats?.totalProfit?.toLocaleString()}</Descriptions.Item>
          <Descriptions.Item label="平均毛利率">{brandDetailData.projectStats?.avgProfitMargin}%</Descriptions.Item>
        </Descriptions>
        
        <h4 style={{ marginTop: 16, marginBottom: 8 }}>项目列表</h4>
        <Table
          dataSource={brandDetailData.projects}
          rowKey="id"
          size="small"
          pagination={false}
          columns={[
            { title: '项目名称', dataIndex: 'projectName', width: 150 },
            { 
              title: '执行周期', 
              dataIndex: 'executionPeriod', 
              width: 180,
              render: (period) => Array.isArray(period) ? `${period[0]} ~ ${period[1]}` : '-'
            },
            { 
              title: '状态', 
              dataIndex: 'status', 
              width: 80,
              render: (status) => <Tag>{status}</Tag>
            },
            { 
              title: '预算', 
              dataIndex: 'planningBudget', 
              width: 100,
              render: (amount) => `¥${amount?.toLocaleString()}`
            },
            { 
              title: '利润率', 
              dataIndex: 'grossMargin', 
              width: 80,
              render: (margin) => `${margin}%`
            },
          ]}
        />
      </Card>
    );
  };

  render() {
    const { loading } = this.state;

    return (
      <div style={{ padding: 24 }}>
        <Spin spinning={loading}>
          <Card title="财务报表API测试" style={{ marginBottom: 24 }}>
            <div style={{ marginBottom: 16 }}>
              <Button 
                type="primary" 
                onClick={this.testSummaryAPI}
                style={{ marginRight: 16 }}
              >
                测试品牌汇总API
              </Button>
              <Button 
                type="primary" 
                onClick={this.testBrandDetailAPI}
              >
                测试品牌详细API
              </Button>
            </div>
            <p style={{ color: '#666', fontSize: 14 }}>
              点击按钮测试API接口，如果API不可用会自动使用模拟数据。
              请查看浏览器控制台获取详细的API调用信息。
            </p>
          </Card>

          {this.renderAPIStatus()}
          {this.renderSummaryData()}
          {this.renderDetailData()}
        </Spin>
      </div>
    );
  }
}

export default APITestPage;
