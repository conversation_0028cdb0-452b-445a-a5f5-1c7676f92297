import React, { Component } from 'react';
import { Card, Row, Col, Statistic, Icon } from 'antd';

class TestCard extends Component {
  render() {
    return (
      <div style={{
        padding: '0',
        margin: '0',
        width: '100%',
        boxSizing: 'border-box',
      }}
      >
        <h2 style={{ marginBottom: '24px', padding: '0 24px' }}>Card 测试页面</h2>

        {/* 布局调试信息 */}
        <div style={{
          padding: '0 24px',
          marginBottom: '24px',
          background: '#f0f0f0',
          border: '1px solid #d9d9d9',
          borderRadius: '4px',
        }}
        >
          <h3>布局调试信息</h3>
          <p>容器宽度: {window.innerWidth}px</p>
          <p>如果您能看到这个灰色区域，说明布局是正常的。</p>
          <p>如果内容被截断或偏移，请检查CSS样式。</p>
        </div>

        {/* 基础Card测试 */}
        <div style={{ padding: '0 24px', marginBottom: '24px' }}>
          <Card title="基础Card测试">
            <p>这是一个基础的Card组件测试。如果您能看到这个内容，说明Card组件工作正常。</p>
          </Card>
        </div>

        {/* 统计Card测试 */}
        <div style={{ padding: '0 24px', marginBottom: '24px' }}>
          <Row gutter={16}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="测试数据1"
                  value={1234}
                  prefix={<Icon type="dollar" />}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="测试数据2"
                  value={5678}
                  prefix={<Icon type="rise" />}
                  valueStyle={{ color: '#cf1322' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="测试数据3"
                  value={9012}
                  prefix={<Icon type="percentage" />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="测试数据4"
                  value={3456}
                  prefix={<Icon type="fund" />}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
          </Row>
        </div>

        {/* 原生样式Card测试 */}
        <div style={{ padding: '0 24px', marginBottom: '24px' }}>
          <Card title="原生样式Card测试">
            <p>这是一个使用Ant Design原生样式的Card组件。</p>
            <p>如果这个Card显示正常，说明样式没有冲突。</p>
          </Card>
        </div>

        {/* 无边框Card测试 */}
        <div style={{ padding: '0 24px', marginBottom: '24px' }}>
          <Card title="无边框Card测试" bordered={false}>
            <p>这是一个无边框的Card组件。</p>
          </Card>
        </div>

        {/* 嵌套Card测试 */}
        <div style={{ padding: '0 24px', marginBottom: '24px' }}>
          <Card title="嵌套Card测试">
            <Row gutter={16}>
              <Col span={12}>
                <Card type="inner" title="内部Card 1">
                  <p>这是嵌套在外部Card中的内部Card。</p>
                </Card>
              </Col>
              <Col span={12}>
                <Card type="inner" title="内部Card 2">
                  <p>这是另一个内部Card。</p>
                </Card>
              </Col>
            </Row>
          </Card>
        </div>

        {/* 加载状态Card测试 */}
        <div style={{ padding: '0 24px', marginBottom: '24px' }}>
          <Card title="加载状态Card测试" loading>
            <p>这个Card应该显示加载状态。</p>
          </Card>
        </div>

        {/* 操作按钮Card测试 */}
        <div style={{ padding: '0 24px', marginBottom: '24px' }}>
          <Card title="操作按钮Card测试" extra={<a href="#">更多</a>}>
            <p>这个Card带有额外的操作按钮。</p>
          </Card>
        </div>
      </div>
    );
  }
}

export default TestCard;
