/* 应用加载容器 */
.app-loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.app-loading-content {
  width: 100%;
  max-width: 480px;
  padding: 20px;
}

/* 加载卡片 */
.loading-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px 32px;
  text-align: center;
}

/* 加载头部 */
.loading-header {
  margin-bottom: 32px;
}

.loading-icon {
  margin-bottom: 16px;
}

.loading-title {
  margin-bottom: 8px !important;
  color: #262626;
  font-weight: 600;
}

.loading-subtitle {
  color: #8c8c8c;
  font-size: 14px;
  font-weight: 400;
}

/* 进度条区域 */
.loading-progress {
  margin-bottom: 24px;
}

.loading-progress .ant-progress {
  margin-bottom: 12px;
}

.progress-text {
  color: #595959;
  font-size: 14px;
  font-weight: 500;
}

/* 加载提示 */
.loading-tips {
  color: #8c8c8c;
  font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-loading-content {
    max-width: 90%;
    padding: 16px;
  }
  
  .loading-card {
    padding: 32px 24px;
  }
  
  .loading-title {
    font-size: 20px !important;
  }
}

/* 动画效果 */
.loading-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 进度条自定义样式 */
.loading-progress .ant-progress-bg {
  transition: all 0.3s ease;
}

/* 加载图标动画 */
.loading-icon .anticon-loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
