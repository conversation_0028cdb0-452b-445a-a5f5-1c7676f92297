import React from 'react';
import { Card, Icon } from 'antd';
import BrandManagement from '../ProjectManagement/BrandManagement';
import { PageGuard } from '../Permission/PermissionGuard';
import { PAGE_PERMISSIONS } from '../../config/permissions';
import './BrandManagementPage.css';

const BrandManagementPage = () => {
  return (
    <PageGuard permissions={PAGE_PERMISSIONS.BRAND_MANAGEMENT}>
      <div className="brand-management-page">
        <div className="page-header">
          <div className="page-header-content">
            <div className="page-title">
              <Icon type="tags" className="page-icon" />
              <span>品牌管理</span>
            </div>
            <div className="page-description">
              管理系统中的所有品牌信息，包括品牌创建、编辑、状态管理等功能
            </div>
          </div>
        </div>

        <div className="page-content">
          <Card
            className="brand-management-card"
            bodyStyle={{ padding: '24px' }}
            bordered={false}
          >
            <BrandManagement />
          </Card>
        </div>
      </div>
    </PageGuard>
  );
};

export default BrandManagementPage;
