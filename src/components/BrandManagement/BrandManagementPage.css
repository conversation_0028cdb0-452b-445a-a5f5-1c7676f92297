.brand-management-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 32px 24px;
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header-content {
  max-width: 1200px;
  margin: 0 auto;
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 28px;
  font-weight: 600;
  color: white;
  margin-bottom: 8px;
}

.page-icon {
  font-size: 32px;
  margin-right: 12px;
  color: white;
}

.page-description {
  color: rgba(255, 255, 255, 0.85);
  font-size: 16px;
  line-height: 1.5;
  max-width: 600px;
}

.page-content {
  /* max-width: 1200px; */
  margin: 0 auto;
  padding: 0 24px;
}

.brand-management-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: none;
}

.brand-management-card .ant-card-body {
  padding: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 24px 16px;
    margin-bottom: 16px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .page-icon {
    font-size: 28px;
  }
  
  .page-description {
    font-size: 14px;
  }
  
  .page-content {
    padding: 0 16px;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .brand-management-page {
    background: #141414;
  }
  
  .page-header {
    background: linear-gradient(135deg, #434343 0%, #000000 100%);
  }
  
  .brand-management-card {
    background: #1f1f1f;
    border-color: #303030;
  }
}
