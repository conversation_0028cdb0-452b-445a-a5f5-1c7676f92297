/* 全局样式重置和基础样式 */
@import '~antd/dist/antd.css';

/* CSS 变量定义 */
:root {
  /* 主色调 */
  --color-primary: #1890ff;
  --color-primary-light: #40a9ff;
  --color-primary-dark: #096dd9;
  
  /* 功能色彩 */
  --color-success: #52c41a;
  --color-warning: #faad14;
  --color-error: #ff4d4f;
  --color-info: #1890ff;
  
  /* 文字颜色 */
  --color-text-primary: #262626;
  --color-text-secondary: #595959;
  --color-text-tertiary: #8c8c8c;
  --color-text-disabled: #bfbfbf;
  
  /* 背景色 */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #fafafa;
  --color-bg-tertiary: #f5f5f5;
  --color-bg-dark: #001529;
  
  /* 边框色 */
  --color-border-light: #f0f0f0;
  --color-border-base: #d9d9d9;
  --color-border-dark: #434343;
  
  /* 渐变色 */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-success: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  --gradient-warning: linear-gradient(135deg, #faad14 0%, #d48806 100%);
  --gradient-error: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);
  --gradient-background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-base: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  --spacing-3xl: 64px;
  
  /* 圆角 */
  --border-radius-sm: 4px;
  --border-radius-base: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  
  /* 阴影 */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-base: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
  --shadow-xl: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
  
  /* 动画 */
  --animation-duration-fast: 150ms;
  --animation-duration-base: 300ms;
  --animation-duration-slow: 500ms;
}

/* 全局重置 */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
  height: 100%;
}

body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--color-bg-secondary);
  color: var(--color-text-primary);
}

#root {
  height: 100%;
}

.App {
  height: 100%;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-tertiary);
  border-radius: var(--border-radius-base);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-base);
  border-radius: var(--border-radius-base);
  transition: background var(--animation-duration-base);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-tertiary);
}

/* 全局动画类 */
.fade-in {
  animation: fadeIn var(--animation-duration-base) ease-out;
}

.slide-in-up {
  animation: slideInUp var(--animation-duration-slow) ease-out;
}

.slide-in-down {
  animation: slideInDown var(--animation-duration-slow) ease-out;
}

.scale-in {
  animation: scaleIn var(--animation-duration-base) ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

/* 动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.font-weight-light {
  font-weight: 300;
}

.font-weight-normal {
  font-weight: 400;
}

.font-weight-medium {
  font-weight: 500;
}

.font-weight-semibold {
  font-weight: 600;
}

.font-weight-bold {
  font-weight: 700;
}

.text-primary {
  color: var(--color-primary);
}

.text-success {
  color: var(--color-success);
}

.text-warning {
  color: var(--color-warning);
}

.text-error {
  color: var(--color-error);
}

.text-secondary {
  color: var(--color-text-secondary);
}

.text-tertiary {
  color: var(--color-text-tertiary);
}

/* 间距工具类 */
.m-0 { margin: 0; }
.mt-0 { margin-top: 0; }
.mr-0 { margin-right: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }

.m-1 { margin: var(--spacing-xs); }
.mt-1 { margin-top: var(--spacing-xs); }
.mr-1 { margin-right: var(--spacing-xs); }
.mb-1 { margin-bottom: var(--spacing-xs); }
.ml-1 { margin-left: var(--spacing-xs); }

.m-2 { margin: var(--spacing-sm); }
.mt-2 { margin-top: var(--spacing-sm); }
.mr-2 { margin-right: var(--spacing-sm); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.ml-2 { margin-left: var(--spacing-sm); }

.m-3 { margin: var(--spacing-base); }
.mt-3 { margin-top: var(--spacing-base); }
.mr-3 { margin-right: var(--spacing-base); }
.mb-3 { margin-bottom: var(--spacing-base); }
.ml-3 { margin-left: var(--spacing-base); }

.m-4 { margin: var(--spacing-lg); }
.mt-4 { margin-top: var(--spacing-lg); }
.mr-4 { margin-right: var(--spacing-lg); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.ml-4 { margin-left: var(--spacing-lg); }

.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pr-0 { padding-right: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }

.p-1 { padding: var(--spacing-xs); }
.pt-1 { padding-top: var(--spacing-xs); }
.pr-1 { padding-right: var(--spacing-xs); }
.pb-1 { padding-bottom: var(--spacing-xs); }
.pl-1 { padding-left: var(--spacing-xs); }

.p-2 { padding: var(--spacing-sm); }
.pt-2 { padding-top: var(--spacing-sm); }
.pr-2 { padding-right: var(--spacing-sm); }
.pb-2 { padding-bottom: var(--spacing-sm); }
.pl-2 { padding-left: var(--spacing-sm); }

.p-3 { padding: var(--spacing-base); }
.pt-3 { padding-top: var(--spacing-base); }
.pr-3 { padding-right: var(--spacing-base); }
.pb-3 { padding-bottom: var(--spacing-base); }
.pl-3 { padding-left: var(--spacing-base); }

.p-4 { padding: var(--spacing-lg); }
.pt-4 { padding-top: var(--spacing-lg); }
.pr-4 { padding-right: var(--spacing-lg); }
.pb-4 { padding-bottom: var(--spacing-lg); }
.pl-4 { padding-left: var(--spacing-lg); }

/* 修复 Ant Design 3.x placeholder 显示问题 */
.ant-input::-webkit-input-placeholder {
  color: #bfbfbf !important;
  opacity: 1 !important;
}

.ant-input::-moz-placeholder {
  color: #bfbfbf !important;
  opacity: 1 !important;
}

.ant-input:-ms-input-placeholder {
  color: #bfbfbf !important;
  opacity: 1 !important;
}

.ant-input::placeholder {
  color: #bfbfbf !important;
  opacity: 1 !important;
}

/* 修复 Select 组件 placeholder */
.ant-select-selection__placeholder {
  color: #bfbfbf !important;
  opacity: 1 !important;
}

.ant-select-search__field__placeholder {
  color: #bfbfbf !important;
  opacity: 1 !important;
}

/* 修复 DatePicker 组件 placeholder */
.ant-calendar-picker-input::-webkit-input-placeholder {
  color: #bfbfbf !important;
  opacity: 1 !important;
}

.ant-calendar-picker-input::-moz-placeholder {
  color: #bfbfbf !important;
  opacity: 1 !important;
}

.ant-calendar-picker-input:-ms-input-placeholder {
  color: #bfbfbf !important;
  opacity: 1 !important;
}

.ant-calendar-picker-input::placeholder {
  color: #bfbfbf !important;
  opacity: 1 !important;
}

/* 修复 TimePicker 组件 placeholder */
.ant-time-picker-input::-webkit-input-placeholder {
  color: #bfbfbf !important;
  opacity: 1 !important;
}

.ant-time-picker-input::-moz-placeholder {
  color: #bfbfbf !important;
  opacity: 1 !important;
}

.ant-time-picker-input:-ms-input-placeholder {
  color: #bfbfbf !important;
  opacity: 1 !important;
}

.ant-time-picker-input::placeholder {
  color: #bfbfbf !important;
  opacity: 1 !important;
}

/* 修复 TextArea 组件 placeholder */
.ant-input::-webkit-input-placeholder {
  color: #bfbfbf !important;
  opacity: 1 !important;
}

/* 确保 placeholder 在聚焦状态下也能正常显示 */
.ant-input:focus::-webkit-input-placeholder {
  color: #d9d9d9 !important;
  opacity: 1 !important;
}

.ant-input:focus::-moz-placeholder {
  color: #d9d9d9 !important;
  opacity: 1 !important;
}

.ant-input:focus:-ms-input-placeholder {
  color: #d9d9d9 !important;
  opacity: 1 !important;
}

.ant-input:focus::placeholder {
  color: #d9d9d9 !important;
  opacity: 1 !important;
}

/* 修复表单项中的 placeholder 显示 */
.ant-form-item .ant-input::-webkit-input-placeholder,
.ant-form-item .ant-select-selection__placeholder,
.ant-form-item .ant-calendar-picker-input::-webkit-input-placeholder,
.ant-form-item .ant-time-picker-input::-webkit-input-placeholder {
  color: #bfbfbf !important;
  opacity: 1 !important;
  font-style: normal !important;
}

/* 确保在不同浏览器中 placeholder 都能正常显示 */
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #bfbfbf !important;
  opacity: 1 !important;
}

input::-moz-placeholder,
textarea::-moz-placeholder {
  color: #bfbfbf !important;
  opacity: 1 !important;
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: #bfbfbf !important;
  opacity: 1 !important;
}

input::placeholder,
textarea::placeholder {
  color: #bfbfbf !important;
  opacity: 1 !important;
}
