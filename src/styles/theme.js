// 全局主题配置
export const theme = {
  // 主色调
  colors: {
    primary: '#1890ff',
    primaryLight: '#40a9ff',
    primaryDark: '#096dd9',

    // 功能色彩
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    info: '#1890ff',

    // 中性色
    text: {
      primary: '#262626',
      secondary: '#595959',
      tertiary: '#8c8c8c',
      disabled: '#bfbfbf',
    },

    // 背景色
    background: {
      primary: '#ffffff',
      secondary: '#fafafa',
      tertiary: '#f5f5f5',
      dark: '#001529',
    },

    // 边框色
    border: {
      light: '#f0f0f0',
      base: '#d9d9d9',
      dark: '#434343',
    },

    // 渐变色
    gradients: {
      primary: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      success: 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)',
      warning: 'linear-gradient(135deg, #faad14 0%, #d48806 100%)',
      error: 'linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%)',
      info: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
    },
  },

  // 字体系统
  typography: {
    fontFamily: {
      base: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
      mono: 'SFMono-Regular, Consolas, "Liberation Mono", Menlo, Courier, monospace',
    },
    fontSize: {
      xs: '12px',
      sm: '14px',
      base: '16px',
      lg: '18px',
      xl: '20px',
      '2xl': '24px',
      '3xl': '30px',
      '4xl': '36px',
    },
    fontWeight: {
      light: 300,
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
    lineHeight: {
      tight: 1.25,
      normal: 1.5,
      relaxed: 1.75,
    },
  },

  // 间距系统
  spacing: {
    xs: '4px',
    sm: '8px',
    base: '16px',
    lg: '24px',
    xl: '32px',
    '2xl': '48px',
    '3xl': '64px',
  },

  // 圆角系统
  borderRadius: {
    sm: '4px',
    base: '8px',
    lg: '12px',
    xl: '16px',
    full: '50%',
  },

  // 阴影系统
  shadows: {
    sm: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',
    base: '0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)',
    lg: '0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)',
    xl: '0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22)',
    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
  },

  // 动画系统
  animation: {
    duration: {
      fast: '150ms',
      base: '300ms',
      slow: '500ms',
    },
    easing: {
      ease: 'ease',
      easeIn: 'ease-in',
      easeOut: 'ease-out',
      easeInOut: 'ease-in-out',
    },
  },

  // 断点系统
  breakpoints: {
    xs: '480px',
    sm: '576px',
    md: '768px',
    lg: '992px',
    xl: '1200px',
    xxl: '1600px',
  },

  // Z-index 层级
  zIndex: {
    dropdown: 1000,
    sticky: 1020,
    fixed: 1030,
    modalBackdrop: 1040,
    modal: 1050,
    popover: 1060,
    tooltip: 1070,
  },
};

// 组件样式变量
export const componentStyles = {
  // 卡片样式
  card: {
    borderRadius: theme.borderRadius.lg,
    boxShadow: theme.shadows.base,
    border: `1px solid ${theme.colors.border.light}`,
    background: theme.colors.background.primary,
  },

  // 按钮样式
  button: {
    primary: {
      background: theme.colors.gradients.primary,
      borderRadius: theme.borderRadius.base,
      boxShadow: '0 4px 16px rgba(102, 126, 234, 0.3)',
      border: 'none',
      fontWeight: theme.typography.fontWeight.semibold,
    },
    secondary: {
      background: theme.colors.background.primary,
      border: `2px solid ${theme.colors.border.base}`,
      borderRadius: theme.borderRadius.base,
      fontWeight: theme.typography.fontWeight.medium,
    },
  },

  // 表单样式
  form: {
    input: {
      borderRadius: theme.borderRadius.base,
      border: `2px solid ${theme.colors.border.base}`,
      padding: '12px 16px',
      fontSize: theme.typography.fontSize.sm,
    },
    label: {
      fontWeight: theme.typography.fontWeight.semibold,
      color: theme.colors.text.primary,
      marginBottom: theme.spacing.sm,
    },
  },

  // 表格样式
  table: {
    headerBackground: theme.colors.background.secondary,
    borderColor: theme.colors.border.light,
    hoverBackground: theme.colors.background.tertiary,
  },

  // 导航样式
  navigation: {
    background: theme.colors.background.dark,
    activeBackground: theme.colors.primary,
    textColor: '#ffffff',
    activeTextColor: '#ffffff',
  },
};

export default theme;
