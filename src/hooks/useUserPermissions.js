import { useEffect } from 'react';
import { useStore } from '../store';
import { userApi } from '../services/api';

/**
 * 用户权限初始化 Hook
 * 负责获取和设置当前用户的权限信息
 */
export const useUserPermissions = () => {
  const { state, actions } = useStore();
  const { currentUser, permissions, currentUserPermissions } = state;

  useEffect(() => {
    const initializeUserPermissions = async () => {
      // 如果已经有用户信息，跳过
      if (currentUser) {
        console.log('👤 用户信息已存在:', currentUser.name);
        return;
      }

      // 如果还没有权限数据，等待权限数据加载完成
      if (!permissions || permissions.length === 0) {
        console.log('⏳ 等待权限数据加载...');
        return;
      }

      try {
        console.log('🔍 获取当前用户信息...');
        const [userResponse, userPermissionsResponse] = await Promise.all([
          userApi.getCurrentUser(),
          userApi.getCurrentUserPermissions(),
        ]);
        if (userResponse.success && userPermissionsResponse.success) {
          console.log('✅ 获取到当前用户:', userResponse.data);
          console.log('✅ 获取到当前用户权限:', userPermissionsResponse.data.permissions);
          console.log('✅ 获取到当前用户角色:', userPermissionsResponse.data.roles);

          // 确保用户数据包含完整的权限信息
          const userWithPermissions = {
            ...userResponse.data,
            permissions: userPermissionsResponse.data.permissions,
            roles: userPermissionsResponse.data.roles,
          };
          console.log('[ userWithPermissions ] >', userWithPermissions);
          actions.setCurrentUser(userWithPermissions);
        } else {
          console.warn('⚠️ 获取当前用户失败:', userResponse.message);
          // 创建默认管理员用户
          createDefaultAdminUser();
        }
      } catch (error) {
        console.error('❌ 获取当前用户失败:', error);
        // 创建默认管理员用户
        createDefaultAdminUser();
      }
    };

    const createDefaultAdminUser = () => {
      const defaultUser = {
        id: 'default-admin',
        name: '系统管理员',
        username: 'admin',
        email: '<EMAIL>',
        roles: [
          {
            id: 'admin-role',
            name: '系统管理员',
            code: 'admin',
            description: '拥有所有权限的系统管理员',
            permissions, // 使用所有可用权限
          },
        ],
      };

      console.log('🔧 创建默认管理员用户:', defaultUser);
      actions.setCurrentUser(defaultUser);
    };

    initializeUserPermissions();
  }, [currentUser, permissions, currentUserPermissions, actions]);

  return {
    currentUser,
    isLoading: !currentUser,
    hasPermissions: currentUser && currentUserPermissions && currentUserPermissions.length > 0,
  };
};

export default useUserPermissions;
