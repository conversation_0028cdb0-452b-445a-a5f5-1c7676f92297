import { useState, useCallback } from 'react';
import { message } from 'antd';
import authService from '../services/auth';
import { useStore } from '../store';

/**
 * 权限刷新 Hook
 * 提供实时权限检查和手动刷新功能
 */
export const usePermissionRefresh = () => {
  const [refreshing, setRefreshing] = useState(false);
  const { actions } = useStore();

  // 手动刷新权限
  const refreshPermissions = useCallback(async () => {
    setRefreshing(true);
    try {
      console.log('🔄 手动刷新用户权限...');
      
      const result = await authService.refreshUserPermissions();
      
      if (result.success) {
        // 更新Store中的用户信息
        actions.setCurrentUser(result.data);
        
        if (result.hasChanges) {
          message.success('权限已更新！检测到角色或权限变更');
        } else {
          message.info('权限已刷新，无变更');
        }
        
        return result;
      } else {
        message.error(result.message || '权限刷新失败');
        return result;
      }
    } catch (error) {
      console.error('权限刷新失败:', error);
      message.error('权限刷新失败');
      return { success: false, message: error.message };
    } finally {
      setRefreshing(false);
    }
  }, [actions]);

  // 实时权限检查（不使用缓存）
  const checkPermissionRealtime = useCallback(async (permission) => {
    try {
      return await authService.hasPermission(permission, false);
    } catch (error) {
      console.error('实时权限检查失败:', error);
      return false;
    }
  }, []);

  // 实时角色检查（不使用缓存）
  const checkRoleRealtime = useCallback(async (role) => {
    try {
      return await authService.hasRole(role, false);
    } catch (error) {
      console.error('实时角色检查失败:', error);
      return false;
    }
  }, []);

  // 批量权限检查
  const checkMultiplePermissions = useCallback(async (permissions, useCache = true) => {
    try {
      const results = {};
      
      for (const permission of permissions) {
        results[permission] = await authService.hasPermission(permission, useCache);
      }
      
      return results;
    } catch (error) {
      console.error('批量权限检查失败:', error);
      return {};
    }
  }, []);

  return {
    refreshing,
    refreshPermissions,
    checkPermissionRealtime,
    checkRoleRealtime,
    checkMultiplePermissions,
  };
};

/**
 * 自动权限刷新 Hook
 * 在组件挂载时自动检查权限是否有更新
 */
export const useAutoPermissionRefresh = (interval = 60000) => {
  const { refreshPermissions } = usePermissionRefresh();

  // 可以在这里添加定时刷新逻辑
  // useEffect(() => {
  //   const timer = setInterval(() => {
  //     refreshPermissions();
  //   }, interval);
  //   
  //   return () => clearInterval(timer);
  // }, [refreshPermissions, interval]);

  return { refreshPermissions };
};

export default usePermissionRefresh;
