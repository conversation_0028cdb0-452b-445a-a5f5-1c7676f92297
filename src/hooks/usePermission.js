import { useMemo } from 'react';
import { useStore } from '../store';

/**
 * 权限控制 Hook
 * 提供权限检查、页面访问控制等功能
 */
export const usePermission = () => {
  const { state } = useStore();
  const { currentUser, currentUserPermissions } = state;
  // console.log('🚀获取到的当前用户[ currentUser ] >', currentUser);
  // console.log('🚀获取到的当前用户权限[ currentUserPermissions ] >', currentUserPermissions);

  // 获取当前用户的所有权限
  const userPermissions = useMemo(() => {
    if (!currentUserPermissions) {
      return [];
    }

    // 去重
    const uniquePermissions = currentUserPermissions.filter((permission, index, self) =>
      index === self.findIndex((p) => p.id === permission.id));

    return uniquePermissions;
  }, [currentUserPermissions]);

  // 检查是否有指定权限
  const hasPermission = (permissionName) => {
    if (!permissionName) return false;
    return userPermissions.some((permission) => permission.name === permissionName);
  };

  // 检查是否有任一权限
  const hasAnyPermission = (permissionNames) => {
    if (!permissionNames || permissionNames.length === 0) return false;
    return permissionNames.some((name) => hasPermission(name));
  };

  // 检查是否有所有权限
  const hasAllPermissions = (permissionNames) => {
    if (!permissionNames || permissionNames.length === 0) return false;
    return permissionNames.every((name) => hasPermission(name));
  };

  // 检查模块权限
  const hasModulePermission = (module, action = 'read') => {
    const permissionName = `${module}.${action}`;
    return hasPermission(permissionName);
  };

  // 检查是否为超级管理员
  const isSuperAdmin = useMemo(() => {
    if (!currentUser || !currentUser.roles) return false;
    console.log('[ currentUser.roles.some((role) => >', currentUser.roles.some((role) =>
      role.code === 'admin' || role.code === 'super_admin'));
    return currentUser.roles.some((role) =>
      role.code === 'admin' || role.code === 'super_admin');
  }, [currentUser]);

  // 获取用户权限列表
  const getPermissionNames = () => {
    return userPermissions.map((permission) => permission.name);
  };

  // 获取用户模块权限
  const getModulePermissions = (module) => {
    return userPermissions
      .filter((permission) => permission.module === module)
      .map((permission) => permission.action);
  };

  return {
    // 权限数据
    userPermissions,
    isSuperAdmin,

    // 权限检查方法
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasModulePermission,

    // 工具方法
    getPermissionNames,
    getModulePermissions,
  };
};

/**
 * 页面权限控制 Hook
 * 用于页面级别的权限控制
 */
export const usePagePermission = (requiredPermissions = []) => {
  const { hasAnyPermission, isSuperAdmin } = usePermission();

  // 检查页面访问权限
  const hasPageAccess = useMemo(() => {
    // 超级管理员有所有权限
    if (isSuperAdmin) return true;

    // 如果没有指定权限要求，则允许访问
    if (!requiredPermissions || requiredPermissions.length === 0) return true;

    // 检查是否有任一所需权限
    return hasAnyPermission(requiredPermissions);
  }, [hasAnyPermission, isSuperAdmin, requiredPermissions]);

  return {
    hasPageAccess,
    isSuperAdmin,
  };
};

/**
 * 功能权限控制 Hook
 * 用于具体功能的权限控制
 */
export const useFeaturePermission = (module) => {
  const { hasModulePermission, isSuperAdmin } = usePermission();
  const permissions = useMemo(() => {
    if (!module) {
      return {
        canRead: false,
        canCreate: false,
        canUpdate: false,
        canDelete: false,
        canApprove: false,
        canExport: false,
        canAssign: false,
        canConfig: false,
      };
    }

    return {
      canRead: isSuperAdmin || hasModulePermission(module, 'read'),
      canCreate: isSuperAdmin || hasModulePermission(module, 'create'),
      canUpdate: isSuperAdmin || hasModulePermission(module, 'update'),
      canDelete: isSuperAdmin || hasModulePermission(module, 'delete'),
      canApprove: isSuperAdmin || hasModulePermission(module, 'approve'),
      canExport: isSuperAdmin || hasModulePermission(module, 'export'),
      canAssign: isSuperAdmin || hasModulePermission(module, 'assign'),
      canConfig: isSuperAdmin || hasModulePermission(module, 'config'),
    };
  }, [module, isSuperAdmin, hasModulePermission]);

  return {
    ...permissions,
    isSuperAdmin,
  };
};

export default usePermission;
