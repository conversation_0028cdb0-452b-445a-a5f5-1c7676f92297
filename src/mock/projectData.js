// 项目管理模拟数据

// 品牌库数据
export const BRAND_LIST = [
  { id: 1, name: '品牌A', code: 'BRAND_A', status: 'active', createTime: '2024-01-01' },
  { id: 2, name: '品牌B', code: 'BRAND_B', status: 'active', createTime: '2024-01-02' },
  { id: 3, name: '品牌C', code: 'BRAND_C', status: 'active', createTime: '2024-01-03' },
  { id: 4, name: '品牌D', code: 'BRAND_D', status: 'inactive', createTime: '2024-01-04' },
  { id: 5, name: '品牌E', code: 'BRAND_E', status: 'active', createTime: '2024-01-05' },
];

// 用户数据（用于PM和内容媒介选择）
export const USER_LIST = [
  { id: 1, name: '张三', userId: 'zhang<PERSON>', department: '市场部', avatar: null },
  { id: 2, name: '李四', userId: 'lisi', department: '运营部', avatar: null },
  { id: 3, name: '王五', userId: 'wangwu', department: '内容部', avatar: null },
  { id: 4, name: '赵六', userId: 'zhaoliu', department: '媒介部', avatar: null },
  { id: 5, name: '钱七', userId: 'qianqi', department: '项目部', avatar: null },
  { id: 6, name: '孙八', userId: 'sunba', department: '内容部', avatar: null },
  { id: 7, name: '周九', userId: 'zhoujiu', department: '媒介部', avatar: null },
];

// 项目数据
export const PROJECT_LIST = [
  {
    id: 1,
    documentType: 'project_initiation',
    brand: 1,
    executionPeriod: ['2024-01-01', '2024-03-31'],
    projectName: '春季营销推广项目',
    planningBudget: 1000000,
    talentBudget: 300000,
    adBudget: 400000,
    otherBudget: 50000,
    estimatedTalentRebate: 30000,
    talentCost: 280000,
    adCost: 380000,
    otherCost: 45000,
    projectProfit: 325000,
    grossMargin: 32.5,
    executivePM: 1,
    contentMedia: [3, 6],
    contractType: 'quarterly',
    settlementRules: '<p>按季度结算，每月预付70%，季度末结清余款</p>',
    kpi: '<p>目标曝光量：1000万<br/>目标转化率：3%<br/>目标ROI：1:4</p>',
    attachments: [],
    status: 'executing',
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-15 14:30:00',
  },
  {
    id: 2,
    documentType: 'project_initiation',
    brand: 2,
    executionPeriod: ['2024-02-01', '2024-02-29'],
    projectName: '情人节特别活动',
    planningBudget: 500000,
    talentBudget: 150000,
    adBudget: 200000,
    otherBudget: 30000,
    estimatedTalentRebate: 15000,
    talentCost: 140000,
    adCost: 190000,
    otherCost: 25000,
    projectProfit: 160000,
    grossMargin: 32.0,
    executivePM: 2,
    contentMedia: [4, 7],
    contractType: 'single',
    settlementRules: '<p>一次性结算，项目完成后7个工作日内付款</p>',
    kpi: '<p>目标销售额：200万<br/>目标新客户：5000人<br/>目标复购率：25%</p>',
    attachments: [],
    status: 'completed',
    createTime: '2024-01-20 09:00:00',
    updateTime: '2024-03-01 16:00:00',
  },
  {
    id: 3,
    documentType: 'project_initiation',
    brand: 3,
    executionPeriod: ['2024-03-01', '2024-12-31'],
    projectName: '年度品牌建设项目',
    planningBudget: 2000000,
    talentBudget: 600000,
    adBudget: 800000,
    otherBudget: 100000,
    estimatedTalentRebate: 60000,
    talentCost: 580000,
    adCost: 750000,
    otherCost: 90000,
    projectProfit: 640000,
    grossMargin: 32.0,
    executivePM: 5,
    contentMedia: [3, 4, 6, 7],
    contractType: 'annual',
    settlementRules: '<p>按月结算，每月25日前结清当月费用</p>',
    kpi: '<p>品牌知名度提升：20%<br/>市场份额增长：15%<br/>年度销售目标：5000万</p>',
    attachments: [],
    status: 'approved',
    createTime: '2024-02-15 11:00:00',
    updateTime: '2024-02-20 10:30:00',
  },
];

// 获取品牌选项（只返回激活状态的品牌）
export const getBrandOptions = () => {
  return BRAND_LIST.filter((brand) => brand.status === 'active').map((brand) => ({
    value: brand.id,
    label: brand.name,
  }));
};

// 获取用户选项
export const getUserOptions = () => {
  return USER_LIST.map((user) => ({
    value: user.id,
    label: `${user.name}（${user.department}）`,
  }));
};

// 根据ID获取品牌名称
export const getBrandNameById = (brandId) => {
  const brand = BRAND_LIST.find((b) => b.id === brandId);
  return brand ? brand.name : '';
};

// 根据ID获取用户名称
export const getUserNameById = (userId) => {
  const user = USER_LIST.find((u) => u.id === userId);
  return user ? user.name : '';
};

// 根据ID数组获取用户名称列表
export const getUserNamesByIds = (userIds) => {
  if (!Array.isArray(userIds)) return [];
  return userIds.map((id) => getUserNameById(id)).filter((name) => name);
};
