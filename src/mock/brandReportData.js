// 品牌报表模拟数据

// 品牌汇总数据
export const BRAND_SUMMARY_DATA = {
  totalBrands: 8,
  totalProjects: 24,
  totalBudget: 5800000,
  totalCost: 4200000,
  totalProfit: 1600000,
  avgProfitMargin: 27.6,
  brandStats: [
    {
      rank: 1,
      brandId: 1,
      brandName: '品牌A',
      projectCount: 6,
      totalBudget: 1800000,
      totalCost: 1200000,
      totalProfit: 600000,
      profitMargin: 33.3,
      executionRate: 92,
    },
    {
      rank: 2,
      brandId: 2,
      brandName: '品牌B',
      projectCount: 4,
      totalBudget: 1200000,
      totalCost: 850000,
      totalProfit: 350000,
      profitMargin: 29.2,
      executionRate: 88,
    },
    {
      rank: 3,
      brandId: 3,
      brandName: '品牌C',
      projectCount: 5,
      totalBudget: 1500000,
      totalCost: 1100000,
      totalProfit: 400000,
      profitMargin: 26.7,
      executionRate: 85,
    },
    {
      rank: 4,
      brandId: 4,
      brandName: '品牌D',
      projectCount: 3,
      totalBudget: 800000,
      totalCost: 600000,
      totalProfit: 200000,
      profitMargin: 25.0,
      executionRate: 78,
    },
    {
      rank: 5,
      brandId: 5,
      brandName: '品牌E',
      projectCount: 6,
      totalBudget: 500000,
      totalCost: 450000,
      totalProfit: 50000,
      profitMargin: 10.0,
      executionRate: 65,
    },
  ],
  projectStatusStats: [
    { status: '执行中', count: 12 },
    { status: '已完成', count: 8 },
    { status: '已审批', count: 3 },
    { status: '待审批', count: 1 },
  ],
  monthlyTrend: [
    { month: '2024-01', budget: 800000, cost: 600000, profit: 200000 },
    { month: '2024-02', budget: 950000, cost: 720000, profit: 230000 },
    { month: '2024-03', budget: 1200000, cost: 880000, profit: 320000 },
    { month: '2024-04', budget: 1100000, cost: 800000, profit: 300000 },
    { month: '2024-05', budget: 1300000, cost: 950000, profit: 350000 },
    { month: '2024-06', budget: 1450000, cost: 1050000, profit: 400000 },
  ],
};

// 品牌执行数据
export const BRAND_EXECUTION_DATA = {
  1: {
    brandInfo: {
      id: 1,
      name: '品牌A',
      description: '知名消费品牌',
    },
    projectStats: {
      totalProjects: 6,
      totalBudget: 1800000,
      totalCost: 1200000,
      totalProfit: 600000,
      avgProfitMargin: 33.3,
    },
    projects: [
      {
        id: 1,
        projectName: '春季营销推广项目',
        executionPeriod: ['2024-03-01', '2024-05-31'],
        status: 'executing',
        planningBudget: 500000,
        projectProfit: 150000,
        grossMargin: 30.0,
        executionProgress: 75,
      },
      {
        id: 2,
        projectName: '夏季新品发布',
        executionPeriod: ['2024-06-01', '2024-08-31'],
        status: 'approved',
        planningBudget: 400000,
        projectProfit: 120000,
        grossMargin: 30.0,
        executionProgress: 0,
      },
      {
        id: 3,
        projectName: '双十一大促',
        executionPeriod: ['2024-10-01', '2024-11-30'],
        status: 'pending',
        planningBudget: 600000,
        projectProfit: 200000,
        grossMargin: 33.3,
        executionProgress: 0,
      },
      {
        id: 4,
        projectName: '年终回馈活动',
        executionPeriod: ['2024-12-01', '2024-12-31'],
        status: 'draft',
        planningBudget: 300000,
        projectProfit: 90000,
        grossMargin: 30.0,
        executionProgress: 0,
      },
    ],
    monthlyTrend: [
      { month: '2024-01', budget: 200000, cost: 140000, profit: 60000 },
      { month: '2024-02', budget: 250000, cost: 175000, profit: 75000 },
      { month: '2024-03', budget: 300000, cost: 210000, profit: 90000 },
      { month: '2024-04', budget: 280000, cost: 196000, profit: 84000 },
      { month: '2024-05', budget: 320000, cost: 224000, profit: 96000 },
      { month: '2024-06', budget: 350000, cost: 245000, profit: 105000 },
    ],
    budgetExecution: [
      { category: '达人预算', planned: 600000, actual: 580000, rate: 96.7 },
      { category: '投流预算', planned: 800000, actual: 750000, rate: 93.8 },
      { category: '其他预算', planned: 400000, actual: 370000, rate: 92.5 },
    ],
  },
  2: {
    brandInfo: {
      id: 2,
      name: '品牌B',
      description: '时尚生活品牌',
    },
    projectStats: {
      totalProjects: 4,
      totalBudget: 1200000,
      totalCost: 850000,
      totalProfit: 350000,
      avgProfitMargin: 29.2,
    },
    projects: [
      {
        id: 5,
        projectName: '时尚周联名活动',
        executionPeriod: ['2024-04-01', '2024-06-30'],
        status: 'executing',
        planningBudget: 400000,
        projectProfit: 120000,
        grossMargin: 30.0,
        executionProgress: 60,
      },
      {
        id: 6,
        projectName: '夏日清新系列',
        executionPeriod: ['2024-07-01', '2024-09-30'],
        status: 'approved',
        planningBudget: 350000,
        projectProfit: 105000,
        grossMargin: 30.0,
        executionProgress: 0,
      },
      {
        id: 7,
        projectName: '秋冬新品预热',
        executionPeriod: ['2024-09-01', '2024-11-30'],
        status: 'pending',
        planningBudget: 300000,
        projectProfit: 90000,
        grossMargin: 30.0,
        executionProgress: 0,
      },
      {
        id: 8,
        projectName: '年度品牌升级',
        executionPeriod: ['2024-01-01', '2024-12-31'],
        status: 'executing',
        planningBudget: 150000,
        projectProfit: 35000,
        grossMargin: 23.3,
        executionProgress: 45,
      },
    ],
    monthlyTrend: [
      { month: '2024-01', budget: 150000, cost: 110000, profit: 40000 },
      { month: '2024-02', budget: 180000, cost: 130000, profit: 50000 },
      { month: '2024-03', budget: 220000, cost: 160000, profit: 60000 },
      { month: '2024-04', budget: 200000, cost: 145000, profit: 55000 },
      { month: '2024-05', budget: 240000, cost: 175000, profit: 65000 },
      { month: '2024-06', budget: 210000, cost: 150000, profit: 60000 },
    ],
    budgetExecution: [
      { category: '达人预算', planned: 400000, actual: 380000, rate: 95.0 },
      { category: '投流预算', planned: 500000, actual: 470000, rate: 94.0 },
      { category: '其他预算', planned: 300000, actual: 280000, rate: 93.3 },
    ],
  },
};

// 获取品牌汇总数据的模拟API
export const getMockBrandsSummary = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        data: BRAND_SUMMARY_DATA,
      });
    }, 500);
  });
};

// 获取品牌执行数据的模拟API
export const getMockBrandExecution = (brandId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const data = BRAND_EXECUTION_DATA[brandId];
      if (data) {
        resolve({
          success: true,
          data,
        });
      } else {
        resolve({
          success: false,
          message: '品牌数据不存在',
        });
      }
    }, 500);
  });
};

// 获取品牌统计数据的模拟API
export const getMockBrandStats = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        data: {
          totalBrands: BRAND_SUMMARY_DATA.totalBrands,
          activeBrands: BRAND_SUMMARY_DATA.totalBrands - 1,
          totalProjects: BRAND_SUMMARY_DATA.totalProjects,
          totalBudget: BRAND_SUMMARY_DATA.totalBudget,
          totalProfit: BRAND_SUMMARY_DATA.totalProfit,
          avgProfitMargin: BRAND_SUMMARY_DATA.avgProfitMargin,
        },
      });
    }, 300);
  });
};
