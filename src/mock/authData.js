// 模拟用户数据
export const mockUsers = [
  {
    id: 1,
    username: 'admin',
    password: '123456',
    name: '系统管理员',
    email: '<EMAIL>',
    avatar: null,
    roles: ['admin'],
    permissions: [
      'project:view',
      'project:create',
      'project:edit',
      'project:delete',
      'brand:view',
      'brand:create',
      'brand:edit',
      'brand:delete',
      'stats:view',
      'file:upload',
    ],
    status: 'active',
    createTime: '2024-01-01 00:00:00',
    lastLoginTime: '2024-06-11 10:00:00',
  },
  {
    id: 2,
    username: 'pm001',
    password: '123456',
    name: '项目经理张三',
    email: '<EMAIL>',
    avatar: null,
    roles: ['project_manager'],
    permissions: [
      'project:view',
      'project:create',
      'project:edit',
      'brand:view',
      'stats:view',
      'file:upload',
    ],
    status: 'active',
    createTime: '2024-01-02 00:00:00',
    lastLoginTime: '2024-06-11 09:30:00',
  },
  {
    id: 3,
    username: 'bm001',
    password: '123456',
    name: '品牌经理李四',
    email: '<EMAIL>',
    avatar: null,
    roles: ['brand_manager'],
    permissions: [
      'project:view',
      'brand:view',
      'brand:create',
      'brand:edit',
      'stats:view',
      'file:upload',
    ],
    status: 'active',
    createTime: '2024-01-03 00:00:00',
    lastLoginTime: '2024-06-11 08:45:00',
  },
  {
    id: 4,
    username: 'viewer001',
    password: '123456',
    name: '观察员王五',
    email: '<EMAIL>',
    avatar: null,
    roles: ['viewer'],
    permissions: [
      'project:view',
      'brand:view',
      'stats:view',
    ],
    status: 'active',
    createTime: '2024-01-04 00:00:00',
    lastLoginTime: '2024-06-11 08:00:00',
  },
];

// 模拟token存储
const mockTokens = new Map();

// 生成模拟token
export const generateMockToken = (userId) => {
  const token = `mock_token_${userId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  const expiresAt = Date.now() + 24 * 60 * 60 * 1000; // 24小时后过期

  mockTokens.set(token, {
    userId,
    expiresAt,
    createdAt: Date.now(),
  });

  return token;
};

// 验证token
export const validateMockToken = (token) => {
  const tokenData = mockTokens.get(token);

  if (!tokenData) {
    return null;
  }

  if (tokenData.expiresAt < Date.now()) {
    mockTokens.delete(token);
    return null;
  }

  return tokenData;
};

// 删除token
export const removeMockToken = (token) => {
  mockTokens.delete(token);
};

// 清理过期token
export const cleanupExpiredTokens = () => {
  const now = Date.now();
  for (const [token, tokenData] of mockTokens.entries()) {
    if (tokenData.expiresAt < now) {
      mockTokens.delete(token);
    }
  }
};

// 模拟登录API
export const mockLogin = async (credentials) => {
  // 模拟网络延迟
  await new Promise((resolve) => setTimeout(resolve, 500));

  const { username, password } = credentials;

  // 查找用户
  const user = mockUsers.find((u) => u.username === username);

  if (!user) {
    throw new Error('用户名不存在');
  }

  if (user.password !== password) {
    throw new Error('密码错误');
  }

  if (user.status !== 'active') {
    throw new Error('账号已被禁用');
  }

  // 生成token
  const token = generateMockToken(user.id);

  // 更新最后登录时间
  user.lastLoginTime = new Date().toISOString().replace('T', ' ').substr(0, 19);

  // 返回用户信息（不包含密码）
  const { password: _, ...userInfo } = user;

  return {
    success: true,
    data: {
      token,
      user: userInfo,
    },
    message: '登录成功',
  };
};

// 模拟获取用户信息API
export const mockGetUserInfo = async (token) => {
  // 模拟网络延迟
  await new Promise((resolve) => setTimeout(resolve, 200));

  const tokenData = validateMockToken(token);

  if (!tokenData) {
    throw new Error('Token无效或已过期');
  }

  const user = mockUsers.find((u) => u.id === tokenData.userId);

  if (!user) {
    throw new Error('用户不存在');
  }

  // 返回用户信息（不包含密码）
  const { password: _, ...userInfo } = user;

  return {
    success: true,
    data: userInfo,
    message: '获取用户信息成功',
  };
};

// 模拟刷新token API
export const mockRefreshToken = async (oldToken) => {
  // 模拟网络延迟
  await new Promise((resolve) => setTimeout(resolve, 300));

  const tokenData = validateMockToken(oldToken);

  if (!tokenData) {
    throw new Error('Token无效或已过期');
  }

  // 删除旧token
  removeMockToken(oldToken);

  // 生成新token
  const newToken = generateMockToken(tokenData.userId);

  const user = mockUsers.find((u) => u.id === tokenData.userId);
  const { password: _, ...userInfo } = user;

  return {
    success: true,
    data: {
      token: newToken,
      user: userInfo,
    },
    message: 'Token刷新成功',
  };
};

// 模拟登出API
export const mockLogout = async (token) => {
  // 模拟网络延迟
  await new Promise((resolve) => setTimeout(resolve, 200));

  if (token) {
    removeMockToken(token);
  }

  return {
    success: true,
    data: null,
    message: '登出成功',
  };
};

// 模拟钉钉免登API
export const mockDingTalkLogin = async () => {
  // 模拟网络延迟
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // 模拟钉钉用户（默认使用admin用户）
  const user = mockUsers[0]; // 使用admin用户作为默认钉钉用户

  if (user.status !== 'active') {
    throw new Error('账号已被禁用');
  }

  // 生成token
  const accessToken = generateMockToken(user.id);
  const refreshToken = generateMockToken(`${user.id }_refresh`);

  // 更新最后登录时间
  user.lastLoginTime = new Date().toISOString().replace('T', ' ').substr(0, 19);

  // 返回用户信息（不包含密码）
  const { password: _, ...userInfo } = user;

  return {
    success: true,
    data: {
      accessToken,
      refreshToken,
      user: {
        ...userInfo,
        // 添加钉钉特有的字段
        userid: 'ding_user_001',
        dingUserId: 'ding_user_001',
        dingUnionId: 'ding_union_001',
        mobile: '13800138000',
        jobNumber: 'EMP001',
        department: '技术部',
        position: '系统管理员',
        deptIds: ['1', '2'],
        isBoss: false,
        isAdmin: true,
      },
    },
    message: '钉钉免登成功',
  };
};

// 定期清理过期token
setInterval(cleanupExpiredTokens, 60 * 60 * 1000); // 每小时清理一次
