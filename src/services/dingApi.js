// 钉钉API服务 - 独立文件，避免循环引用
import config from '../config.js';

const { host } = config;
const API_BASE_URL = `${host}/api`;

// 基础请求方法（不依赖 authService 避免循环引用）
const baseFetch = async (url, options = {}) => {
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
    },
  };

  const finalOptions = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  };

  try {
    const response = await fetch(`${API_BASE_URL}${url}`, finalOptions);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || `HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error('DingTalk API request failed:', error);
    throw error;
  }
};

// 钉钉相关API
export const dingApi = {
  // 获取钉钉进程实例空间
  getProcessInstanceCspaceSpace: (accessToken) => {
    const headers = {};
    if (accessToken) {
      headers.Authorization = `Bearer ${accessToken}`;
    }

    return baseFetch('/app/dingtalk/processinstance/cspace/space', {
      method: 'GET',
      headers,
    });
  },

  // 获取钉钉JSAPI签名（初始化时不需要认证）
  getJsapiSignature: (accessToken) => {
    const headers = {};
    if (accessToken) {
      headers.Authorization = `Bearer ${accessToken}`;
    }

    return baseFetch(`/app/jsapi-signature/enhanced?url=${encodeURIComponent(window.location.href)}`, {
    // return baseFetch(`/app/jsapi-signature/enhanced?url=${window.location.href}`, {
      method: 'GET',
      headers,
    });
  },
};

export default dingApi;
