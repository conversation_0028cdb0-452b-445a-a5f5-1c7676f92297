// 用户认证服务
import config from '../config.js';
import * as dd from 'dingtalk-jsapi';

const { host } = config;
const API_BASE_URL = `${host}/api`;

// Token存储键名
const ACCESS_TOKEN_KEY = 'accessToken';
const REFRESH_TOKEN_KEY = 'refreshToken';
const USER_INFO_KEY = 'userInfo';

// 认证服务类
class AuthService {
  constructor() {
    this.accessToken = this.getStoredAccessToken();
    this.refreshToken = this.getStoredRefreshToken();
    this.userInfo = this.getStoredUserInfo();
  }

  // 获取存储的accessToken
  getStoredAccessToken() {
    try {
      return localStorage.getItem(ACCESS_TOKEN_KEY);
    } catch (error) {
      console.error('Failed to get access token from localStorage:', error);
      return null;
    }
  }

  // 获取存储的refreshToken
  getStoredRefreshToken() {
    try {
      return localStorage.getItem(REFRESH_TOKEN_KEY);
    } catch (error) {
      console.error('Failed to get refresh token from localStorage:', error);
      return null;
    }
  }

  // 获取存储的用户信息
  getStoredUserInfo() {
    try {
      const userInfo = localStorage.getItem(USER_INFO_KEY);
      const parsedUserInfo = userInfo ? JSON.parse(userInfo) : null;

      console.log('📂 从 localStorage 获取用户信息:', parsedUserInfo);
      console.log('📂 存储的用户角色:', parsedUserInfo?.roles);
      console.log('📂 存储的用户权限:', parsedUserInfo?.permissions);

      return parsedUserInfo;
    } catch (error) {
      console.error('❌ 从 localStorage 获取用户信息失败:', error);
      return null;
    }
  }

  // 存储accessToken
  setAccessToken(accessToken) {
    try {
      this.accessToken = accessToken;
      localStorage.setItem(ACCESS_TOKEN_KEY, accessToken);
    } catch (error) {
      console.error('Failed to store access token:', error);
    }
  }

  // 存储refreshToken
  setRefreshToken(refreshToken) {
    try {
      this.refreshToken = refreshToken;
      localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken);
    } catch (error) {
      console.error('Failed to store refresh token:', error);
    }
  }

  // 存储token（兼容旧方法）
  setToken(token) {
    this.setAccessToken(token);
  }

  // 存储用户信息
  setUserInfo(userInfo) {
    try {
      console.log('💾 存储用户信息:', userInfo);
      console.log('💾 用户角色:', userInfo?.roles);
      console.log('💾 用户权限:', userInfo?.permissions);

      this.userInfo = userInfo;
      localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo));

      console.log('✅ 用户信息已存储到 localStorage');
    } catch (error) {
      console.error('❌ 存储用户信息失败:', error);
    }
  }

  // 清除认证信息
  clearAuth() {
    try {
      this.accessToken = null;
      this.refreshToken = null;
      this.userInfo = null;
      localStorage.removeItem(ACCESS_TOKEN_KEY);
      localStorage.removeItem(REFRESH_TOKEN_KEY);
      localStorage.removeItem(USER_INFO_KEY);

      // 清除其他可能的缓存数据
      // 可以根据需要添加更多清理逻辑
      console.log('认证信息已清除');
    } catch (error) {
      console.error('Failed to clear auth info:', error);
    }
  }

  // 强制重新认证（用于处理用户切换）
  async forceReauth() {
    console.log('🔄 执行强制重新认证');
    return this.checkExistingAuth(true);
  }

  // 调试方法：清除所有认证信息并重新登录
  async debugClearAndReauth() {
    console.log('🧹 调试：清除所有认证信息并重新登录');

    // 清除所有相关的 localStorage 项
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.includes('auth') || key.includes('user') || key.includes('token'))) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach((key) => {
      console.log('🗑️ 删除 localStorage 项:', key);
      localStorage.removeItem(key);
    });

    // 清除内存中的认证信息
    this.clearAuth();

    // 重新进行钉钉免登认证
    return this.initDingTalkAuth();
  }

  // 检查是否已登录
  isAuthenticated() {
    return !!this.accessToken;
  }

  // 获取当前accessToken
  getAccessToken() {
    return this.accessToken;
  }

  // 获取当前refreshToken
  getRefreshToken() {
    return this.refreshToken;
  }

  // 获取当前token（兼容旧方法）
  getToken() {
    return this.accessToken;
  }

  // 获取当前用户信息
  getUserInfo() {
    console.log('📖 获取用户信息:', this.userInfo);
    console.log('📖 用户角色:', this.userInfo?.roles);
    console.log('📖 用户权限:', this.userInfo?.permissions);
    return this.userInfo;
  }

  // 获取最新的用户信息（每次都从后台获取）
  async getLatestUserInfo() {
    try {
      if (!this.accessToken) {
        console.warn('⚠️ 没有访问令牌，无法获取最新用户信息');
        return this.userInfo;
      }

      const response = await fetch(`${API_BASE_URL}/auth/me`, {
        headers: {
          Authorization: `Bearer ${this.accessToken}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          console.log('✅ 获取到最新用户信息:', data.data);
          this.userInfo = data.data;
          return data.data;
        }
      }

      console.warn('⚠️ 获取最新用户信息失败，使用缓存信息');
      return this.userInfo;
    } catch (error) {
      console.error('❌ 获取最新用户信息异常:', error);
      return this.userInfo;
    }
  }

  // 检查现有认证状态
  async checkExistingAuth(forceReauth = false) {
    const savedAccessToken = this.getStoredAccessToken();
    const savedRefreshToken = this.getStoredRefreshToken();

    // 如果强制重新认证，直接清除缓存并重新认证
    if (forceReauth) {
      console.log('强制重新认证，清除已保存的认证信息');
      this.clearAuth();
      return this.initDingTalkAuth();
    }

    if (savedAccessToken) {
      this.accessToken = savedAccessToken;
      this.refreshToken = savedRefreshToken;

      console.log('发现已保存的token，验证并获取最新用户信息...');

      // 验证token是否仍然有效，并获取最新的用户信息
      try {
        const response = await fetch(`${API_BASE_URL}/auth/me`, {
          headers: {
            Authorization: `Bearer ${this.accessToken}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            console.log('✅ Token有效，获取到最新用户信息:', data.data);
            console.log('👤 用户角色:', data.data.roles);
            console.log('🔐 用户权限:', data.data.permissions);

            // 确保用户信息包含必要的字段
            if (!data.data.roles) {
              console.warn('⚠️ 用户信息中缺少 roles 字段');
            }
            if (!data.data.permissions) {
              console.warn('⚠️ 用户信息中缺少 permissions 字段');
            }

            // 直接使用从后台获取的最新用户信息，不依赖本地缓存
            this.userInfo = data.data;
            // 只保存基本的用户信息到localStorage，权限信息每次都从后台获取
            this.setUserInfo(data.data);

            return { success: true, data: { user: data.data } };
          }
        }
      } catch (error) {
        console.log('验证token或获取用户信息失败:', error);
      }

      // 如果验证失败，清除保存的信息
      this.clearAuth();
    }

    // 开始钉钉免登认证
    return this.initDingTalkAuth();
  }

  // 获取当前钉钉用户信息（用于检测用户切换）
  async getCurrentDingTalkUser() {
    try {
      // 检查是否在钉钉环境中
      if (typeof dd === 'undefined') {
        console.log('不在钉钉环境中，无法获取钉钉用户信息');
        return null;
      }

      return new Promise((resolve) => {
        dd.ready(() => {
          // 使用 dd.device.base.getUUID 获取设备唯一标识
          // 或者尝试获取当前登录用户的基本信息
          dd.biz.user.get({
            onSuccess: (result) => {
              console.log('获取当前钉钉用户信息成功:', result);
              resolve(result);
            },
            onFail: (err) => {
              console.log('获取当前钉钉用户信息失败，尝试其他方法:', err);

              // 如果 dd.biz.user.get 失败，尝试通过免登码获取用户信息
              // 这种方法可能更可靠，但会产生额外的网络请求
              dd.runtime.permission.requestAuthCode({
                corpId: process.env.NODE_ENV === 'production' ? 'ding660cccf3aa1024874ac5d6980864d335' : 'dinge21dd1a7d6663db3a39a90f97fcb1e09',
                onSuccess: (authResult) => {
                  console.log('通过免登码获取用户标识成功:', authResult);
                  // 这里我们只需要 authCode 来标识当前用户，不需要完整认证
                  resolve({ authCode: authResult.code, userid: `temp_${authResult.code.substring(0, 10)}` });
                },
                onFail: (authErr) => {
                  console.log('通过免登码获取用户标识也失败:', authErr);
                  resolve(null);
                },
              });
            },
          });
        });

        dd.error((err) => {
          console.log('钉钉环境初始化失败:', err);
          resolve(null);
        });
      });
    } catch (error) {
      console.log('获取钉钉用户信息异常:', error);
      return null;
    }
  }

  // 初始化钉钉免登认证
  async initDingTalkAuth() {
    // 检查是否在钉钉环境中
    if (typeof dd === 'undefined') {
      throw new Error('请在钉钉客户端中打开此页面');
    }

    // 钉钉环境检查
    return new Promise((resolve, reject) => {
      dd.ready(() => {
        console.log('钉钉环境准备就绪');

        // 获取免登码
        dd.runtime.permission.requestAuthCode({
          // 生产环境和开发环境
          corpId: process.env.NODE_ENV === 'production' ? 'ding660cccf3aa1024874ac5d6980864d335' : 'dinge21dd1a7d6663db3a39a90f97fcb1e09', // 使用您的企业corpId
          onSuccess: (result) => {
            console.log('获取免登码成功:', result);
            this.authenticateWithServer(result.code)
              .then(resolve)
              .catch(reject);
          },
          onFail: (err) => {
            console.error('获取免登码失败:', err);
            reject(new Error(`获取免登码失败: ${err.message}`));
          },
        });
      });

      dd.error((err) => {
        console.error('钉钉初始化失败:', err);
        reject(new Error(`钉钉初始化失败: ${err.message}`));
      });
    });
  }

  // 向服务器验证免登码
  async authenticateWithServer(code) {
    try {
      console.log('向服务器发送免登码:', code);

      // 调用后端API验证免登码并获取JWT token
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          authCode: code,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || '认证请求失败');
      }

      if (data.success) {
        // 保存JWT token
        this.accessToken = data.data.accessToken;
        this.refreshToken = data.data.refreshToken;
        this.userInfo = data.data.user;

        console.log('🎉 登录成功，用户信息:', this.userInfo);
        console.log('👤 用户角色:', this.userInfo.roles);
        console.log('🔐 用户权限:', this.userInfo.permissions);
        console.log('🔑 访问令牌已保存:', `${this.accessToken.substring(0, 20) }...`);

        // 检查关键字段是否存在
        if (!this.userInfo.roles) {
          console.error('❌ 登录响应中缺少用户角色信息');
        }
        if (!this.userInfo.permissions) {
          console.error('❌ 登录响应中缺少用户权限信息');
        }

        // 保存到localStorage
        this.setAccessToken(this.accessToken);
        this.setRefreshToken(this.refreshToken);
        this.setUserInfo(this.userInfo);

        return data;
      } else {
        throw new Error(data.message || '登录失败');
      }
    } catch (error) {
      console.error('服务器认证失败:', error);
      throw new Error(`服务器认证失败: ${ error.message}`);
    }
  }

  // 钉钉免登（主入口）
  async dingtalkLogin() {
    try {
      console.log('🔐 开始钉钉免登认证流程...');

      // 钉钉环境应该已经在 AppInitializer 中初始化完成
      // 这里直接进行认证检查
      return await this.checkExistingAuth();
    } catch (error) {
      console.error('❌ 钉钉免登失败:', error);
      throw error;
    }
  }

  // 传统登录（保留作为备用）
  async login(credentials) {
    try {
      // 使用真实API
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      if (data.success && data.data.accessToken) {
        this.setAccessToken(data.data.accessToken);
        this.setRefreshToken(data.data.refreshToken);
        this.setUserInfo(data.data.user);
        return data;
      } else {
        throw new Error(data.message || '登录失败');
      }
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  }

  // 登出
  async logout() {
    try {
      console.log('开始执行登出操作');

      // 如果有token，调用后端登出接口
      if (this.accessToken) {
        try {
          // 使用真实API
          await fetch(`${API_BASE_URL}/auth/logout`, {
            method: 'POST',
            headers: {
              Authorization: `Bearer ${this.accessToken}`,
              'Content-Type': 'application/json',
            },
          });
          console.log('后端登出接口调用成功');
        } catch (apiError) {
          console.error('后端登出接口调用失败:', apiError);
          // 继续执行本地清理，不因为API失败而中断
        }
      }

      // 清除本地存储
      this.clearAuth();

      // 清除可能的其他缓存
      try {
        // 清除所有可能相关的localStorage项
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (key.includes('auth') || key.includes('user') || key.includes('token'))) {
            keysToRemove.push(key);
          }
        }
        keysToRemove.forEach((key) => localStorage.removeItem(key));

        // 清除sessionStorage
        sessionStorage.clear();

        console.log('本地认证数据清除完成');
      } catch (storageError) {
        console.error('清除本地存储失败:', storageError);
      }

      // 重新加载页面以确保完全重置状态
      setTimeout(() => {
        window.location.reload();
      }, 100);
    } catch (error) {
      console.error('Logout failed:', error);
      // 即使出错也要清除本地认证信息
      this.clearAuth();
      window.location.reload();
    }
  }

  // 刷新token（兼容旧方法）
  async refreshToken() {
    const success = await this.refreshAccessToken();
    if (success) {
      return { success: true, data: { token: this.accessToken } };
    } else {
      this.clearAuth();
      throw new Error('Token刷新失败');
    }
  }

  // 刷新访问令牌
  async refreshAccessToken() {
    try {
      if (!this.refreshToken) {
        console.error('缺少刷新令牌');
        return false;
      }

      // 使用真实API
      const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refreshToken: this.refreshToken,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      if (data.success && data.data.accessToken) {
        this.accessToken = data.data.accessToken;
        this.setAccessToken(this.accessToken);
        console.log('访问令牌刷新成功');
        return true;
      } else {
        console.error('刷新令牌失败:', data.message);
        return false;
      }
    } catch (error) {
      console.error('刷新令牌异常:', error);
      return false;
    }
  }

  // 获取用户信息
  async fetchUserInfo() {
    try {
      if (!this.accessToken) {
        throw new Error('No token available');
      }

      // 使用真实API
      const response = await fetch(`${API_BASE_URL}/auth/me`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      if (data.success && data.data) {
        this.setUserInfo(data.data);
        return data;
      } else {
        throw new Error(data.message || '获取用户信息失败');
      }
    } catch (error) {
      console.error('Fetch user info failed:', error);
      throw error;
    }
  }

  // 检查权限（实时获取最新权限）
  async hasPermission(permission, useCache = true) {
    try {
      let { userInfo } = this;

      // 如果不使用缓存，则获取最新用户信息
      if (!useCache) {
        userInfo = await this.getLatestUserInfo();
      }

      if (!userInfo || !userInfo.permissions) {
        return false;
      }

      // 支持字符串权限和对象权限
      const permissions = userInfo.permissions.map((p) =>
        (typeof p === 'string' ? p : p.code || p.name));

      return permissions.includes(permission);
    } catch (error) {
      console.error('检查权限时出错:', error);
      return false;
    }
  }

  // 检查角色（实时获取最新角色）
  async hasRole(role, useCache = true) {
    try {
      let { userInfo } = this;

      // 如果不使用缓存，则获取最新用户信息
      if (!useCache) {
        userInfo = await this.getLatestUserInfo();
      }

      if (!userInfo || !userInfo.roles) {
        return false;
      }

      // 支持字符串角色和对象角色
      const roles = userInfo.roles.map((r) =>
        (typeof r === 'string' ? r : r.code || r.name));

      return roles.includes(role);
    } catch (error) {
      console.error('检查角色时出错:', error);
      return false;
    }
  }

  // 同步版本的权限检查（向后兼容）
  hasPermissionSync(permission) {
    if (!this.userInfo || !this.userInfo.permissions) {
      return false;
    }

    const permissions = this.userInfo.permissions.map((p) =>
      (typeof p === 'string' ? p : p.code || p.name));

    return permissions.includes(permission);
  }

  // 同步版本的角色检查（向后兼容）
  hasRoleSync(role) {
    if (!this.userInfo || !this.userInfo.roles) {
      return false;
    }

    const roles = this.userInfo.roles.map((r) =>
      (typeof r === 'string' ? r : r.code || r.name));

    return roles.includes(role);
  }

  // 刷新用户权限信息
  async refreshUserPermissions() {
    try {
      console.log('🔄 开始刷新用户权限信息...');

      if (!this.accessToken) {
        console.warn('⚠️ 没有访问令牌，无法刷新权限');
        return { success: false, message: '未登录' };
      }

      const response = await fetch(`${API_BASE_URL}/auth/me`, {
        headers: {
          Authorization: `Bearer ${this.accessToken}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          const oldUserInfo = { ...this.userInfo };

          // 更新用户信息
          this.setUserInfo(data.data);

          // 检查是否有权限变更
          const hasPermissionChanges = this.hasPermissionChanges(oldUserInfo, data.data);

          console.log('✅ 用户权限信息刷新成功');
          console.log('🔍 权限是否有变更:', hasPermissionChanges);

          return {
            success: true,
            data: data.data,
            hasChanges: hasPermissionChanges,
          };
        }
      }

      throw new Error('刷新权限失败');
    } catch (error) {
      console.error('❌ 刷新用户权限失败:', error);
      return { success: false, message: error.message };
    }
  }

  // 检查权限是否有变更
  hasPermissionChanges(oldUserInfo, newUserInfo) {
    if (!oldUserInfo || !newUserInfo) return true;

    // 比较角色
    const oldRoles = (oldUserInfo.roles || []).map((r) => (typeof r === 'string' ? r : r.code)).sort();
    const newRoles = (newUserInfo.roles || []).map((r) => (typeof r === 'string' ? r : r.code)).sort();

    if (JSON.stringify(oldRoles) !== JSON.stringify(newRoles)) {
      console.log('🔄 检测到角色变更:', { old: oldRoles, new: newRoles });
      return true;
    }

    // 比较权限
    const oldPermissions = (oldUserInfo.permissions || []).map((p) => (typeof p === 'string' ? p : p.code)).sort();
    const newPermissions = (newUserInfo.permissions || []).map((p) => (typeof p === 'string' ? p : p.code)).sort();

    if (JSON.stringify(oldPermissions) !== JSON.stringify(newPermissions)) {
      console.log('🔄 检测到权限变更:', { old: oldPermissions.length, new: newPermissions.length });
      return true;
    }

    return false;
  }
}

// 创建认证服务实例
const authService = new AuthService();

// 权限常量
export const PERMISSIONS = {
  // 项目权限
  PROJECT_VIEW: 'project:project:view',
  PROJECT_CREATE: 'project:project:create',
  PROJECT_EDIT: 'project:project:edit',
  PROJECT_DELETE: 'project:project:delete',
  PROJECT_MANAGE: 'project:project:manage',

  // 品牌权限
  BRAND_VIEW: 'brand:brand:view',
  BRAND_CREATE: 'brand:brand:create',
  BRAND_EDIT: 'brand:brand:edit',
  BRAND_DELETE: 'brand:brand:delete',
  BRAND_MANAGE: 'brand:brand:manage',

  // 供应商权限
  SUPPLIER_VIEW: 'supplier:supplier:view',
  SUPPLIER_CREATE: 'supplier:supplier:create',
  SUPPLIER_EDIT: 'supplier:supplier:edit',
  SUPPLIER_DELETE: 'supplier:supplier:delete',
  SUPPLIER_MANAGE: 'supplier:supplier:manage',

  // 用户权限
  USER_VIEW: 'user:user:view',
  USER_CREATE: 'user:user:create',
  USER_EDIT: 'user:user:edit',
  USER_DELETE: 'user:user:delete',
  USER_MANAGE: 'user:user:manage',

  // 角色权限
  ROLE_VIEW: 'role:role:view',
  ROLE_CREATE: 'role:role:create',
  ROLE_EDIT: 'role:role:edit',
  ROLE_DELETE: 'role:role:delete',
  ROLE_MANAGE: 'role:role:manage',

  // 权限管理权限
  PERMISSION_VIEW: 'permission:permission:view',
  PERMISSION_CREATE: 'permission:permission:create',
  PERMISSION_EDIT: 'permission:permission:edit',
  PERMISSION_DELETE: 'permission:permission:delete',
  PERMISSION_MANAGE: 'permission:permission:manage',

  // 收入权限
  REVENUE_VIEW: 'revenue:revenue:view',
  REVENUE_CREATE: 'revenue:revenue:create',
  REVENUE_EDIT: 'revenue:revenue:edit',
  REVENUE_DELETE: 'revenue:revenue:delete',
  REVENUE_CONFIRM: 'revenue:revenue:confirm',

  // 预算权限
  BUDGET_VIEW: 'budget:budget:view',
  BUDGET_CREATE: 'budget:budget:create',
  BUDGET_EDIT: 'budget:budget:edit',
  BUDGET_DELETE: 'budget:budget:delete',
  BUDGET_APPROVE: 'budget:budget:approve',

  // 报表权限
  REPORT_VIEW: 'report:report:view',
  REPORT_EXPORT: 'report:report:export',

  // 统计权限
  STATS_VIEW: 'stats:stats:view',

  // 文件权限
  FILE_UPLOAD: 'file:file:upload',
  FILE_DELETE: 'file:file:delete',

  // 系统权限
  SYSTEM_MANAGE: 'system:system:manage',
  SYSTEM_CONFIG: 'system:system:config',
};

// 角色常量
export const ROLES = {
  ADMIN: 'admin',
  PROJECT_MANAGER: 'project_manager',
  FINANCE_MANAGER: 'finance_manager',
  CONTENT_MANAGER: 'content_manager',
  BRAND_MANAGER: 'brand_manager',
  SUPPLIER_MANAGER: 'supplier_manager',
  OPERATOR: 'operator',
  VIEWER: 'viewer',
};

export default authService;
