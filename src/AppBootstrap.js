import React, { useState, useEffect } from 'react';
import { Spin, Alert } from 'antd';
// import { DEPARTMENT_CONFIG } from './config/departments';
import { departmentAPI, roleApi, permissionApi } from './services/api';

/**
 * 应用启动器
 * 在渲染主应用之前完成所有必需数据的初始化
 */
const AppBootstrap = ({ children, onDataReady }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [initData, setInitData] = useState(null);
  console.log('[ initData ] >', initData);

  useEffect(() => {
    const initializeAppData = async () => {
      try {
        console.log('🚀 开始应用数据初始化...');
        setLoading(true);
        setError(null);

        // 1. 初始化部门数据
        let departments = [];
        let departmentMap = {};

        try {
          const response = await departmentAPI.getDepartments();
          if (response.success && response.data) {
            departments = response.data;
            console.log('✅ 从API获取部门数据:', departments.length, '个部门');
          } else {
            throw new Error('API返回数据格式错误');
          }
        } catch (apiError) {
          console.warn('⚠️ API获取部门数据失败，使用配置文件数据:', apiError.message);
          departments = Object.values(DEPARTMENT_CONFIG);
          console.log('✅ 使用配置文件部门数据:', departments.length, '个部门');
        }

        departmentMap = departments.reduce((acc, dept) => {
          acc[dept.id] = dept;
          return acc;
        }, {});

        let defaultRoles = [];
        try {
          const response = roleApi.getRoles();
          if (response.success && response.data) {
            defaultRoles = response.data.roles;
            console.log('✅ 从API获取角色数据:', defaultRoles.length, '个角色');
          } else {
            throw new Error('API返回数据格式错误');
          }
        } catch (_error) {
          console.warn('⚠️ API获取角色数据失败，使用默认角色数据:', _error.message);
        }

        // 2. 初始化角色数据
        // const defaultRoles = [
        //   { id: 1, name: '系统管理员', code: 'admin', description: '系统管理员角色', status: 'active' },
        //   { id: 2, name: '项目经理', code: 'pm', description: '项目经理角色', status: 'active' },
        //   { id: 3, name: '执行人员', code: 'executor', description: '执行人员角色', status: 'active' },
        //   { id: 4, name: '财务人员', code: 'finance', description: '财务人员角色', status: 'active' },
        //   { id: 5, name: '普通用户', code: 'user', description: '普通用户角色', status: 'active' },
        // ];
        const roleMap = defaultRoles.reduce((acc, role) => {
          acc[role.id] = role;
          return acc;
        }, {});
        console.log('✅ 角色数据初始化完成:', defaultRoles.length, '个角色');

        // 3. 初始化权限数据
        let defaultPermissions = [];
        try {
          const response = await permissionApi.getPermissions({ pageSize: 1000 });
          if (response.success && response.data) {
            defaultPermissions = Array.isArray(response.data)
              ? response.data
              : response.data.permissions || [];
            console.log('✅ 从API获取权限数据:', defaultPermissions.length, '个权限');
          } else {
            throw new Error('API返回数据格式错误');
          }
        } catch (apiError) {
          console.warn('⚠️ API获取权限数据失败，使用默认权限数据:', apiError.message);
        }
        const permissionMap = defaultPermissions.reduce((acc, permission) => {
          acc[permission.id] = permission;
          return acc;
        }, {});
        console.log('✅ 权限数据初始化完成:', defaultPermissions.length, '个权限');

        // 4. 权限分类
        const permissionCategories = [
          { id: 'system', name: '系统管理', description: '系统相关权限' },
          { id: 'business', name: '业务管理', description: '业务相关权限' },
        ];

        // 5. 组装初始化数据
        const initializedData = {
          departments,
          departmentMap,
          roles: defaultRoles,
          roleMap,
          permissions: defaultPermissions,
          permissionMap,
          permissionCategories,
        };

        setInitData(initializedData);

        // 通知父组件数据已准备好
        if (onDataReady) {
          onDataReady(initializedData);
        }

        console.log('🎉 应用数据初始化完成！');
        setLoading(false);
      } catch (_error) {
        console.error('❌ 应用数据初始化失败:', _error);
        setError(_error.message);
        setLoading(false);
      }
    };

    initializeAppData();
  }, []); // 移除 onDataReady 依赖，避免死循环

  // 加载中状态
  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column',
      }}
      >
        <Spin size="large" />
        <div style={{ marginTop: 16, color: '#666' }}>
          正在初始化应用数据...
        </div>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        padding: '20px',
      }}
      >
        <Alert
          message="应用初始化失败"
          description={`初始化过程中发生错误: ${error}`}
          type="error"
          showIcon
          action={
            <button
              onClick={() => window.location.reload()}
              style={{
                border: 'none',
                background: '#ff4d4f',
                color: 'white',
                padding: '4px 12px',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              重新加载
            </button>
          }
        />
      </div>
    );
  }

  // 数据准备完成，渲染子组件
  return children;
};

export default AppBootstrap;
