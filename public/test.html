<!DOCTYPE html>
<html lang="zh">

<head>
  <meta charset="UTF-8">
  <title>单气泡流式输出示例</title>
  <style>
    body {
      font-family: sans-serif;
      background: #f4f4f4;
      margin: 0;
      padding: 20px;
    }

    #chat {
      max-width: 600px;
      margin: auto;
      background: #fff;
      padding: 10px;
      border-radius: 4px;
      height: 80vh;
      overflow-y: auto;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;
    }

    .bubble {
      display: inline-flex;
      flex-direction: column;
      margin: 8px 0;
      padding: 14px;
      border-radius: 18px;
      position: relative;
      opacity: 0;
      max-width: 100%;
    }

    .ai {
      background: #e0e0e0;
      align-self: flex-start;
    }

    .fade-in {
      animation: fadeIn 0.3s forwards;
    }

    @keyframes fadeIn {
      to {
        opacity: 1;
      }
    }

    .typing {
      border-right: .1em solid rgba(0, 0, 0, 0.3);
      white-space: pre-wrap;
      word-break: break-word;
      overflow: hidden;
    }

    .btn-group {
      margin-top: 8px;
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .btn-group button {
      padding: 6px 12px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }

    .content img {
      max-width: 200px;
      margin-top: 8px;
      border-radius: 4px;
    }
  </style>
</head>

<body>
  <div id="chat">
    <!-- 统一的 AI 气泡 -->
    <div id="aiBubble" class="bubble ai"></div>
  </div>

  <script>
    // 假设一次性拿到的 JSON
    const mockResponse = {
      segments: [
        { type: 'text', content: '你好，我是 AI 助手～' },
        { type: 'text', content: '我可以帮你做很多事情，比如：' },
        {
          type: 'button', buttons: [
            { text: '查询天气', payload: 'WEATHER' },
            { text: '生成海报', payload: 'POSTER' }
          ]
        },
        { type: 'image', src: 'https://via.placeholder.com/120' },
        { type: 'text', content: '很好，帮我查询明天的天气。' }
      ]
    };

    const bubble = document.getElementById('aiBubble');
    // 先让气泡淡入
    bubble.classList.add('fade-in');

    async function renderStream(segments) {
      for (const seg of segments) {
        if (seg.type === 'text') {
          await renderText(seg.content);
        }
        else if (seg.type === 'image') {
          await renderImage(seg.src);
        }
        else if (seg.type === 'button') {
          await renderButtons(seg.buttons);
        }
        // 每次添加完都滚到底
        chat.scrollTop = chat.scrollHeight;
        await new Promise(r => setTimeout(r, 200));
      }
    }

    // 文本打字机效果，渲染到同一个 bubble
    function renderText(text) {
      return new Promise(resolve => {
        const span = document.createElement('span');
        span.classList.add('typing');
        bubble.appendChild(span);
        let i = 0, speed = 40;
        function tick() {
          span.textContent = text.slice(0, i++);
          chat.scrollTop = chat.scrollHeight;
          if (i <= text.length) {
            setTimeout(tick, speed);
          } else {
            span.classList.remove('typing');
            bubble.appendChild(document.createElement('br'));
            resolve();
          }
        }
        tick();
      });
    }

    // 图片淡入
    function renderImage(src) {
      return new Promise(resolve => {
        const container = document.createElement('div');
        container.classList.add('content');
        const img = document.createElement('img');
        img.src = src;
        img.onload = () => {
          bubble.appendChild(container);
          container.appendChild(img);
          resolve();
        };
      });
    }

    // 按钮组
    function renderButtons(buttons) {
      return new Promise(resolve => {
        const group = document.createElement('div');
        group.classList.add('btn-group');
        buttons.forEach(b => {
          const btn = document.createElement('button');
          btn.textContent = b.text;
          btn.addEventListener('click', () => {
            // TODO: 这里可以把按钮当作用户输入，触发新一轮渲染
            console.log('用户点击：', b.payload);
          });
          group.appendChild(btn);
        });
        bubble.appendChild(group);
        resolve();
      });
    }

    // 启动渲染
    document.addEventListener('DOMContentLoaded', () => {
      renderStream(mockResponse.segments);
    });
  </script>
</body>

</html>