# 周期选择器功能实现总结

## 实现的功能

根据您的需求，我实现了两个主要功能：

### 1. 供应商选择时自动带入税率
- ✅ 当选择供应商时，如果供应商有首选税率，自动填入税率字段
- ✅ 在供应商选项中显示税率信息，格式：`供应商名称 (税率: special_6)`
- ✅ 自动填入时显示友好的提示信息

### 2. 周期显示为"几月第几周"
- ✅ 表单中的周期选择器显示格式：`3月第2周`
- ✅ 列表中的周期显示格式：`3月第2周`（主要显示）+ 日期范围（次要显示）
- ✅ 保持原有的日期选择功能不变

## 修改的文件

### 1. `src/components/ProjectManagement/WeeklyBudgetForm.js`
- 添加了 `handleSupplierChange` 方法处理供应商选择
- 更新了供应商选择器，添加 `onChange` 事件和税率显示
- 使用自定义的 `WeekPicker` 组件替换原生的 `DatePicker.WeekPicker`

### 2. `src/components/ProjectManagement/WeekPicker.js`
- 新建的自定义周期选择器组件
- 在选择周期后自动更新输入框显示为"几月第几周"格式
- 保持原有的日期选择功能和限制条件

### 3. `src/components/ProjectManagement/WeeklyBudgetTable.js`
- 更新周期列的显示格式
- 主要显示：`3月第2周`（加粗）
- 次要显示：具体日期范围（小字灰色）

### 4. `src/utils/weeklyBudgetUtils.js`
- 添加了 `getMonthWeekInfo` 函数，计算月内周数
- 添加了 `formatWeekPeriodDisplay` 函数，格式化周期显示

## 技术实现要点

### 月内周数计算
```javascript
export const getMonthWeekInfo = (date) => {
  const momentDate = moment(date);
  const year = momentDate.year();
  const month = momentDate.month() + 1;
  
  const firstDayOfMonth = moment(date).startOf('month');
  const weekOfMonth = Math.ceil((momentDate.date() + firstDayOfMonth.day()) / 7);
  
  return {
    year,
    month,
    weekOfMonth,
    monthName: momentDate.format('M月'),
    displayText: `${year}年${month}月第${weekOfMonth}周`,
  };
};
```

### 自定义显示格式
- 使用 React ref 获取输入框元素
- 在组件更新后手动设置输入框的显示值
- 保持原有的 moment 对象值不变，只改变显示

### 供应商税率自动填入
```javascript
handleSupplierChange = (supplierId) => {
  const { supplierOptions } = this.state;
  const selectedSupplier = supplierOptions.find(supplier => supplier.value === supplierId);
  
  if (selectedSupplier && selectedSupplier.preferredTaxRate) {
    this.props.form.setFieldsValue({
      taxRate: selectedSupplier.preferredTaxRate
    });
    message.info(`已自动填入供应商首选税率：${selectedSupplier.preferredTaxRate}`);
  }
};
```

## 用户体验改进

1. **更直观的周期显示**：`3月第2周` 比 `2024-03-11 至 2024-03-17` 更容易理解
2. **减少手动输入**：选择供应商时自动填入税率，提高效率
3. **保持功能完整性**：所有原有功能都保持不变
4. **友好的用户反馈**：操作时显示相应的提示信息

## 兼容性说明

- ✅ 保持与现有API完全兼容
- ✅ 不影响现有数据结构
- ✅ 可以直接替换使用，无需修改其他代码
- ✅ 支持编辑模式的数据回填

现在您可以测试这些功能了。周期选择器会显示"几月第几周"的格式，供应商选择时会自动带入税率。
