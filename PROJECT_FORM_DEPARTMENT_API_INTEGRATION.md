# ProjectForm部门用户API集成总结

## 🎯 功能概述

将ProjectForm组件的执行PM和内容媒介数据源从mock数据改为真实的部门用户API接口，实现动态获取部门人员信息。

## 🔧 实现的功能

### 1. API接口集成
- **部门用户API**：`/app/department/users`
- **执行PM数据**：从项目管理部门获取
- **内容媒介数据**：从内容媒介部门获取

### 2. 部门配置管理
- 创建部门配置文件 `src/config/departments.js`
- 统一管理部门ID和信息
- 便于后续维护和配置

### 3. 数据结构优化
- 分离PM和媒介人员数据
- 支持用户详细信息（头像、手机、邮箱）
- 优化选择器交互体验

## 📁 文件修改

### 1. 新增文件

#### `src/config/departments.js`
```javascript
export const DEPARTMENT_CONFIG = {
  // 项目管理部门
  PROJECT_MANAGEMENT: {
    id: 1,
    name: '项目管理部',
    description: '负责项目执行和管理的部门',
  },
  
  // 内容部门
  CONTENT_MEDIA: {
    id: 2,
    name: '内容媒介部',
    description: '负责内容创作和媒介投放的部门',
  },
  
  // 运营部门
  OPERATIONS: {
    id: 3,
    name: '运营部',
    description: '负责日常运营和数据分析的部门',
  },
  
  // 财务部门
  FINANCE: {
    id: 4,
    name: '财务部',
    description: '负责财务管理和预算控制的部门',
  },
};
```

### 2. 修改文件

#### `src/services/api.js`
```javascript
// 部门用户管理API
export const departmentAPI = {
  // 获取部门用户列表
  getDepartmentUsers: (params = {}) => {
    const queryParams = new URLSearchParams();
    if (params.deptId) queryParams.append('deptId', params.deptId);
    if (params.cursor) queryParams.append('cursor', params.cursor);
    if (params.size) queryParams.append('size', params.size);

    const queryString = queryParams.toString();
    return request(`/app/department/users${queryString ? `?${queryString}` : ''}`);
  },
};
```

#### `src/components/ProjectManagement/ProjectForm.js`
主要修改：
1. 导入部门API和配置
2. 修改状态结构
3. 重写loadOptions方法
4. 更新选择器组件

## 🛠 技术实现

### 1. 状态管理优化
```javascript
// 修改前
this.state = {
  userOptions: [],  // 通用用户选项
};

// 修改后
this.state = {
  pmOptions: [],     // 执行PM选项
  mediaOptions: [],  // 内容媒介选项
};
```

### 2. API调用实现
```javascript
loadOptions = async () => {
  try {
    // 加载品牌选项
    const brandResponse = await brandAPI.getBrands({ status: 'active' });
    let brandOptions = [];
    if (brandResponse.success) {
      brandOptions = brandResponse.data.brands.map((brand) => ({
        value: brand.id,
        label: brand.name,
      }));
    }

    // 加载执行PM选项（从项目管理部门获取）
    let pmOptions = [];
    const pmResponse = await departmentAPI.getDepartmentUsers({
      deptId: DEPARTMENT_CONFIG.PROJECT_MANAGEMENT.id,
      size: 100,
    });
    if (pmResponse.success) {
      console.log('[ pmResponse ] >', pmResponse)
      pmOptions = pmResponse.data.list.map((user) => ({
        value: user.userid,
        label: user.name,
        avatar: user.avatar,
        mobile: user.mobile,
        email: user.email,
      }));
    }

    // 加载内容媒介选项（从内容部门获取）
    let mediaOptions = [];
    const mediaResponse = await departmentAPI.getDepartmentUsers({
      deptId: DEPARTMENT_CONFIG.CONTENT_MEDIA.id,
      size: 100,
    });
    if (mediaResponse.success) {
      mediaOptions = mediaResponse.data.list.map((user) => ({
        value: user.userid,
        label: user.name,
        avatar: user.avatar,
        mobile: user.mobile,
        email: user.email,
      }));
    }

    this.setState({
      brandOptions,
      pmOptions,
      mediaOptions,
    });
  } catch (error) {
    console.error('Load options failed:', error);
    message.error('加载选项数据失败');
    this.setState({
      brandOptions: [],
      pmOptions: [],
      mediaOptions: [],
    });
  }
};
```

### 3. 选择器组件优化
```javascript
// 执行PM选择器
<Select placeholder="请选择执行PM" showSearch optionFilterProp="children">
  {pmOptions.map((user) => (
    <Option key={user.value} value={user.value} title={user.email || user.mobile}>
      {user.label}
    </Option>
  ))}
</Select>

// 内容媒介选择器
<Select 
  mode="multiple" 
  placeholder="请选择内容媒介" 
  showSearch 
  optionFilterProp="children"
>
  {mediaOptions.map((user) => (
    <Option key={user.value} value={user.value} title={user.email || user.mobile}>
      {user.label}
    </Option>
  ))}
</Select>
```

## 📊 API接口规范

### 请求参数
```javascript
{
  deptId: number,    // 必需：部门ID
  cursor: number,    // 可选：分页游标，默认0
  size: number       // 可选：每页数量，默认100，最大100
}
```

### 响应数据结构
```javascript
{
  success: boolean,
  data: {
    list: [
      {
        userid: string,    // 用户ID
        name: string,      // 用户姓名
        avatar: string,    // 头像URL
        mobile: string,    // 手机号
        email: string      // 邮箱
      }
    ],
    has_more: boolean,     // 是否有更多数据
    next_cursor: number    // 下一页游标
  },
  message: string
}
```

## 🎯 部门映射

### 当前配置
- **项目管理部门**：ID = 1，用于获取执行PM人员
- **内容媒介部门**：ID = 2，用于获取内容媒介人员

### 扩展配置
- **运营部门**：ID = 3，预留用于运营相关功能
- **财务部门**：ID = 4，预留用于财务相关功能

## ✅ 实现效果

### 1. 数据来源
- ✅ 完全使用真实API接口
- ✅ 移除所有mock数据
- ✅ 支持动态部门配置

### 2. 用户体验
- ✅ 执行PM和内容媒介分别从对应部门获取
- ✅ 选择器支持搜索功能
- ✅ 显示用户详细信息（邮箱/手机）

### 3. 可维护性
- ✅ 部门配置集中管理
- ✅ API调用统一处理
- ✅ 错误处理完善

### 4. 扩展性
- ✅ 易于添加新部门
- ✅ 支持部门配置修改
- ✅ 便于功能扩展

## 🔄 使用流程

### 1. 组件加载
1. 组件挂载时调用 `loadOptions()`
2. 并行请求品牌、PM、媒介数据
3. 数据转换为选择器格式
4. 更新组件状态

### 2. 用户选择
1. 用户在执行PM下拉框中选择项目管理部门人员
2. 用户在内容媒介下拉框中选择内容部门人员
3. 支持搜索和多选功能

### 3. 数据提交
1. 表单提交时包含选中的用户ID
2. 数据通过 `dataTransform.projectToAPI()` 转换
3. 发送到后端API保存

## 🚀 后续优化建议

### 1. 性能优化
- 考虑添加部门用户数据缓存
- 实现增量加载大量用户数据
- 添加用户搜索防抖功能

### 2. 功能增强
- 支持用户头像显示
- 添加用户在线状态
- 支持用户详情查看

### 3. 配置优化
- 支持动态部门配置
- 添加部门权限控制
- 实现部门层级管理

通过这次集成，ProjectForm组件现在完全使用真实的部门用户API，为项目管理提供了准确的人员数据支持。
